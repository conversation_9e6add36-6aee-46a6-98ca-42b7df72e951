﻿using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper.Mappers.NotesMappers
{
    public class NoteOtherMapper : Profile
    {
        public NoteOtherMapper()
        {
            CreateMap<NotesResponseDataMessage, List<NoteOther>>()
                .ConvertUsing<NotesDtoToListOfNoteOtherConverter>();
        }

        public class NotesDtoToListOfNoteOtherConverter : ITypeConverter<NotesResponseDataMessage, List<NoteOther>>
        {
            public List<NoteOther> Convert(NotesResponseDataMessage? source, List<NoteOther> destination, ResolutionContext context)
            {
                var listOfNotes = new List<NoteOther>();

                if (source?.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended != null)
                {
                    listOfNotes.AddRange(AdvancesCreditAndGuaranteesGrantedToDirectorsSection1aConvertion(
                        source.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended));
                }

                listOfNotes.PopulateWhenNotePresent("OffBalanceSheetArrangements", source?.OffBalanceSheetArrangements);
                listOfNotes.PopulateWhenNotePresent("AdvancesCreditAndGuaranteesGrantedToDirectors", source?.AdvancesCreditAndGuaranteesGrantedToDirectors);
                listOfNotes.PopulateWhenNotePresent("GuaranteesAndOtherFinancialCommitments", source?.GuaranteesAndOtherFinancialCommitments);
                listOfNotes.PopulateWhenNotePresent("RelatedPartyTransactions", source?.RelatedPartyTransactions);
                listOfNotes.PopulateWhenNotePresent("ControllingPartyNote", source?.ControllingPartyNote);
                listOfNotes.PopulateWhenNotePresent("MembersLiabilityText", source?.MembersLiabilityText?.NoteText, source?.MembersLiabilityText?.NoteTitle);
                listOfNotes.PopulateWhenNotePresent("AdditionalNote1", source?.AdditionalNote1?.NoteText, source?.AdditionalNote1?.NoteTitle);
                listOfNotes.PopulateWhenNotePresent("AdditionalNote2", source?.AdditionalNote2?.NoteText, source?.AdditionalNote2?.NoteTitle);

                return listOfNotes;
            }

            private static List<NoteOther> AdvancesCreditAndGuaranteesGrantedToDirectorsSection1aConvertion(AdvancesCreditAndGuaranteesGrantedToDirectorsMessage? source)
            {
                var listOfNotes = new List<NoteOther>();

                if (source is not null && source.Items.IsPopulated())
                {
                    source.Items.ForEach(item => listOfNotes.AddRange(AdvancesCreditAndGuaranteesGrantedToDirectorsSection1itemConvertion(item)));
                }

                listOfNotes.PopulateWhenNotePresent("GuaranteesGrantedToDirectorsText", source?.Guarantees);

                return listOfNotes;
            }

            private static List<NoteOther> AdvancesCreditAndGuaranteesGrantedToDirectorsSection1itemConvertion(AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage item)
            {
                var listOfNotes = new List<NoteOther>();

                listOfNotes.PopulateWhenNotePresent($"AdvancesCreditGrantedDirector{item.Index}Name", item.DirectorName);
                listOfNotes.PopulateWhenNotePresent($"AdvancesCreditGrantedDirector{item.Index}AmountsBfwd", item.BalanceOutstandingAtStartOfYear);
                listOfNotes.PopulateWhenNotePresent($"AdvancesCreditGrantedDirector{item.Index}AmountsAdvanced", item.AmountsAdvanced);
                listOfNotes.PopulateWhenNotePresent($"AdvancesCreditGrantedDirector{item.Index}AmountsRepaid", item.AmountsRepaid);
                listOfNotes.PopulateWhenNotePresent($"AdvancesCreditGrantedDirector{item.Index}AmountsWOff", item.AmountsWrittenOff);
                listOfNotes.PopulateWhenNotePresent($"AdvancesCreditGrantedDirector{item.Index}AmountsWaived", item.AmountsWaived);
                listOfNotes.PopulateWhenNotePresent($"AdvancesCreditGrantedDirector{item.Index}AmountsCfwd", item.BalanceOutstandingAtEndOfYear);
                listOfNotes.PopulateWhenNotePresent($"AdvancesCreditGrantedDirector{item.Index}Details", item.AdvanceCreditConditions);

                return listOfNotes;
            }
        }
    }
}