﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Address;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.Client
{
    public class ClientDocumentDto
    {
        public string Name { get; set; } = null!;
        public string BusinessType { get; set; } = null!;
        public string BusinessSubType { get; set; } = null!;
        public string LimitedCompanyType { get; set; } = null!;
        public string RegisteredNo { get; set; } = null!;
        public string Surname { get; set; } = null!;
        public string Forenames { get; set; } = null!;
        public string Title { get; set; } = null!;
        public ClientAddressDto ClientAddresses { get; set; } = null!;
    }
}
