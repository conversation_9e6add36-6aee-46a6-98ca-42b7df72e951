﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NotesRunner.NoteFrs105Runner;
using AccountsProduction.AccountsBuilder.Domain;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations.ValidationRunners
{
    public class NotesFrs105ValidationRunnerTests
    {
        public class When_validating_advances_credit_guarantees : NotesFrs105ValidationRunnerTests
        {
            [Fact]
            public void Should_not_trigger_validation_if_no_accounts_found()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    TrialBalance =
                    {
                        TrialBalances = new List<PeriodTrialBalance>()
                    }
                };
                var issues =
                    new NotesFrs105ValidationRunner(new Mock<IGroupAccountSubAccountIntervalRepository>().Object).Validate(
                        accountsBuilder);
                issues.Any(el => el.Name == NotesFrs105Validations.AdvancesCreditGuarantees).ShouldBeFalse();
            }

            [Fact]
            public void Should_not_trigger_validation_if_accounts_are_found_but_with_0_value()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    TrialBalance =
                    {
                        TrialBalances = new List<PeriodTrialBalance>
                        {
                            new PeriodTrialBalance
                            {
                                AccountCode = 2, Amount = 0
                            }
                        }
                    }
                };
                var mock = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mock.Setup(m =>
                    m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                        new GroupAccountSubAccountInterval
                        {
                            GroupNo =  641,
                            AccountIntervalFrom = 1,
                            AccountIntervalTo = 10
                        }
                    });
                var issues =
                    new NotesFrs105ValidationRunner(mock.Object).Validate(
                        accountsBuilder);
                issues.Any(el => el.Name == NotesFrs105Validations.AdvancesCreditGuarantees).ShouldBeFalse();
            }

            [Fact]
            public void Should_trigger_validation_if_accounts_are_found_and_with_an_amount_different_than_0_value()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    TrialBalance =
                    {
                        TrialBalances = new List<PeriodTrialBalance>
                        {
                            new PeriodTrialBalance
                            {
                                AccountCode = 2, Amount = -100
                            }
                        }
                    }
                };
                var mock = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mock.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                        new GroupAccountSubAccountInterval
                        {
                            GroupNo = 631,
                            AccountIntervalFrom = 1,
                            AccountIntervalTo = 10
                        }
                    });
                var issues =
                    new NotesFrs105ValidationRunner(mock.Object).Validate(
                        accountsBuilder);
                issues.Any(el => el.Name == NotesFrs105Validations.AdvancesCreditGuarantees && el.ErrorCode == ValidationCodes.AdvancesCreditAndGuaranteesGrantedForDirectors).ShouldBeTrue();
            }
        }
    }
}