﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.AccountPeriod;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.AggregationStrategies
{
    public class AccountPeriodDataEventStrategyTests
    {

        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<ILogger<AccountPeriodDataEventStrategy>> _logger;
        private readonly IMapper _mapper;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = Guid.NewGuid();
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;

        public AccountPeriodDataEventStrategyTests()
        {
            var mapperConfig = new MapperConfiguration(cfg =>
                {
                    cfg.AddProfile<AccountPeriodMapper>();
                }
            );

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();

            _repository = new Mock<IAccountsBuilderRepository>();
            _logger = new Mock<ILogger<AccountPeriodDataEventStrategy>>();
        }

        [Fact]
        public async Task Should_update_accounts_production_data()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
               .ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId));
            var requestMessage = JsonSerializer.Serialize(new AccountPeriodMessage
            {
                ClientId = _clientId,
                PeriodId = _periodId,
                ReviseType = "SupplementaryNote",
            }, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase }); 

            var strategy = new AccountPeriodDataEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(
                repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Once);
        }

        [Fact]
        public async Task Should_not_update_accounts_production_data_when_no_accounts_builder_entity_found()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync((AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder)null);

            var requestMessage = JsonSerializer.Serialize(new AccountPeriodMessage
            {
                ClientId = _clientId,
                PeriodId = _periodId,
                ReviseType = "SupplementaryNote",
            }, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            var strategy = new AccountPeriodDataEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(
                repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Never);
        }
    }
}
