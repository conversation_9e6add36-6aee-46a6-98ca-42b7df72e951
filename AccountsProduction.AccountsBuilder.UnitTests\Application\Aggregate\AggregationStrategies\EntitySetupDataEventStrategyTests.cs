﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.NonFinancialData;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Application.IntegrationEvents;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.AggregationStrategies
{
    public class EntitySetupDataEventStrategyTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<ILogger<EntitySetupDataEventStrategy>> _logger;
        private readonly Mock<IGenerateReportDataService> _generateReportDataService;
        private readonly Mock<IDomainEventService> _domainEventService;
        private readonly IMapper _mapper;
        private readonly Mock<UserContext> _userContext;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = Guid.NewGuid();
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;
        private readonly Mock<IAccountPeriodService> _accountPeriodService;

        public EntitySetupDataEventStrategyTests()
        {
            _repository = new Mock<IAccountsBuilderRepository>();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _logger = new Mock<ILogger<EntitySetupDataEventStrategy>>();
            _userContext = new Mock<UserContext>();
            _userContext.Setup(q => q.TenantId).Returns(_tenantId.ToString());
            _generateReportDataService = new Mock<IGenerateReportDataService>();

            _generateReportDataService.Setup(c => c.GetReportData(_clientId, _periodId)).ReturnsAsync(new AccountsBuilder.Application.Common.Dto.Report.GenerateReportDto()
            {
                ClientAddressDto = new AccountsBuilder.Application.Common.Dto.Address.ClientAddressDto(),
                ClientResponse = new AccountsBuilder.Application.Common.Dto.Client.ClientResponse(),
                InvolvementDtos = new List<AccountsBuilder.Application.Common.Dto.Client.InvolvementDto>(),
                PracticeDetailsDto = new AccountsBuilder.Application.Common.Dto.PracticeDetails.PracticeDetailMessage(),
                TrialBalanceDto = new AccountsBuilder.Application.Common.Dto.TrialBalance.TrialBalanceDto()
            });
            _domainEventService = new Mock<IDomainEventService>();
        }

        [Fact]
        public async Task Should_update_accounts_production_data()
        {
            var nonFinancialDataResponse = new NonFinancialDataDto
            {
                UUID = TestHelpers.Guids.GuidOne,
                Document = new ClientDocumentDto
                {
                    Title = "Test Title",
                    Forenames = "Test Forenames",
                    Surname = "Test Surname",
                }
            };

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId,
                    _periodId));

            var requestMessage = GetRequestMessage();
            var strategy = new EntitySetupDataEventStrategy(_repository.Object, _logger.Object, _mapper, _userContext.Object, _generateReportDataService.Object, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(
                repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Once);

        }

        [Fact]
        public async Task Should_update_accounts_production_in_error_state()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId,
                    _periodId));

            var requestMessage = GetRequestMessage();
            var strategy = new EntitySetupDataEventStrategy(_repository.Object, _logger.Object, _mapper, _userContext.Object, _generateReportDataService.Object, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Once);
        }


        [Fact]
        public async Task Should_add_accounts_builder_when_not_exist()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => null);
            var requestMessage = GetRequestMessage();
            var strategy = new EntitySetupDataEventStrategy(_repository.Object, _logger.Object, _mapper, _userContext.Object, _generateReportDataService.Object, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Once);
        }

        [Fact]
        public async Task Should_add_non_financial_data_to_accounts_builder_when_business_type_not_exist()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId,
                    _periodId);
            accountsBuilder.UpdateNonFinancialData(new NonFinancialData
            {
                CompanyName = "test"
            });
            accountsBuilder.AddEntitySetup(new EntitySetup
            {
                EntitySize = "test"
            });
            accountsBuilder.AddTrialBalance(new TrialBalance
            {
                TrialBalances = new List<PeriodTrialBalance> { 
                 new PeriodTrialBalance
                 {
                     AccountCode = 1,
                     Amount = 1,
                 }
                }
            });

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);
            var requestMessage = GetRequestMessage();
            var strategy = new EntitySetupDataEventStrategy(_repository.Object, _logger.Object, _mapper, _userContext.Object, _generateReportDataService.Object, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Once);
        }

        [Fact]
        public async Task Should_throw_on_exception()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId,
                    _periodId));

            _repository.Setup(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None))
                .Throws<Exception>();
            var requestMessage = GetRequestMessage();
            var strategy = new EntitySetupDataEventStrategy(_repository.Object, _logger.Object, _mapper, _userContext.Object, _generateReportDataService.Object, _domainEventService.Object);

            await Should.ThrowAsync<Exception>(async () =>
            {
                await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);
            });
        }

        [Fact]
        public async Task Should_trigger_dpl_calculations_when_entity_setup_reporting_standard_has_changed_and_trial_balance_is_not_empty()
            {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId,
                    _periodId);
            accountsBuilder.AddEntitySetup(new EntitySetup
            {
                ReportingStandard = "Changed ReportingStandard"
            });
            accountsBuilder.AddTrialBalance(new TrialBalance
            {
                TrialBalances = new List<PeriodTrialBalance> {
                 new PeriodTrialBalance
                 {
                     AccountCode = 1,
                     Amount = 1,
                 }
                }
            });
            accountsBuilder.NonFinancialData = new NonFinancialData
            {
                CompanyName = "test",
                BusinessType = "Limited"
            };

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);
            var requestMessage = GetRequestMessage();
            var strategy = new EntitySetupDataEventStrategy(_repository.Object, _logger.Object, _mapper, _userContext.Object, _generateReportDataService.Object, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _domainEventService.Verify(domainEventService =>
                    domainEventService.Publish(It.IsAny<NotificationTrialBalanceChanged>(), CancellationToken.None),
                Times.Once);
        }

        [Fact]
        public async Task Should_trigger_dpl_calculations_when_entity_setup_practice_address_has_changed_and_trial_balance_is_not_empty()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId,
                    _periodId);
            accountsBuilder.AddEntitySetup(new EntitySetup
            {
                PracticeAddress = "Changed PracticeAddress"
            });
            accountsBuilder.AddTrialBalance(new TrialBalance
            {
                TrialBalances = new List<PeriodTrialBalance> {
                 new PeriodTrialBalance
                 {
                     AccountCode = 1,
                     Amount = 1,
                 }
                }
            });
            accountsBuilder.NonFinancialData = new NonFinancialData
            {
                CompanyName = "test",
                BusinessType = "Limited"
            };

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);
            var requestMessage = GetRequestMessage();
            var strategy = new EntitySetupDataEventStrategy(_repository.Object, _logger.Object, _mapper, _userContext.Object, _generateReportDataService.Object, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _domainEventService.Verify(domainEventService =>
                    domainEventService.Publish(It.IsAny<NotificationTrialBalanceChanged>(), CancellationToken.None),
                Times.Once);
        }

        [Fact]
        public async Task Should_not_trigger_dpl_calculations_when_entity_setup_reporting_standard_has_changed_and_trial_balance_is_empty()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId,
                    _periodId);
            accountsBuilder.AddEntitySetup(new EntitySetup
            {
                ReportingStandard = "Changed ReportingStandard"
            });
          
            accountsBuilder.NonFinancialData = new NonFinancialData
            {
                CompanyName = "test",
                BusinessType = "Limited"
            };

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);
            var requestMessage = GetRequestMessage();
            var strategy = new EntitySetupDataEventStrategy(_repository.Object, _logger.Object, _mapper, _userContext.Object, _generateReportDataService.Object, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _domainEventService.Verify(domainEventService =>
                    domainEventService.Publish(It.IsAny<NotificationTrialBalanceChanged>(), CancellationToken.None),
                Times.Never);
        }



        [Fact]
        public async Task Should_not_trigger_dpl_calculations_when_entity_setup_reporting_standard_remains_the_same_and_trial_balance_is_not_empty()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId,
                    _periodId);
            accountsBuilder.AddEntitySetup(new EntitySetup
            {
                ReportingStandard = "ReportingStandard"
            });
            accountsBuilder.AddTrialBalance(new TrialBalance
            {
                TrialBalances = new List<PeriodTrialBalance> {
                 new PeriodTrialBalance
                 {
                     AccountCode = 1,
                     Amount = 1,
                 }
                }
            });
            accountsBuilder.NonFinancialData = new NonFinancialData
            {
                CompanyName = "test",
                BusinessType = "Limited"
            };

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);
            var requestMessage = GetRequestMessage();
            var strategy = new EntitySetupDataEventStrategy(_repository.Object, _logger.Object, _mapper, _userContext.Object, _generateReportDataService.Object, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _domainEventService.Verify(domainEventService =>
                    domainEventService.Publish(It.IsAny<NotificationTrialBalanceChanged>(), CancellationToken.None),
                Times.Never);
        }

      
        private string GetRequestMessage()
        {
            var message = JsonSerializer.Serialize(new EntitySetupDto
            {
                ChoiceOfStatement = "ChoiceOfStatement",
                DormantStatus = "DormantStatus",
                EntitySize = "EntitySize",
                IndependentReviewType = "IndependentReviewType",
                ReportingStandard = "ReportingStandard",
                Terminology = "Terminology",
                TradingStatus = "TradingStatus",
                CIC34Report = "Simple"
            }, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return message;
        }
    }
}
