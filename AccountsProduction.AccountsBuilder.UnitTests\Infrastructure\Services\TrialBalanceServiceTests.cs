﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Infrastructure.Services;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using Shouldly;
using System.Net;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Infrastructure.Services
{
    public class TrialBalanceServiceTests
    {
        private readonly Mock<ILogger<TrialBalanceService>> _logger;
        private readonly Mock<UserContext> _userContext;
        private readonly Mock<IEnvVariableProvider> _envVariableProvider;
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private TrialBalanceDto _trialBalanceResponse;

        public TrialBalanceServiceTests()
        {
            _logger = new Mock<ILogger<TrialBalanceService>>();
            _envVariableProvider = new Mock<IEnvVariableProvider>();
            _userContext = new Mock<UserContext>();
            _userContext.Setup(x => x.TenantId).Returns(TestHelpers.Guids.GuidOne.ToString());
            _userContext.Setup(x => x.CorrelationId).Returns(Guid.NewGuid().ToString());

            _trialBalanceResponse = new TrialBalanceDto
            {
                ClientId = TestHelpers.Guids.GuidOne,
                AccountsChartId = 1,
                AccountsChartIdentifier = "ELTD",
                TrialBalances = new List<PeriodTrialBalanceDto>()
            };
        }

        [Fact]
        public async Task Should_get_trialbalance()
        {
            var responseMessage = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(_trialBalanceResponse, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase }))
            };
            var service = SetupMockData(responseMessage);
            var response = await service.GetTrialBalanceCalculate(TestHelpers.Guids.GuidOne, TestHelpers.Guids.GuidTwo);

            response.ShouldNotBeNull();
            response.ShouldBeOfType(typeof(TrialBalanceDto));
            response.AccountsChartId.ShouldBe(1);
            response.AccountsChartIdentifier.ShouldBe("ELTD");
            response.TrialBalances.Count.ShouldBe(0);
        }

        [Theory]
        [InlineData(HttpStatusCode.NotFound)]
        [InlineData(HttpStatusCode.InternalServerError)]
        public async Task Should_throw_exception_when_trialbalance_http_request_status_is_not_success(HttpStatusCode httpStatusCode)
        {
            var responseMessage = new HttpResponseMessage
            {
                StatusCode = httpStatusCode
            };

            var accountPeriodService = SetupMockData(responseMessage);

            await Should.ThrowAsync<HttpRequestException>(async () => { await accountPeriodService.GetTrialBalanceCalculate(TestHelpers.Guids.GuidOne, TestHelpers.Guids.GuidTwo); });
        }


        private ITrialBalanceService SetupMockData(HttpResponseMessage responseMessage)
        {
            _envVariableProvider.Setup(obj => obj.AwsAccessKey).Returns("accesskey");
            _envVariableProvider.Setup(obj => obj.AwsRegion).Returns("eu-west-2");
            _envVariableProvider.Setup(obj => obj.AwsSecretKey).Returns("secretkey");
            _envVariableProvider.Setup(obj => obj.AwsSessionToken).Returns("session");
            _envVariableProvider.Setup(provider => provider.AccountPeriodApiScheme).Returns("https");
            _envVariableProvider.Setup(provider => provider.AccountPeriodApiHost).Returns("api.elements-development.iris.co.uk/accountsproduction-accountperiod/v1");
            _envVariableProvider.Setup(provider => provider.AccountPeriodApiKey).Returns("test");
            _envVariableProvider.Setup(provider => provider.AccountPeriodApiId).Returns("test");
            _envVariableProvider.Setup(provider => provider.TrialBalanceApiScheme).Returns("https");
            _envVariableProvider.Setup(provider => provider.TrialBalanceApiHost).Returns("api.elements-development.iris.co.uk/accountsproduction-accountperiod/v1");
            _envVariableProvider.Setup(provider => provider.TrialBalanceApiKey).Returns("test");
            _envVariableProvider.Setup(provider => provider.TrialBalanceApiId).Returns("test");

            var mockFactory = new Mock<IHttpClientFactory>();
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(responseMessage)
                .Verifiable();
            var client = new HttpClient(_mockHttpMessageHandler.Object);
            mockFactory.Setup(_ => _.CreateClient(It.IsAny<string>())).Returns(client);

            var service = new TrialBalanceService(_envVariableProvider.Object, client, _userContext.Object, _logger.Object);
            return service;
        }

    }
}
