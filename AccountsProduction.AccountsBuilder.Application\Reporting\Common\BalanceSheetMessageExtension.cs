﻿using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.Common
{
    public static class BalanceSheetMessageExtension
    {
        public static Guid PeriodId { get; set; }
        public static decimal CalledUpShareCapitalNotPaidNumber(this BalanceSheetMessage bsm) => (bsm?.CalledUpShareCapitalNotPaid?.Value).ToDecimal();
        public static decimal FixedAssetsNumber(this BalanceSheetMessage bsm) => (bsm?.FixedAssets?.Value).ToDecimal();
        public static decimal CurrentAssetsNumber(this BalanceSheetMessage bsm) => (bsm?.CurrentAssets?.Value).ToDecimal();
        public static decimal PrepaymentsAndAccruedIncomeNumber(this BalanceSheetMessage bsm) => (bsm?.PrepaymentsAndAccruedIncome?.Value).ToDecimal();
        public static decimal CreditorsAmountsFallingDueWithinOneYearNumber(this BalanceSheetMessage bsm) => (bsm?.CreditorsAmountsFallingDueWithinOneYear?.Value).ToDecimal();
        public static decimal NetCurrentAssetsOrLiabilitiesNumber(this BalanceSheetMessage bsm) => (bsm?.NetCurrentAssetsOrLiabilities?.Value).ToDecimal();
        public static decimal TotalAssetsLessCurrentLiabilitiesNumber(this BalanceSheetMessage bsm) => (bsm?.TotalAssetsLessCurrentLiabilities?.Value).ToDecimal();
        public static decimal CreditorsAmountsFallingAfterMoreThanOneYearNumber(this BalanceSheetMessage bsm) => (bsm?.CreditorsAmountsFallingAfterMoreThanOneYear?.Value).ToDecimal();
        public static decimal ProvisionsForLiabilitiesNumber(this BalanceSheetMessage bsm) => (bsm?.ProvisionsForLiabilities?.Value).ToDecimal();
        public static decimal AccrualsAndDeferredIncomeNumber(this BalanceSheetMessage bsm) => (bsm?.AccrualsAndDeferredIncome?.Value).ToDecimal();
        public static decimal NetAssetsNumber(this BalanceSheetMessage bsm) => (bsm?.NetAssets?.Value).ToDecimal();
        public static decimal CapitalAndReservesNumber(this BalanceSheetMessage bsm) => (bsm?.CapitalAndReserves?.Value).ToDecimal();
        public static decimal IntangibleAssetsNumber(this BalanceSheetMessage bsm) => (bsm?.IntangibleAssets?.Value).ToDecimal();
        public static decimal TangibleFixedAssetsNumber(this BalanceSheetMessage bsm) => (bsm?.TangibleFixedAssets?.Value).ToDecimal();
        public static decimal FixedAssetInvestmentsNumber(this BalanceSheetMessage bsm) => (bsm?.FixedAssetInvestments?.Value).ToDecimal();
        public static decimal CurrentAssetInvestmentsNumber(this BalanceSheetMessage bsm) => (bsm?.CurrentAssetInvestments?.Value).ToDecimal();
        public static decimal InvestmentPropertyNumber(this BalanceSheetMessage bsm) => (bsm?.InvestmentProperty?.Value).ToDecimal();
        public static decimal StockNumber(this BalanceSheetMessage bsm) => (bsm?.Stock?.Value).ToDecimal();
        public static decimal DebtorsNumber(this BalanceSheetMessage bsm) => (bsm?.Debtors?.Value).ToDecimal();
        public static decimal CashAtBankAndInHandNumber(this BalanceSheetMessage bsm) => (bsm?.CashAtBankAndInHand?.Value).ToDecimal();
        public static decimal CapitalAccountNumber(this BalanceSheetMessage bsm) => (bsm?.CapitalAccount?.Value).ToDecimal();
        public static decimal PartnersCapitalAccountsNumber(this BalanceSheetMessage bsm) => (bsm?.PartnersCapitalAccounts?.Value).ToDecimal();
        public static decimal PartnersCurrentAccountsNumber(this BalanceSheetMessage bsm) => (bsm?.PartnersCurrentAccounts?.Value).ToDecimal();
        public static decimal OtherReservesNumber(this BalanceSheetMessage bsm) => (bsm?.OtherReserves?.Value).ToDecimal();
        public static decimal GoodwillNumber(this BalanceSheetMessage bsm) => (bsm?.Goodwill?.Value).ToDecimal();
        public static decimal PensionSchemeAssetsLiabilitiesNumber(this BalanceSheetMessage bsm) => (bsm?.PensionSchemeAssetsLiabilities?.Value).ToDecimal();
        public static decimal HealthcareObligatonsNumber(this BalanceSheetMessage bsm) => (bsm?.HealthcareObligatons?.Value).ToDecimal();
        public static decimal CalledUpShareCapitalNumber(this BalanceSheetMessage bsm) => (bsm?.CalledUpShareCapital?.Value).ToDecimal();
        public static decimal SharePremiumReserveNumber(this BalanceSheetMessage bsm) => (bsm?.SharePremiumReserve?.Value).ToDecimal();
        public static decimal RevaluationReserveNumber(this BalanceSheetMessage bsm) => (bsm?.RevaluationReserve?.Value).ToDecimal();
        public static decimal CapitalRedemptionReserveNumber(this BalanceSheetMessage bsm) => (bsm?.CapitalRedemptionReserve?.Value).ToDecimal();
        public static decimal OtherReserves1Number(this BalanceSheetMessage bsm) => (bsm?.OtherReserves1?.Value).ToDecimal();
        public static decimal OtherReserves2Number(this BalanceSheetMessage bsm) => (bsm?.OtherReserves2?.Value).ToDecimal();
        public static decimal FairValueReserveNumber(this BalanceSheetMessage bsm) => (bsm?.FairValueReserve?.Value).ToDecimal();
        public static decimal ProfitAndLossReserveNumber(this BalanceSheetMessage bsm) => (bsm?.ProfitAndLossReserve?.Value).ToDecimal();
        public static decimal NonControllingInterestsNumber(this BalanceSheetMessage bsm) => (bsm?.NonControllingInterests?.Value).ToDecimal();
        public static decimal TotalMembersInterestsNumber(this BalanceSheetMessage bsm) => (bsm?.TotalMembersInterests?.Value).ToDecimal();
        public static decimal MembersOtherInterestsNumber(this BalanceSheetMessage bsm) => (bsm?.MembersOtherInterests?.Value).ToDecimal();
        public static decimal MembersCapitalNumber(this BalanceSheetMessage bsm) => (bsm?.MembersCapital?.Value).ToDecimal();
        public static decimal OtherDebtsDueToMembersNumber(this BalanceSheetMessage bsm) => (bsm?.OtherDebtsDueToMembers?.Value).ToDecimal();
        public static decimal LoansAndOtherDebtsDueToMembersNumber(this BalanceSheetMessage bsm) => (bsm?.LoansAndOtherDebtsDueToMembers?.Value).ToDecimal();
        public static decimal HerdBasisNumber(this BalanceSheetMessage bsm) => (bsm?.HerdBasis?.Value).ToDecimal();
    }
}
