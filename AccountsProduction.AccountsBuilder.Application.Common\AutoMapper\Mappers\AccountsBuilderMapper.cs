﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder;
using AutoMapper;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class AccountsBuilderMapper : Profile
    {
        public AccountsBuilderMapper()
        {
            CreateMap<Domain.AccountsBuilderModels.AccountsBuilder, AccountsBuilderDto>();
            CreateMap<Domain.AccountsBuilderModels.AccountsBuilder, AccountsBuilderFullDto>()
                .ForMember(s => s.Involvements, o => o.MapFrom(d => d.InvolvementsData.Involvements));
        }
    }
}