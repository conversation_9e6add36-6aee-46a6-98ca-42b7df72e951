﻿using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper;
using AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence;
using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.Commands.SaveAndUpdateStrategies
{
    public class FRS1021AStrategyTests
    {
        private readonly Mock<IMediator> _mediatorMock;
        private readonly Mock<ILogger<FRS1021AStrategy>> _loggerMock;
        private readonly IMapper _mapper;

        private readonly AccountsProductionReportingDbContext _dbContext;
        private readonly FRS1021AStrategy _frs1021aStrategy;

        public FRS1021AStrategyTests()
        {
            _mediatorMock = new Mock<IMediator>();
            _loggerMock = new Mock<ILogger<FRS1021AStrategy>>();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();

            var options = new DbContextOptionsBuilder<AccountsProductionReportingDbContext>()
               .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
               .Options;
            _dbContext = new AccountsProductionReportingDbContext(options);
            _dbContext.Database.EnsureCreated();

            _frs1021aStrategy = new FRS1021AStrategy(_mediatorMock.Object, _loggerMock.Object, _dbContext, _mapper);
        }

        [Fact]
        public void Name_should_return_expected_value()
        {
            var result = _frs1021aStrategy.Name;

            Assert.Equal(ReportType.FRS102_1A, result);
        }
    }
}
