﻿using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.BalanceSheetRunner
{
    public class BalanceSheetValidationRunner : ValidationRunner
    {
        public const int Account502 = 502;

        protected override Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>> ValidationRules =>
            new Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>>
            {
                {
                    BalanceSheetValidation.BalanceSheet,
                    ValidateBalanceSheet
                }
            };

        private ValidationIssue ValidateBalanceSheet(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            if (accountsBuilder.TrialBalance.TrialBalances.Any(x => x.AccountCode == Account502)) return null;

            var ruleConfig = BalanceSheetValidation.BalanceSheetConfig;
            return ValidationIssue.BuildValidationIssue(ruleConfig.ErrorCode, ruleConfig.Name, ruleConfig.DisplayName, ruleConfig.Breadcrumb,
                ruleConfig.Description, ruleConfig.Type, ruleConfig.Target, ruleConfig.ErrorCategory);
        }
    }
}
