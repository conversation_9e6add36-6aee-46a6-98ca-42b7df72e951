﻿namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.Client
{
    public class InvolvementDto
    {
        public int Id { get; set; }
        public Guid InvolvedClientGuid { get; set; }
        public Guid CurrentClientGuid { get; set; }
        public string? InvolvementType { get; set; }
        public string? InvolvedClientType { get; set; }
        public string? InvolvedClientTitle { get; set; }
        public string? InvolvedClientName { get; set; }
        public string? InvolvedClientFirstName { get; set; }
        public DateTime? InvolvedClientDateOfDeath { get; set; }
        public string? InvolvedClientSurname { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public Guid InvolvementGuid { get; set; }
        public int? AccountingOrder { get; set; }
        public bool IsDeleted { get; set; }
    }
}
