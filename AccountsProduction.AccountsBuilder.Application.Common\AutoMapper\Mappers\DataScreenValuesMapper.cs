﻿using AccountsProduction.AccountsBuilder.Domain.DataScreens;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.DataScreenValue;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class DataScreenValuesMapper : Profile
    {
        public DataScreenValuesMapper()
        {
            CreateMap<DataScreenValue, DataScreenValueMessage>()
                .ForMember(x => x.ClientId, dest => dest.MapFrom(x => x.ClientId))
                .ForMember(x => x.PeriodId, dest => dest.MapFrom(x => x.PeriodId))
                .ForMember(x => x.PreviousPeriodId, dest => dest.MapFrom(x => x.PreviousPeriodId))
                .ForMember(x => x.TenantId, dest => dest.MapFrom(x => x.TenantId))
                .ForMember(x => x.CurrentPeriod, dest => dest.MapFrom(x => x.CurrentPeriod))
                .ForMember(x => x.PreviousPeriod, dest => dest.MapFrom(x => x.PreviousPeriod))
                .ForMember(x => x.CorrelationId, dest => dest.MapFrom(x => x.CorrelationId)).ReverseMap();

            CreateMap<PeriodScreenValue, PeriodScreenValueMessage>().ReverseMap();
            CreateMap<ScreenField, ScreenFieldMessage>().ReverseMap();
        }
    }
}
