﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.ProfitAndLoss.Contract;

namespace AccountsProduction.AccountsBuilder.Reporting.Domain.ProfitAndLoss
{
    public class ProfitAndLossNonCorp : ProfitAndLossBase
    {
        public decimal Sales { get; set; }

        public decimal CostOfSales { get; set; }

        public decimal OtherIncome { get; set; }

        public decimal Expenses { get; set; }

        public decimal FinanceCosts { get; set; }

        public decimal PartnerAppropriations { get; set; }

        public decimal Depreciation { get; set; }
    }
}
