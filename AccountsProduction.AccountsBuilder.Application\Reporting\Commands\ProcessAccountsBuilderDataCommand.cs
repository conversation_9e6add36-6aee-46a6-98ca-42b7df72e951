﻿using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies;
using Iris.AccountsProduction.Common.Toolkit.Exceptions;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.Commands
{
    public class ProcessAccountsBuilderDataCommand : IRequest<Unit>
    {
        public AccountsBuilderReportingMessageDto Message { get; set; } = null!;
    }

    public class ProcessAccountsBuilderDataCommandHandler : IRequestHandler<ProcessAccountsBuilderDataCommand, Unit>
    {
        private readonly UserContext _userContext;
        private readonly IAccountsProductionReportingDbContext _dbContext;
        private readonly ILogger<ProcessAccountsBuilderDataCommandHandler> _logger;
        private readonly IEnumerable<ISaveUpdateStrategy> _saveUpdateStrategies;

        public ProcessAccountsBuilderDataCommandHandler(UserContext userContext,
            IAccountsProductionReportingDbContext context,
            ILogger<ProcessAccountsBuilderDataCommandHandler> logger,
            IEnumerable<ISaveUpdateStrategy> saveUpdateStrategies)
        {
            _userContext = userContext;
            _dbContext = context;
            _logger = logger;
            _saveUpdateStrategies = saveUpdateStrategies;
        }

        public async Task<Unit> Handle(ProcessAccountsBuilderDataCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("{ProcessTime} Begin processing data", DateTime.UtcNow);

            var data = request.Message;

            if (data == null)
            {
                _logger.LogWarning("AccountsBuilder data is null - TenantId: {TenantId}=", _userContext.TenantId);

                return Unit.Value;
            }

            _logger.LogInformation("{ProcessTime} Start processing message with PeriodId: {PeriodId}, clientId: {ClientId}, TenantId: {TenantId}, ReportType: {ReportType}", DateTime.UtcNow, data.PeriodId, data.ClientId, data.TenantId, data.ReportType);

            if (data.TenantId.ToString() != _userContext.TenantId)
            {
                throw new InvalidTenantException($"TenantId does not match - data.TenantId: {data.TenantId}, UserContext.TenantId: {_userContext.TenantId}");
            }

            var report = _saveUpdateStrategies.FirstOrDefault(r => r.IsMatch(data.ReportType));

            if (report == null)
            {
                throw new BadRequestException($"ReportType {data.ReportType} is invalid - PeriodId: {data.PeriodId}, ClientId: {data.ClientId}");
            }

            _logger.LogInformation("{ProcessTime} Found Strategy {report} for {reportName}", DateTime.UtcNow, report, report.Name);

            var existingData = await _dbContext.ReportingPeriods
               .Where(x => x.ClientId == data.ClientId && x.AccountPeriodId == data.PeriodId)
               .Include(x => x.Client).ThenInclude(c => c.Tenant)
               .Include(x => x.Client).ThenInclude(c => c.Member)
               .Include(x => x.Signature)
               .Include(x => x.BalanceSheetGeneral)
               .Include(x => x.BalanceSheetFRS105)
               .Include(x => x.BalanceSheetLLP)
               .Include(x => x.NoteAccountingPolicies)
               .Include(x => x.NoteBalanceSheet)
               .Include(x => x.NoteOther)
               .Include(x => x.NoteProfitAndLoss)
               .Include(x => x.Reports)
               .Include(x => x.ProfitAndLossCompaniesAct)
               .Include(x => x.ProfitAndLossFRS105)
               .Include(x => x.ProfitAndLossNonCorp)
               .Include(x => x.LineItem)
               .Include(x => x.DplSummaryCalcs)
               .Include(x => x.MultiColumnToken)
               .AsSplitQuery()
               .FirstOrDefaultAsync(cancellationToken);

            _logger.LogInformation("Data fetched");

            report.ReportingPeriod = existingData;

            await report.SendSaveAndUpdateDataEvents(data, cancellationToken);

            _logger.LogInformation("{ProcessTime} Finished processing the events", DateTime.UtcNow);

            await _dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("{ProcessTime} End processing data", DateTime.UtcNow);

            return Unit.Value;
        }
    }
}
