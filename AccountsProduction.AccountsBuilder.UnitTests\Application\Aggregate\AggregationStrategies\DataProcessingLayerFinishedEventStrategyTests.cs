﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.AggregationStrategies
{
    public class DataProcessingLayerFinishedEventStrategyTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<IDomainEventService> _domainEventService;
        private readonly Mock<ILogger<DataProcessingLayerFinishedEventStrategy>> _logger;
        private readonly Mock<UserContext> _userContext;
        private readonly IMapper _mapper;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = TestHelpers.Guids.GuidTwo;
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;

        public DataProcessingLayerFinishedEventStrategyTests()
        {
            var mapperConfig = new MapperConfiguration(cfg => { cfg.AddProfile<TrialBalanceMapper>(); });
            mapperConfig.AssertConfigurationIsValid();
            _repository = new Mock<IAccountsBuilderRepository>();
            _mapper = mapperConfig.CreateMapper();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _logger = new Mock<ILogger<DataProcessingLayerFinishedEventStrategy>>();
            _userContext = new Mock<UserContext>();
            _userContext.Setup(q => q.TenantId).Returns(_tenantId.ToString());
            _domainEventService = new Mock<IDomainEventService>();
        }

        [Fact]
        public async Task Should_update_financial_data()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, null!)
            {
                FinancialData = new FinancialData
                {
                    Status = (int)FinancialDataStatusDto.Success,
                    Financials = []
                }
            };

            _repository.Setup(repository => repository.Get(_processId, CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            var requestMessage = GetSuccessfulRequestMessage(DateTime.UtcNow);
            var strategy = new DataProcessingLayerFinishedEventStrategy(_repository.Object, _logger.Object, _mapper, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Once);
        }

        [Fact]
        public async Task Should_update_financial_data_when_one_period_missing()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, null!)
            {
                TrialBalance = new TrialBalance
                {
                    ReportingPeriods =
                    [
                        new() { Id = Guid.NewGuid(), EndDate = new DateTime(2020, 1, 1) },
                        new() { Id = Guid.NewGuid(), EndDate = new DateTime(2021, 1, 1) }
                    ]
                },
                FinancialData = new FinancialData
                {
                    Status = (int)FinancialDataStatusDto.Success,
                    Financials = []
                }
            };
            _repository.Setup(repository => repository.Get(It.IsAny<Guid>(), CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            var requestMessage = GetSuccessfulRequestMessage(new DateTime(2021, 1, 1));
            var strategy = new DataProcessingLayerFinishedEventStrategy(_repository.Object, _logger.Object, _mapper, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(
                repository => repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(entity =>
                    entity.FinancialData.Financials.Count == 2 &&
                    entity.FinancialData.Financials.FirstOrDefault(x => x.Period == new DateTime(2020, 1, 1)) != null),
                    CancellationToken.None),
                Times.Once);
        }

        [Fact]
        public async Task Should_update_financial_data_with_error_status_when_retry_is_reached()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, null!)
            {
                FinancialData = new FinancialData
                {
                    Status = (int)FinancialDataStatusDto.Error,
                    Retry = 3,
                    Financials = []
                }
            };
            _repository.Setup(repository => repository.Get(It.IsAny<Guid>(), CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            var requestMessage = GetFailedRequestMessage();
            var strategy = new DataProcessingLayerFinishedEventStrategy(_repository.Object, _logger.Object, _mapper, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(
                repository => repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(entity =>
                    entity.FinancialData.StatusCode == AccountsBuilder.Domain.Status.Fail &&
                    entity.FinancialData.ErrorCode == Error.TechnicalError),
                    CancellationToken.None),
                Times.Once);

        }

        [Theory]
        [InlineData(ReportStandardType.FRS102_1A)]
        [InlineData(ReportStandardType.FRS102)]
        public async Task Should_retry_sending_event_to_dpl_if_error_and_retry_not_reached(string reportingStandard)
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, null!)
            {
                EntitySetup = new EntitySetup
                {
                    EntitySize = "Small",
                    Terminology = "Companies Act",
                    ReportingStandard = reportingStandard
                },
                FinancialData = new FinancialData
                {
                    Status = (int)FinancialDataStatusDto.Error,
                    Retry = 1,
                    Financials = []
                },
                TrialBalance = new TrialBalance
                {
                    ReportingPeriods =
                    [
                        new() { Id = Guid.NewGuid(), EndDate = new DateTime(2020, 1, 1) },
                        new() { Id = Guid.NewGuid(), EndDate = new DateTime(2021, 1, 1) }
                    ],
                    TrialBalances =
                    [
                        new()
                        {
                            AccountCode = 1,
                            Amount = 2000,
                            Description = "no description",
                            SubAccountCode = 1,
                            Year = DateTime.UtcNow
                        }
                    ],
                }
            };
            _repository.Setup(repository => repository.Get(It.IsAny<Guid>(), CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            var requestMessage = GetFailedRequestMessage();
            var strategy = new DataProcessingLayerFinishedEventStrategy(_repository.Object, _logger.Object, _mapper, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(
                repository => repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(entity =>
                    entity.FinancialData.Retry == 2),
                    CancellationToken.None),
                Times.Once);

        }

        [Fact]
        public async Task Should_not_update_financial_data_when_no_process_found()
        {
            var requestMessage = GetSuccessfulRequestMessage(new DateTime(2021, 1, 1));
            var strategy = new DataProcessingLayerFinishedEventStrategy(_repository.Object, _logger.Object, _mapper, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_not_update_financial_data_when_no_accounts_builder_found()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => null!);
            var requestMessage = GetSuccessfulRequestMessage(new DateTime(2021, 1, 1));
            var strategy = new DataProcessingLayerFinishedEventStrategy(_repository.Object, _logger.Object, _mapper, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_throw_when_exception()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, null!)
            {
                FinancialData = new FinancialData
                {
                    Status = (int)FinancialDataStatusDto.Success,
                    Financials = []
                }
            };
            _repository.Setup(repository => repository.Get(It.IsAny<Guid>(), CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            _repository.Setup(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None)).Throws<Exception>();
            var requestMessage = GetSuccessfulRequestMessage(new DateTime(2021, 1, 1));
            var strategy = new DataProcessingLayerFinishedEventStrategy(_repository.Object, _logger.Object, _mapper, _domainEventService.Object);

            await Should.ThrowAsync<Exception>(async () => { await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None); });
        }

        [Fact]
        public async Task Should_update_status_for_dpl_error()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, null!);
            _repository.Setup(repository => repository.Get(_processId, CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            var requestMessage = GetCalculationErrorRequestMessage();

            var strategy = new DataProcessingLayerFinishedEventStrategy(_repository.Object, _logger.Object, _mapper, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(
                repository => repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(entity =>
                    entity.FinancialData.StatusCode == Status.Fail &&
                    entity.FinancialData.ErrorCode == Error.CalculationError),
                    CancellationToken.None),
                Times.Once);
        }

        private static string GetCalculationErrorRequestMessage()
        {
            var drilldown = new List<FinancialDataDrilldownMessage>
            {
                new() { AccountCode = 1, Amount = 100, Description = "Bank deposit", SubAccountCode = null }
            };
            var notApplicableCategoryDto = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "Not Calculable/Not Applicable"
            };

            var message = JsonSerializer.Serialize(new FinancialDataDto
            {
                Status = FinancialDataStatusDto.Success,
                Data =
                [
                    new() {
                        Period = DateTime.UtcNow,
                        Turnover = new FinancialDataCategoryMessage { DrilldownData = drilldown, Value = "-500.0" },
                        OtherIncome = notApplicableCategoryDto,
                        CostOfRawMaterialsAndConsumables = new FinancialDataCategoryMessage { DrilldownData = drilldown, Value = "0" },
                        StaffCosts = new FinancialDataCategoryMessage { DrilldownData = drilldown, Value = "100" },
                        DepreciationAndOtherAmountsWrittenOffAssets = new FinancialDataCategoryMessage { DrilldownData = drilldown, Value = "0" },
                        OtherCharges = new FinancialDataCategoryMessage { DrilldownData = drilldown, Value = "1000" },
                        Tax = new FinancialDataCategoryMessage { DrilldownData = drilldown, Value = "100" }
                    }
                ]
            }, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return message;
        }

        [Fact]
        public async Task Should_set_correct_process_status()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, null!);
            _repository.Setup(repository => repository.Get(_processId, CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            var requestMessage = GetSuccessfulRequestMessage(new DateTime(2021, 1, 1));
            var strategy = new DataProcessingLayerFinishedEventStrategy(_repository.Object, _logger.Object, _mapper, _domainEventService.Object);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);
            _repository.Verify(
                repository => repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(entity =>
                    entity.Status == AccountsBuilder.Domain.Status.Successful &&
                    entity.ErrorCode == null),
                    CancellationToken.None),
                Times.Once);
        }

        private static string GetSuccessfulRequestMessage(DateTime period)
        {
            var drilldown = new List<FinancialDataDrilldownMessage>
            {
                new() { AccountCode = 1, Amount = 100, Description = "Bank deposit", SubAccountCode = null }
            };
            var turnoverCategoryDto = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "-500.0"
            };
            var otherIncomeCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "1300.0"
            };

            var staffCostsCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "100"
            };
            var otherChargesCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "1000"
            };
            var taxCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "100"
            };
            var zeroValueCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "0"
            };
            var fixedAssetsCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "100"
            };
            var currentAssetsCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "300"
            };
            var netAssetsCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "200"
            };
            var capitalAndReservesCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "400"
            };

            var message = JsonSerializer.Serialize(new FinancialDataDto
            {
                Status = FinancialDataStatusDto.Success,
                Data =
                [
                    new()
                    {
                        Period = period,
                        Turnover = turnoverCategoryDto,
                        OtherIncome = otherIncomeCategory,
                        CostOfRawMaterialsAndConsumables = zeroValueCategory,
                        StaffCosts = staffCostsCategory,
                        DepreciationAndOtherAmountsWrittenOffAssets = zeroValueCategory,
                        OtherCharges = otherChargesCategory,
                        Tax = taxCategory,
                        CalledUpShareCapitalNotPaid = zeroValueCategory,
                        FixedAssets = fixedAssetsCategory,
                        CurrentAssets = currentAssetsCategory,
                        PrepaymentsAndAccruedIncome = zeroValueCategory,
                        CreditorsAmountsFallingDueWithinOneYear = zeroValueCategory,
                        NetCurrentAssetsOrLiabilities = zeroValueCategory,
                        TotalAssetsLessCurrentLiabilities = zeroValueCategory,
                        CreditorsAmountsFallingAfterMoreThanOneYear = zeroValueCategory,
                        ProvisionsForLiabilities = zeroValueCategory,
                        AccrualsAndDeferredIncome = zeroValueCategory,
                        NetAssets = netAssetsCategory,
                        CapitalAndReserves = capitalAndReservesCategory
                    }
                ]
            }, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return message;
        }

        private static string GetFailedRequestMessage()
        {
            var message = JsonSerializer.Serialize(new FinancialDataDto
            {
                Data = [],
                Status = FinancialDataStatusDto.Error,
                Reason = new FinancialDataExceptionDetailsDto()
                {
                    Message = "some error"
                }
            }, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return message;
        }
    }
}