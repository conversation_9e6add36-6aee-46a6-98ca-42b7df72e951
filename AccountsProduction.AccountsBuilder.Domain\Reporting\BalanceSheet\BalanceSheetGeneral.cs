﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet.Contract;

namespace AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet
{
    public class BalanceSheetGeneral : IBalanceSheetBase
    {
        public decimal CalledUpShareCapitalNotPaid { get; set; }

        public decimal Goodwill { get; set; }

        public decimal IntangibleAssets { get; set; }

        public decimal TangibleFixedAssets { get; set; }

        public decimal FixedAssetInvestments { get; set; }

        public decimal CurrentAssetInvestments { get; set; }

        public decimal InvestmentProperty { get; set; }

        public decimal Stock { get; set; }

        public decimal Debtors { get; set; }

        public decimal CashAtBankAndInHand { get; set; }

        public decimal PrepaymentsAndAccruedIncome { get; set; }

        public decimal CreditorsAmountsFallingDueWithinOneYear { get; set; }

        public decimal CreditorsAmountsFallingAfterMoreThanOneYear { get; set; }

        public decimal ProvisionsForLiabilities { get; set; }

        public decimal PensionSchemeAssetsLiabilities { get; set; }

        public decimal HealthcareObligatons { get; set; }

        public decimal AccrualsAndDeferredIncome { get; set; }

        public decimal CalledUpShareCapital { get; set; }

        public decimal SharePremiumReserve { get; set; }

        public decimal RevaluationReserve { get; set; }

        public decimal CapitalRedemptionReserve { get; set; }

        public decimal OtherReserves1 { get; set; }

        public decimal OtherReserves2 { get; set; }

        public decimal FairValueReserve { get; set; }

        public decimal ProfitAndLossReserve { get; set; }

        public decimal NonControllingInterests { get; set; }

        public decimal HerdBasis { get; set; }

        public virtual BalanceSheetIFRS BalanceSheetIFRS { get; set; } = null!;

        public virtual BalanceSheetNonCorp BalanceSheetNonCorp { get; set; } = null!;

        public virtual BalanceSheetCH BalanceSheetCH { get; set; } = null!;

        public virtual ReportingPeriod ReportingPeriod { get; set; } = null!;
        public Guid ClientId { get; set; }
        public Guid AccountPeriodId { get; set; }
    }
}
