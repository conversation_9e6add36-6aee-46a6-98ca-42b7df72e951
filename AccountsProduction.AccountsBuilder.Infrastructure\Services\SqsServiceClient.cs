﻿using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using Amazon.SQS;
using Amazon.SQS.Model;
using Iris.AccountsProduction.Common.Toolkit.Exceptions;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Infrastructure.Services
{
    public class SqsServiceClient : ISqsServiceClient
    {
        private readonly IAmazonSQS _sqsClient;
        private readonly ILogger<SqsServiceClient> _logger;

        public SqsServiceClient(IAmazonSQS sqsClient, ILogger<SqsServiceClient> logger)
        {
            _sqsClient = sqsClient;
            _logger = logger;
        }

        public async Task SendMessage(string queueUrl, string message, CancellationToken cancellationToken)
        {
            ValidateQueueUrlParam(queueUrl);

            try
            {
                var request = new SendMessageRequest
                {
                    QueueUrl = queueUrl,
                    MessageBody = message
                };

                var messageResponse = await _sqsClient.SendMessageAsync(request, cancellationToken);

                _logger.LogInformation("Published message to sqs with MessageId {MessageId}",
                    messageResponse?.MessageId);
            }
            catch (Exception e)
            {
                throw new ExternalException(e.Message);
            }
        }

        public async Task SendMessage(string queueUrl, string message,
            Dictionary<string, string> messageAttributes, CancellationToken cancellationToken)
        {
            ValidateQueueUrlParam(queueUrl);

            try
            {
                var request = new SendMessageRequest
                {
                    QueueUrl = queueUrl,
                    MessageBody = message
                };

                if (messageAttributes != null)
                {
                    request.MessageAttributes = new Dictionary<string, MessageAttributeValue>();

                    foreach (var attribute in messageAttributes)
                    {
                        request.MessageAttributes.Add(attribute.Key, new MessageAttributeValue
                        {
                            DataType = "String",
                            StringValue = attribute.Value
                        });
                    }
                }

                var messageResponse = await _sqsClient.SendMessageAsync(request, cancellationToken);

                _logger.LogInformation("Published message to sqs with MessageId {MessageId}",
                    messageResponse?.MessageId);
            }
            catch (Exception e)
            {
                throw new ExternalException(e.Message);
            }
        }

        public async Task SendMessage(string queueUrl, string message,
            Dictionary<string, string> messageAttributes, int delaySeconds, CancellationToken cancellationToken)
        {
            ValidateQueueUrlParam(queueUrl);

            try
            {
                var request = new SendMessageRequest
                {
                    QueueUrl = queueUrl,
                    MessageBody = message
                };

                if (messageAttributes != null)
                {
                    request.MessageAttributes = new Dictionary<string, MessageAttributeValue>();

                    foreach (var attribute in messageAttributes)
                    {
                        request.MessageAttributes.Add(attribute.Key, new MessageAttributeValue
                        {
                            DataType = "String",
                            StringValue = attribute.Value
                        });
                    }
                }

                request.DelaySeconds = delaySeconds;

                var messageResponse = await _sqsClient.SendMessageAsync(request, cancellationToken);

                _logger.LogInformation("Published message to sqs with MessageId {MessageId}",
                    messageResponse?.MessageId);
            }
            catch (Exception e)
            {
                throw new ExternalException(e.Message);
            }
        }

        private static void ValidateQueueUrlParam(string queueUrl)
        {
            if (string.IsNullOrEmpty(queueUrl)) throw new ArgumentNullException(nameof(queueUrl));
        }
    }
}
