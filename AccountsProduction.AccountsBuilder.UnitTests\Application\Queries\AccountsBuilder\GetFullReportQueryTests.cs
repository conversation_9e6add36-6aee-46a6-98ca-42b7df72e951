﻿using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.PracticeDetails;
using AccountsProduction.AccountsBuilder.Application.Queries.AccountsBuilder;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.NoteModels;
using AccountsProduction.AccountsBuilder.Domain.ProfitShareModels;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;
using Iris.AccountsProduction.Common.Toolkit.Exceptions;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Queries.AccountsBuilder
{
    public class GetFullReportQueryTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repositoryMock;
        private readonly Mock<UserContext> _userContext;
        private readonly IMapper _mapper;

        public GetFullReportQueryTests()
        {
            _repositoryMock = new Mock<IAccountsBuilderRepository>();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _userContext = new Mock<UserContext>();
        }

        [Theory]
        [InlineData("Active", null)]
        [InlineData("Trial", "IRIS Elements Accounts Production Trial Version")]
        public async Task Should_return_response(string licenseStatus, string watermarkText)
        {
            var clientId = Guid.NewGuid();
            var periodId = Guid.NewGuid();
            var tenantId = Guid.NewGuid().ToString();
            _userContext.SetupGet(u => u.TenantId).Returns(tenantId);
            var isTrialAPLicense = licenseStatus.Equals("Trial");

            var reportingStandard = new ReportingStandard
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = "FRS 105",
                Version = ReportingStandardVersion.Full
            };

            var entitySetup = GetEntitySetup();
            var licenseData = new LicenseData(isTrialAPLicense);

            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId, clientId,
                periodId, entitySetup, licenseData, reportingStandard);

            accountsBuilder.UpdateLicenseData(licenseData);
            accountsBuilder.InvolvementsData = new InvolvementsData
            {
                Involvements = new List<Involvement>
                {
                    new Involvement
                    {
                        EndDate = new DateTime(2022, 12, 12),
                        InvolvedClientType = "business",
                        InvolvementClientGuid = TestHelpers.Guids.GuidOne,
                        InvolvementId = 1,
                        InvolvementClientName = "John Doe",
                        InvolvementFirstName = "John",
                        InvolvementSurname = "Doe",
                        InvolvementTitle = "Mr",
                        InvolvementType = "director",
                        PdoCode = 1,
                        IsDeleted = false,
                        StartDate = new DateTime(2021, 12, 12)
                    }
                }
            };

            accountsBuilder.UpdateAccountPeriod(new AccountPeriod
            {
                ClientId = clientId,
                PeriodId = periodId,
                ReviseType = "SupplementaryNote"
            });

            _repositoryMock.Setup(repository => repository.Get(clientId, periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetFullReportQuery
            {
                ClientId = clientId,
                PeriodId = periodId
            };


            var queryHandler = new GetFullReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);

            var response = await queryHandler.Handle(query, CancellationToken.None);

            response.ShouldNotBeNull();

            response.ClientId.ShouldBe(clientId);
            response.PeriodId.ShouldBe(periodId);
            response.TenantId.ShouldBe(tenantId);

            response.ShouldNotBeNull();
            response.AccountPeriod.ShouldNotBeNull();
            response.AccountPeriod.ReviseType.ShouldBe("SupplementaryNote");
            response.EntitySetup.EntitySize.ShouldBe("entitySize");
            response.EntitySetup.IndependentReviewType.ShouldBe("independentReviewType");
            response.EntitySetup.Terminology.ShouldBe("terminology");
            response.EntitySetup.ReportingStandard.ShouldBe("reporting standard");
            response.ReportingStandard.Name.ShouldBe(reportingStandard.Name);
            response.LicenseData.IsTrial.ShouldBe(isTrialAPLicense);
            response.LicenseData.WatermarkText.ShouldBe(watermarkText);

            response.TrialBalance.ShouldNotBeNull();
            response.TrialBalance.ReportingPeriods.ShouldNotBeNull();
            response.LastSuccessfullProcessId.ShouldBeNull();
            response.LastSuccessfullTimeUtc.ShouldBe(DateTime.MinValue);
            response.Status.ShouldBe(Status.Successful);
            response.FinancialData.ShouldNotBeNull();
            response.NonFinancialData.ShouldNotBeNull();


            response.Involvements.Count.ShouldBe(1);
        }

        [Fact]
        public async Task Should_return_reporting_periods()
        {
            var clientId = Guid.NewGuid();
            var periodId = Guid.NewGuid();
            var previousPeriodId = Guid.NewGuid();
            var tenantId = Guid.NewGuid().ToString();
            _userContext.SetupGet(u => u.TenantId).Returns(tenantId);

            var reportingStandard = new ReportingStandard
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = "FRS 105",
                Version = ReportingStandardVersion.Full
            };

            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId, clientId,
                periodId, GetEntitySetup(), null, reportingStandard);

            accountsBuilder.AddTrialBalance(new TrialBalance
            {
                AccountsChartIdentifier = "eltd",
                IsSuccessful = true,
                AccountsChartId = 1,
                PeriodId = Guid.NewGuid(),
                TrialBalances = new List<PeriodTrialBalance>
                {
                    new PeriodTrialBalance
                    {
                        AccountCode = 1,
                        Amount = 2000,
                        Description = "no description",
                        SubAccountCode = 1, Year = DateTime.UtcNow
                    }
                },
                ReportingPeriods = new List<ReportingPeriod>
                {
                    new ReportingPeriod
                    {
                        EndDate = DateTime.UtcNow,
                        Id = periodId
                    },
                    new ReportingPeriod
                    {
                        EndDate = DateTime.UtcNow,
                        Id = previousPeriodId
                    }
                }
            });

            _repositoryMock.Setup(repository => repository.Get(clientId, periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetFullReportQuery
            {
                ClientId = clientId,
                PeriodId = periodId
            };

            var queryHandler = new GetFullReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);

            var response = await queryHandler.Handle(query, CancellationToken.None);

            response.ShouldNotBeNull();
            response.TrialBalance.ShouldNotBe(null);
            response.TrialBalance.ReportingPeriods.ShouldNotBe(null);
            response.PreviousPeriodId.ShouldBe(previousPeriodId);
        }

        [Fact]
        public async Task Should_return_notes()
        {
            var clientId = TestHelpers.Guids.GuidOne;
            var periodId = TestHelpers.Guids.GuidTwo;
            var previousPeriodId = TestHelpers.Guids.GuidOne;
            var tenantId = TestHelpers.Guids.GuidThree;
            _userContext.SetupGet(u => u.TenantId).Returns(tenantId.ToString);

            var reportingStandard = new ReportingStandard
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = "FRS 105",
                Version = ReportingStandardVersion.Full
            };

            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId.ToString(),
                clientId,
                periodId, GetEntitySetup(), null, reportingStandard);

            accountsBuilder.AddNotes(new Notes
            {
                PreviousPeriodId = previousPeriodId,
                PeriodId = periodId,
                ClientId = clientId,
                TenantId = tenantId,
                CorrelationId = TestHelpers.Guids.GuidFour,
                Error = "",
                IsSuccessful = true,
                CurrentPeriod = new NotesData
                {
                    AverageNumberOfEmployees = new AverageNumberOfEmployees
                    {
                        CurrentPeriod = 1,
                        PreviousPeriod = 2
                    },
                    OffBalanceSheetArrangements = "text1",
                    AdvancesCreditAndGuaranteesGrantedToDirectors = "text2",
                    GuaranteesAndOtherFinancialCommitments = "text3",
                    RelatedPartyTransactions = "text4",
                    LoansAndOtherDebtsDueToMembers = "text44",
                    AdditionalNote1 = new AdditionalNote { NoteTitle = "text5", NoteText = "text6" },
                    AdditionalNote2 = new AdditionalNote { NoteTitle = "text7", NoteText = "text8" },
                    ControllingPartyNote = "text9",
                    IntangibleAssetsRevaluation = "text10",
                    OperatingProfitLoss = new OperatingProfitLoss
                    {
                        IsEnabled = true,
                        Items = new List<OperatingProfitLossItem> {
                            new OperatingProfitLossItem { Index = 1, Description = "Test1", Value = 201 },
                            new OperatingProfitLossItem { Index = 2, Description = "Test2", Value = 202.34M },
                            new OperatingProfitLossItem { Index = 3, Description = "Test3", Value = -203.67M },
                    }
                    },
                    AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = new AdvancesCreditAndGuaranteesGrantedToDirectors
                    {
                        Guarantees = "Guarantees",
                        Items = new List<AdvancesCreditAndGuaranteesGrantedToDirectorItem>
                        {
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItem {
                                Index = 1, InvolvementClientGuid = TestHelpers.Guids.GuidFive, DirectorName = "Director1", BalanceOutstandingAtStartOfYear = 1000M, AmountsAdvanced = 100M, AmountsRepaid = 0M, AmountsWrittenOff = 0M, AmountsWaived = 0M, AdvanceCreditConditions = "Director1 AdvanceCreditConditions", BalanceOutstandingAtEndOfYear = 1100M
                            },
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItem {
                                Index = 2, InvolvementClientGuid = TestHelpers.Guids.GuidSix, DirectorName = "Director2", BalanceOutstandingAtStartOfYear = 2000M, AmountsAdvanced = 200M, AmountsRepaid = 0M, AmountsWrittenOff = 0M, AmountsWaived = 0M, AdvanceCreditConditions = "Director2 AdvanceCreditConditions", BalanceOutstandingAtEndOfYear = 2200M
                            },
                        }
                    },
                    TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                    {
                        ValuationInCurrentReportingPeriod = new ValuationInCurrentReportingPeriod
                        {
                            ValuationDetails = "Valuation Details",
                            IndependentValuerInvolved = false,
                            RevaluationBasis = "Revaluation Basis",
                            DateOfRevaluation = new DateTime(2019, 05, 09, 09, 15, 00)
                        },
                        HistoricalCostBreakdown = new HistoricalCostBreakdown
                        {
                            RevaluedAssetClass = "Revalued Asset Class",
                            RevaluedClassPronoun = "Revalued Class Pronoun",
                            CurrentReportingPeriodAccumulatedDepreciation = 1.33m,
                            CurrentReportingPeriodCost = 1.45m,
                        },
                        AnalysisOfCostOrValuation = new AnalysisOfCostOrValuation
                        {
                            AnalysisOfCostOrValuationItems = new List<AnalysisOfCostOrValuationItem>
                            {
                                new AnalysisOfCostOrValuationItem
                                {
                                    Index = 3,
                                    Year = 2019,
                                    LandAndBuildings = 1.1m,
                                    PlantAndMachineryEtc = 2.2m
                                },
                                new AnalysisOfCostOrValuationItem
                                {
                                    Index = 4,
                                    Year = 2020,
                                    LandAndBuildings = 3.3m,
                                    PlantAndMachineryEtc = 4.4m
                                },
                                new AnalysisOfCostOrValuationItem
                                {
                                    Index = 5,
                                    Year = 2021,
                                    LandAndBuildings = -3.3m,
                                    PlantAndMachineryEtc = -4.4m
                                }
                            },
                            CostLandAndBuildings = 5.5m,
                            CostPlantAndMachineryEtc = 6.6m,
                            TotalLandAndBuildings = 11,
                            TotalPlantAndMachineryEtc = 12
                        }
                    }
                }
            });

            _repositoryMock.Setup(repository => repository.Get(clientId, periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetFullReportQuery
            {
                ClientId = clientId,
                PeriodId = periodId
            };

            var queryHandler = new GetFullReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);

            var response = await queryHandler.Handle(query, CancellationToken.None);

            response.ShouldNotBeNull();
            response.Notes.ShouldNotBeNull();
            response.Notes.PreviousPeriodId.ShouldBe(previousPeriodId);
            response.Notes.ClientId.ShouldBe(clientId);
            response.Notes.PeriodId.ShouldBe(periodId);
            response.Notes.PreviousPeriodId.ShouldBe(previousPeriodId);
            response.Notes.CurrentPeriodNotes.AverageNumberOfEmployees.ShouldNotBeNull();
            response.Notes.CurrentPeriodNotes.AverageNumberOfEmployees.CurrentPeriod.ShouldBe(1);
            response.Notes.CurrentPeriodNotes.AverageNumberOfEmployees.PreviousPeriod.ShouldBe(2);
            response.Notes.CurrentPeriodNotes.OffBalanceSheetArrangements.ShouldBe("text1");
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectors.ShouldBe("text2");
            response.Notes.CurrentPeriodNotes.GuaranteesAndOtherFinancialCommitments.ShouldBe("text3");
            response.Notes.CurrentPeriodNotes.RelatedPartyTransactions.ShouldBe("text4");
            response.Notes.CurrentPeriodNotes.LoansAndOtherDebtsDueToMembers.ShouldBe("text44");
            response.Notes.CurrentPeriodNotes.AdditionalNote1.NoteTitle.ShouldBe("text5");
            response.Notes.CurrentPeriodNotes.AdditionalNote1.NoteText.ShouldBe("text6");
            response.Notes.CurrentPeriodNotes.AdditionalNote2.NoteTitle.ShouldBe("text7");
            response.Notes.CurrentPeriodNotes.AdditionalNote2.NoteText.ShouldBe("text8");
            response.Notes.CurrentPeriodNotes.ControllingPartyNote.ShouldBe("text9");
            response.Notes.CurrentPeriodNotes.IntangibleAssetsRevaluation.ShouldBe("text10");
            response.Notes.CurrentPeriodNotes.OperatingProfitLoss.IsEnabled.ShouldBe(true);
            response.Notes.CurrentPeriodNotes.OperatingProfitLoss.Items[0].Index.ShouldBe(1);
            response.Notes.CurrentPeriodNotes.OperatingProfitLoss.Items[0].Description.ShouldBe("Test1");
            response.Notes.CurrentPeriodNotes.OperatingProfitLoss.Items[0].Value.ShouldBe(201);
            response.Notes.CurrentPeriodNotes.OperatingProfitLoss.Items[1].Index.ShouldBe(2);
            response.Notes.CurrentPeriodNotes.OperatingProfitLoss.Items[1].Description.ShouldBe("Test2");
            response.Notes.CurrentPeriodNotes.OperatingProfitLoss.Items[1].Value.ShouldBe(202.34M);
            response.Notes.CurrentPeriodNotes.OperatingProfitLoss.Items[2].Index.ShouldBe(3);
            response.Notes.CurrentPeriodNotes.OperatingProfitLoss.Items[2].Description.ShouldBe("Test3");
            response.Notes.CurrentPeriodNotes.OperatingProfitLoss.Items[2].Value.ShouldBe(-203.67M);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].Index.ShouldBe(1);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].DirectorName.ShouldBe("Director1");
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidFive);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtStartOfYear.ShouldBe(1000M);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsAdvanced.ShouldBe(100M);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsRepaid.ShouldBe(0M);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWaived.ShouldBe(0M);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWrittenOff.ShouldBe(0M);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtEndOfYear.ShouldBe(1100M);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AdvanceCreditConditions.ShouldBe("Director1 AdvanceCreditConditions");
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].Index.ShouldBe(2);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].DirectorName.ShouldBe("Director2");
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidSix);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].BalanceOutstandingAtStartOfYear.ShouldBe(2000M);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsAdvanced.ShouldBe(200M);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsRepaid.ShouldBe(0M);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsWaived.ShouldBe(0M);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsWrittenOff.ShouldBe(0M);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].BalanceOutstandingAtEndOfYear.ShouldBe(2200M);
            response.Notes.CurrentPeriodNotes.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AdvanceCreditConditions.ShouldBe("Director2 AdvanceCreditConditions");
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.ValuationDetails.ShouldBe("Valuation Details");
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.IndependentValuerInvolved.ShouldBeFalse();
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.RevaluationBasis.ShouldBe("Revaluation Basis");
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.DateOfRevaluation.ShouldBe(new DateTime(2019, 05, 09, 09, 15, 00));
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.HistoricalCostBreakdown.RevaluedAssetClass.ShouldBe("Revalued Asset Class");
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.HistoricalCostBreakdown.RevaluedClassPronoun.ShouldBe("Revalued Class Pronoun");
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.HistoricalCostBreakdown.CurrentReportingPeriodAccumulatedDepreciation.ShouldBe(1.33m);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.HistoricalCostBreakdown.CurrentReportingPeriodCost.ShouldBe(1.45m);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems.Count.ShouldBe(3);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].LandAndBuildings.ShouldBe(1.1m);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].PlantAndMachineryEtc.ShouldBe(2.2m);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].Year.ShouldBe(2019);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].Index.ShouldBe(3);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].LandAndBuildings.ShouldBe(3.3m);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].PlantAndMachineryEtc.ShouldBe(4.4m);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].Year.ShouldBe(2020);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].Index.ShouldBe(4);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].LandAndBuildings.ShouldBe(-3.3m);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].PlantAndMachineryEtc.ShouldBe(-4.4m);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].Year.ShouldBe(2021);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].Index.ShouldBe(5);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.CostLandAndBuildings.ShouldBe(5.5m);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.CostPlantAndMachineryEtc.ShouldBe(6.6m);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.TotalLandAndBuildings.ShouldBe(11);
            response.Notes.CurrentPeriodNotes.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.TotalPlantAndMachineryEtc.ShouldBe(12);
        }

        [Fact]
        public async Task Should_return_previous_period_null()
        {
            var clientId = Guid.NewGuid();
            var periodId = Guid.NewGuid();
            var tenantId = Guid.NewGuid().ToString();
            _userContext.SetupGet(u => u.TenantId).Returns(tenantId);

            var reportingStandard = new ReportingStandard
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = "FRS 105",
                Version = ReportingStandardVersion.Full
            };


            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId, clientId,
                periodId, GetEntitySetup(), null, reportingStandard);

            accountsBuilder.AddTrialBalance(new TrialBalance
            {
                AccountsChartIdentifier = "eltd",
                IsSuccessful = true,
                AccountsChartId = 1,
                PeriodId = Guid.NewGuid(),
                TrialBalances = new List<PeriodTrialBalance>
                {
                    new PeriodTrialBalance
                    {
                        AccountCode = 1,
                        Amount = 2000,
                        Description = "no description",
                        SubAccountCode = 1, Year = DateTime.UtcNow
                    }
                },
                ReportingPeriods = new List<ReportingPeriod>
                {
                    new ReportingPeriod
                    {
                        EndDate = DateTime.UtcNow,
                        Id = periodId
                    }
                }
            });

            _repositoryMock.Setup(repository => repository.Get(clientId, periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetFullReportQuery
            {
                ClientId = clientId,
                PeriodId = periodId
            };

            var queryHandler = new GetFullReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);
            var response = await queryHandler.Handle(query, CancellationToken.None);

            response.ShouldNotBeNull();
            response.TrialBalance.ShouldNotBe(null);
            response.TrialBalance.ReportingPeriods.ShouldNotBe(null);
            response.PreviousPeriodId.ShouldBe(Guid.Empty);
        }

        [Fact]
        public async Task Should_return_null_when_period_not_found()
        {
            var clientId = Guid.NewGuid();
            var periodId = Guid.NewGuid();
            var tenantId = Guid.NewGuid().ToString();
            _userContext.SetupGet(u => u.TenantId).Returns(tenantId);

            var reportingStandard = new ReportingStandard
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = "FRS 105",
                Version = ReportingStandardVersion.Full
            };

            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId, clientId,
                periodId, GetEntitySetup(), null, reportingStandard);

            _repositoryMock.Setup(repository => repository.Get(clientId, periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetFullReportQuery
            {
                ClientId = clientId,
                PeriodId = Guid.NewGuid()
            };

            var queryHandler = new GetFullReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);

            var response = await queryHandler.Handle(query, CancellationToken.None);

            response.ShouldBeNull();
        }

        [Fact]
        public async Task Should_return_null_when_client_not_found()
        {
            var clientId = Guid.NewGuid();
            var periodId = Guid.NewGuid();
            var tenantId = Guid.NewGuid().ToString();

            var reportingStandard = new ReportingStandard
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = "FRS 105",
                Version = ReportingStandardVersion.Full
            };

            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId, clientId,
                periodId, GetEntitySetup(), null, reportingStandard);

            _repositoryMock.Setup(repository => repository.Get(clientId, periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetReportQuery
            {
                ClientId = Guid.NewGuid(),
                PeriodId = periodId
            };

            var queryHandler =
                new GetReportQuery.GetReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);

            var response = await queryHandler.Handle(query, CancellationToken.None);

            response.ShouldBeNull();
        }

        [Fact]
        public async Task Should_throw_exception()
        {
            _repositoryMock.Setup(repository =>
                    repository.Get(It.IsAny<Guid>(), It.IsAny<Guid>(), CancellationToken.None))
                .ThrowsAsync(new InternalException());
            var query = new GetReportQuery
            {
                ClientId = Guid.NewGuid(),
                PeriodId = Guid.NewGuid()
            };

            var queryHandler =
                new GetReportQuery.GetReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);

            await Should.ThrowAsync<InternalException>(async () =>
            {
                await queryHandler.Handle(query, CancellationToken.None);
            });
        }

        [Fact]
        public async Task Should_throw_invalid_tenant_exception()
        {
            var clientId = Guid.NewGuid();
            var periodId = Guid.NewGuid();
            var tenantId = Guid.NewGuid().ToString();
            _userContext.SetupGet(u => u.TenantId).Returns(TestHelpers.Guids.GuidOne.ToString);

            var reportingStandard = new ReportingStandard
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = "FRS 105",
                Version = ReportingStandardVersion.Full
            };

            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId, clientId,
                periodId, GetEntitySetup(), null, reportingStandard);

            _repositoryMock.Setup(repository => repository.Get(clientId, periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetFullReportQuery
            {
                ClientId = clientId,
                PeriodId = periodId
            };
            var queryHandler = new GetFullReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);

            await Should.ThrowAsync<InvalidTenantException>(async () =>
            {
                await queryHandler.Handle(query, CancellationToken.None);
            });
        }

        [Fact]
        public async Task Should_return_practice_details()
        {
            var clientId = TestHelpers.Guids.GuidOne;
            var periodId = TestHelpers.Guids.GuidTwo;
            var tenantId = TestHelpers.Guids.GuidThree;
            _userContext.SetupGet(u => u.TenantId).Returns(tenantId.ToString);

            var reportingStandard = new ReportingStandard
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = "FRS 105",
                Version = ReportingStandardVersion.Full
            };

            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId.ToString(),
                clientId,
                periodId, GetEntitySetup(), null, reportingStandard);

            accountsBuilder.AddPracticeDetails(new PracticeDetails
            {
                Name = "Name",
                ReferredType = (int)Referred.Singular,
                SupervisingBody = (int)SupervisoryBody.CAI,
                AddressLine1 = "AddressLine1",
                AddressLine2 = "AddressLine2",
                AddressLine3 = "AddressLine3",
                AddressTown = "AddressTown",
                AddressCounty = "AddressCounty",
                AddressCountry = "AddressCountry",
                AddressPostcode = "AddressPostcode"
            });

            _repositoryMock.Setup(repository => repository.Get(clientId, periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetFullReportQuery
            {
                ClientId = clientId,
                PeriodId = periodId
            };

            var queryHandler = new GetFullReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);

            var response = await queryHandler.Handle(query, CancellationToken.None);

            response.ShouldNotBeNull();
            response.PracticeDetails.ShouldNotBeNull();
            response.PracticeDetails.Name.ShouldBe("Name");
            response.PracticeDetails.ReferredType.ShouldBe((int)Referred.Singular);
            response.PracticeDetails.SupervisingBody.ShouldBe((int)SupervisoryBody.CAI);
            response.PracticeDetails.AddressLine1.ShouldBe("AddressLine1");
            response.PracticeDetails.AddressLine2.ShouldBe("AddressLine2");
            response.PracticeDetails.AddressLine3.ShouldBe("AddressLine3");
            response.PracticeDetails.AddressTown.ShouldBe("AddressTown");
            response.PracticeDetails.AddressCounty.ShouldBe("AddressCounty");
            response.PracticeDetails.AddressCountry.ShouldBe("AddressCountry");
            response.PracticeDetails.AddressPostcode.ShouldBe("AddressPostcode");
        }

        [Fact]
        public async Task Should_return_partner_profit_share_details()
        {
            var clientId = TestHelpers.Guids.GuidOne;
            var periodId = TestHelpers.Guids.GuidTwo;
            var tenantId = TestHelpers.Guids.GuidThree;
            _userContext.SetupGet(u => u.TenantId).Returns(tenantId.ToString);

            var reportingStandard = new ReportingStandard
            {
                Id = "123",
                Name = ReportStandardType.UNINCORPORATED,
                Type = ReportStandardType.UNINCORPORATED,
                Version = ReportingStandardVersion.Full
            };

            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId.ToString(),
                clientId,
                periodId, GetEntitySetup(), null, reportingStandard);

            accountsBuilder.AddPartnerProfitShares(new ProfitShareData
            {
                IsSuccessful = true,
                ProfitShares = new List<ProfitShare>
                 {
                     new()
                     {
                         InvolvementId = 0,
                         CumulativeAmount = 10000,
                         AccountPeriodId = periodId
                     },
                     new()
                     {
                         InvolvementId = 10000,
                         CumulativeAmount = 10000,
                         AccountPeriodId = periodId
                     }
                 }
            });

            _repositoryMock.Setup(repository => repository.Get(clientId, periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetFullReportQuery
            {
                ClientId = clientId,
                PeriodId = periodId
            };

            var queryHandler = new GetFullReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);

            var response = await queryHandler.Handle(query, CancellationToken.None);

            response.ShouldNotBeNull();
            response.ProfitShareData.ShouldNotBeNull();
            response.ProfitShareData.ProfitShares.Count.ShouldBe(2);
            response.ProfitShareData.ProfitShares[0].InvolvementId.ShouldBe(0);
            response.ProfitShareData.ProfitShares[1].InvolvementId.ShouldBe(10000);
        }


        [Fact]
        public async Task Should_return_accounting_policies()
        {
            var clientId = TestHelpers.Guids.GuidOne;
            var periodId = TestHelpers.Guids.GuidTwo;
            var tenantId = TestHelpers.Guids.GuidThree;
            _userContext.SetupGet(u => u.TenantId).Returns(tenantId.ToString);

            var reportingStandard = new ReportingStandard
            {
                Id = "123",
                Name = "FRS 1021A - Full",
                Type = "FRS 1021A",
                Version = ReportingStandardVersion.Full
            };


            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId.ToString(),
                clientId,
                periodId, GetEntitySetup(), null, reportingStandard);

            accountsBuilder.AddAccountingPolicies(TestData.AbFRS1021ACompleted.AccountingPolicies);
            _repositoryMock.Setup(repository => repository.Get(clientId, periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetFullReportQuery
            {
                ClientId = clientId,
                PeriodId = periodId
            };
            var queryHandler = new GetFullReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);
            var response = await queryHandler.Handle(query, CancellationToken.None);

            var expectedResult = TestData.AbFRS1021ACompleted.AccountingPolicies.CurrentPeriodAccountingPolicies;

            response.ShouldNotBeNull();
            response.AccountingPolicies.ShouldNotBeNull();
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.ChangesInAccountingPolicies.ShouldBe(expectedResult.ChangesInAccountingPolicies);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.FinancialInstrumentsAccountingPolicy.ShouldBe(expectedResult.FinancialInstrumentsAccountingPolicy);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.GovernmentGrantsAccountingPolicy.ShouldBe(expectedResult.GovernmentGrantsAccountingPolicy);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.MembersTransactionsWithTheLlpText.ShouldBe(expectedResult.MembersTransactionsWithTheLlpText);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.ForeignCurrencies.ShouldBe(expectedResult.ForeignCurrencies);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.PresentationCurrency.ShouldBe(expectedResult.PresentationCurrency);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.ResearchAndDevelopment.ShouldBe(expectedResult.ResearchAndDevelopment);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.ExemptionsFinancialStatements.ParentAddress.ShouldBe(expectedResult.ExemptionsFinancialStatements.ParentAddress);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.ExemptionsFinancialStatements.ParentName.ShouldBe(expectedResult.ExemptionsFinancialStatements.ParentName);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.ExemptionsFinancialStatements.Section.ShouldBeOfType(typeof(ExemptionsFinancialStatementsEnum));
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.ExemptionsFinancialStatements.Section.ShouldBe(ExemptionsFinancialStatementsEnum.Section_401);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.CategoryDescription.ShouldBe(expectedResult.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.CategoryDescription);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.AlternativeBasis.ShouldBe(expectedResult.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.AlternativeBasis);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.ReducingBalanceBasis.ShouldBe(1);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.StraightLineBasis.ShouldBe(1);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.CategoryDescription.ShouldBe(expectedResult.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.CategoryDescription);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.AlternativeBasis.ShouldBe(expectedResult.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.AlternativeBasis);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.ReducingBalanceBasis.ShouldBe(2);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.StraightLineBasis.ShouldBe(2);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.CategoryDescription.ShouldBe(expectedResult.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.CategoryDescription);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.AlternativeBasis.ShouldBe(expectedResult.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.AlternativeBasis);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.ReducingBalanceBasis.ShouldBe(3);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.StraightLineBasis.ShouldBe(3);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.CategoryDescription.ShouldBe(expectedResult.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.CategoryDescription);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.AlternativeBasis.ShouldBe(expectedResult.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.AlternativeBasis);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.ReducingBalanceBasis.ShouldBe(4);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.StraightLineBasis.ShouldBe(4);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.CategoryDescription.ShouldBe(expectedResult.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.CategoryDescription);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.AlternativeBasis.ShouldBe(expectedResult.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.AlternativeBasis);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.ReducingBalanceBasis.ShouldBe(5);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.StraightLineBasis.ShouldBe(5);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.CategoryDescription.ShouldBe(expectedResult.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.CategoryDescription);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.AlternativeBasis.ShouldBe(expectedResult.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.AlternativeBasis);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.ReducingBalanceBasis.ShouldBe(6);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.StraightLineBasis.ShouldBe(6);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.CategoryDescription.ShouldBe(expectedResult.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.CategoryDescription);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.AlternativeBasis.ShouldBe(expectedResult.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.AlternativeBasis);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.ReducingBalanceBasis.ShouldBe(7);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.StraightLineBasis.ShouldBe(7);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.CategoryDescription.ShouldBe(expectedResult.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.CategoryDescription);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.AlternativeBasis.ShouldBe(expectedResult.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.AlternativeBasis);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.ReducingBalanceBasis.ShouldBe(8);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.StraightLineBasis.ShouldBe(8);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.Goodwill.CategoryDescription.ShouldBe(expectedResult.IntangibleAssets.Goodwill.CategoryDescription);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.Goodwill.AlternativeBasis.ShouldBe(expectedResult.IntangibleAssets.Goodwill.AlternativeBasis);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.Goodwill.ReducingBalanceBasis.ShouldBe(1);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.Goodwill.StraightLineBasis.ShouldBe(1);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.CategoryDescription.ShouldBe(expectedResult.IntangibleAssets.PatentsAndLicenses.CategoryDescription);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.AlternativeBasis.ShouldBe(expectedResult.IntangibleAssets.PatentsAndLicenses.AlternativeBasis);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.ReducingBalanceBasis.ShouldBe(2);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.StraightLineBasis.ShouldBe(2);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.CategoryDescription.ShouldBe(expectedResult.IntangibleAssets.DevelopmentCosts.CategoryDescription);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.AlternativeBasis.ShouldBe(expectedResult.IntangibleAssets.DevelopmentCosts.AlternativeBasis);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.ReducingBalanceBasis.ShouldBe(3);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.StraightLineBasis.ShouldBe(3);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.CategoryDescription.ShouldBe(expectedResult.IntangibleAssets.ComputerSoftware.CategoryDescription);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.AlternativeBasis.ShouldBe(expectedResult.IntangibleAssets.ComputerSoftware.AlternativeBasis);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.ReducingBalanceBasis.ShouldBe(4);
            response.AccountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.StraightLineBasis.ShouldBe(4);
        }

        [Fact]
        public async Task Should_return_accountperiod()
        {
            var clientId = TestHelpers.Guids.GuidOne;
            var periodId = TestHelpers.Guids.GuidTwo;
            var tenantId = TestHelpers.Guids.GuidThree;
            _userContext.SetupGet(u => u.TenantId).Returns(tenantId.ToString);

            var reportingStandard = new ReportingStandard
            {
                Id = "123",
                Name = ReportStandardType.UNINCORPORATED,
                Type = ReportStandardType.UNINCORPORATED,
                Version = ReportingStandardVersion.Full
            };

            var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(tenantId.ToString(),
                clientId,
                periodId, GetEntitySetup(), null, reportingStandard);

            accountsBuilder.UpdateAccountPeriod(new AccountPeriod
            {
                ClientId = clientId,
                PeriodId = periodId,
                ReviseType = "SupplementaryNote",
            });

            _repositoryMock.Setup(repository => repository.Get(clientId, periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var query = new GetFullReportQuery
            {
                ClientId = clientId,
                PeriodId = periodId
            };

            var queryHandler = new GetFullReportQueryHandler(_repositoryMock.Object, _mapper, _userContext.Object);

            var response = await queryHandler.Handle(query, CancellationToken.None);

            response.ShouldNotBeNull();
            response.AccountPeriod.ShouldNotBeNull();
            response.AccountPeriod.ClientId.ToString().ShouldBe(clientId.ToString());
            response.AccountPeriod.PeriodId.ToString().ShouldBe(periodId.ToString());
            response.AccountPeriod.ReviseType.ShouldBe("SupplementaryNote");
        }

        private static EntitySetup GetEntitySetup()
        {
            return new EntitySetup
            {
                EntitySize = "entitySize",
                IndependentReviewType = "independentReviewType",
                Terminology = "terminology",
                ReportingStandard = "reporting standard",
                DormantStatus = "dormant status"
            };
        }
    }
}
