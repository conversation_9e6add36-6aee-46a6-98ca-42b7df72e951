﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.Note.Contract;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.Common
{
    public static class ListExtensions
    {
        public static bool IsPopulated<T>(this List<T> source)
        {
            if (source == null) return false;
            if (!source.Any() || source.TrueForAll(q => q == null)) return false;
            return true;
        }

        public static bool HasLineItems<T>(this List<T> source)
        {
            if (!IsPopulated(source)) return false;

            var properties = source[0]!.GetType().GetProperties().Where(s => s.PropertyType == typeof(FinancialDataCategoryMessage)).Select(x => x).ToList();

            foreach (var item in source)
            {
                foreach (var property in properties)
                {
                    var propertyInfo = item?.GetType().GetProperty(property.Name);
                    var value = propertyInfo?.GetValue(item, null) as FinancialDataCategoryMessage;
                    if (value?.DrilldownData != null && value.DrilldownData.Any())
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        public static List<T> PopulateWhenNotePresent<T>(this List<T> source, string type, string? text) where T : NoteBase, new()
        {
            if (!string.IsNullOrEmpty(text))
            {
                source.Add(new T
                {
                    NoteType = type,
                    NoteText = text
                });
            }

            return source;
        }

        public static List<T> PopulateWhenNotePresent<T>(this List<T> source, string type, string? text, string? title) where T : NoteBase, new()
        {
            if (!string.IsNullOrEmpty(title) || !string.IsNullOrEmpty(text))
            {
                source.Add(new T
                {
                    NoteType = type,
                    NoteText = text,
                    NoteTitle = title
                });
            }

            return source;
        }

        public static List<T> PopulateWhenNotePresent<T>(this List<T> source, string type, string text, decimal? value) where T : NoteBase, new()
        {
            if (!string.IsNullOrEmpty(text) || value.HasValue)
            {
                source.Add(new T
                {
                    NoteType = type,
                    NoteText = text,
                    NoteValue = value
                });
            }

            return source;
        }

        public static List<T> PopulateWhenNotePresent<T>(this List<T> source, string type, decimal? value) where T : NoteBase, new()
        {
            if (value != null)
            {
                source.Add(new T
                {
                    NoteType = type,
                    NoteValue = value
                });
            }

            return source;
        }
    }
}
