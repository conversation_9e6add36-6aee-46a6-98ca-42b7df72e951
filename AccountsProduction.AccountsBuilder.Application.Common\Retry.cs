﻿using Amazon.DynamoDBv2.Model;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Application.Common
{
    public static class Retry
    {
        public static async Task RunAndHandleConcurrency(Func<Task> action, ILogger logger)
        {
            await RunAndHandleConcurrency(action, async () => await Task.CompletedTask, logger);
        }

        public static async Task RunAndHandleConcurrency(Func<Task> action, Func<Task> onFailureAction, ILogger logger)
        {
            const int defaultRetryCount = 3;

            var variable = Environment.GetEnvironmentVariable("CONCURRENCY_RETRY_COUNT");

            if (!int.TryParse(variable, out var retryCount))
            {
                retryCount = defaultRetryCount;
            }
            var currentRetry = 0;

            while (true)
            {
                try
                {
                    await action();
                    break;
                }
                catch (ConditionalCheckFailedException checkFailedException)
                {
                    logger.LogWarning(checkFailedException, "Concurrency issue detected.");
                    await onFailureAction();

                    currentRetry++;

                    if (currentRetry > retryCount)
                    {
                        throw;
                    }
                }
            }
        }
    }
}