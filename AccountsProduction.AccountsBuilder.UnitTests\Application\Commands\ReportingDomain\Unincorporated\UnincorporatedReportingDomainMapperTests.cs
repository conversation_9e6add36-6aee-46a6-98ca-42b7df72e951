﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.Unincorporated;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using DeepEqual.Syntax;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Iris.AccountsProduction.Common.Toolkit.Utils;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.ReportingDomain.Unincorporated
{
    public class UnincorporatedReportingDomainMapperTests
    {
        private readonly Mock<UserContext> _userContext;
        private readonly IMapper _mapper;

        public UnincorporatedReportingDomainMapperTests()
        {
            _userContext = new Mock<UserContext>();
            _userContext.Setup(x => x.TenantId).Returns(TestHelpers.Guids.GuidFour.ToString());
            _userContext.Setup(x => x.CorrelationId).Returns(TestHelpers.Guids.GuidFive.ToString());
            _userContext.Setup(x => x.UserId).Returns(TestHelpers.Guids.GuidSix.ToString());
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
        }

        [Theory]
        [InlineData("Active", null)]
        [InlineData("Trial", "IRIS Elements Accounts Production Trial Version")]
        public void Should_map_correct_data(string licenseStatus, string? watermarkText)
        {
            var isTrialAPLicense = licenseStatus.Equals("Trial");

            _userContext.Setup(x => x.Licenses).Returns(new System.Collections.Generic.List<Iris.Platform.WebApi.Infrastructure.Licenses.License>
                {
                    new Iris.Platform.WebApi.Infrastructure.Licenses.License() { Code = APLicense.Name, IsTrial = isTrialAPLicense}
                });

            var entitySetup = new EntitySetup();
            var reportingStandardDetail = new ReportingStandard
            {
                Id = "62fe4ee96d17f93b787c23c0",
                Name = "Unincorporated",
                Type = "Unincorporated",
                Version = ReportingStandardVersion.Full
            };

            var licenseData = new LicenseData(isTrialAPLicense);
            var accountsBuilder = ReportingDomainMapperTestData.GetAccountsBuilderData(BusinessTypes.PartnershipBusinessType, entitySetup, reportingStandardDetail);
            accountsBuilder.EntitySetup = new EntitySetup { IndependentReviewType = "Accountants" };
            accountsBuilder.UpdateLicenseData(licenseData);
            var uincorporatedReportingDomainMapper = new UnincorporatedReportingDomainMapper(_userContext.Object, _mapper);

            var message = (UnincorporatedReportingMessage)uincorporatedReportingDomainMapper.Map(accountsBuilder);

            var expectedProfitAndLoss = _mapper.Map<ProfitAndLossMessage>(accountsBuilder.FinancialData.Financials.First());
            var expectedBalanceSheet = _mapper.Map<BalanceSheetMessage>(accountsBuilder.FinancialData.Financials.First());


            message.ReportType.ShouldBe(ReportStandardType.UNINCORPORATED);
            message.ClientId.ShouldBe(accountsBuilder.ClientId);
            message.PeriodId.ShouldBe(accountsBuilder.PeriodId);
            message.TenantId.ShouldBe(accountsBuilder.TenantId);
            message.WatermarkText.ShouldBe(watermarkText);

            message.ProfitAndLossData.First().PeriodId.ShouldBe(TestHelpers.Guids.GuidSeven);
            message.ProfitAndLossData.First().Period.ShouldBe(new DateTime(2022, 2, 2));
            message.ProfitAndLossData.First().WithDeepEqual(expectedProfitAndLoss).IgnoreDestinationProperty(x => x.PeriodId).Assert();

            message.BalanceSheetData.First().PeriodId.ShouldBe(TestHelpers.Guids.GuidSeven);
            message.BalanceSheetData.First().Period.ShouldBe(new DateTime(2022, 2, 2));
            message.BalanceSheetData.First().WithDeepEqual(expectedBalanceSheet).IgnoreDestinationProperty(x => x.PeriodId).Assert();

            message.Signatures.AccountantSigningDate.ShouldBe(new DateTime(2022, 3, 3));
            message.Signatures.IncludeAccountantsReport?.ShouldBeTrue();
            message.Signatures.Signatures.First().SignatureType.ShouldBe(SignatureType.BalanceSheet);
            message.Signatures.Signatures.First().SignatureDate.ShouldBe(new DateTime(2022, 3, 3));
            message.Signatures.Signatures.First().DisplayOrder.ShouldBe(1);
            message.Signatures.Signatures.First().InvolvementUUID.ShouldBe(TestHelpers.Guids.GuidSeven);
            message.Signatures.Signatures.First().InvolvementType.ShouldBe("Partner");
            message.Signatures.Signatures.First().SignatoryTitle.ShouldBe("Mr");
            message.Signatures.Signatures.First().SignatoryFirstName.ShouldBe("Bob");
            message.Signatures.Signatures.First().SignatorySurname.ShouldBe("Smith");
            message.Signatures.Signatures.Last().SignatureType.ShouldBe(SignatureType.BalanceSheet);
            message.Signatures.Signatures.Last().SignatureDate.ShouldBeNull();
            message.Signatures.Signatures.Last().DisplayOrder.ShouldBe(1);
            message.Signatures.Signatures.Last().InvolvementUUID.ShouldBe(TestHelpers.Guids.GuidNine);
            message.Signatures.Signatures.Last().InvolvementType.ShouldBe("Partner");
            message.Signatures.Signatures.Last().SignatoryTitle.ShouldBe("Mr");
            message.Signatures.Signatures.Last().SignatoryFirstName.ShouldBe("Smith");
            message.Signatures.Signatures.Last().SignatorySurname.ShouldBe("Bob");

            message.ClientData.CompanyName.ShouldBe("CompanyName");
            message.ClientData.CompanyType.ShouldBe(BusinessTypes.PartnershipBusinessType);

            message.Involvements.First().InvolvementTitle.ShouldBe("Mr");
            message.Involvements.First().InvolvementFirstName.ShouldBe("Bob");
            message.Involvements.First().InvolvementSurname.ShouldBe("Smith");
            message.Involvements.First().InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidSeven);
            message.Involvements.First().InvolvementType.ShouldBe("Partner");
            message.Involvements.Last().InvolvementTitle.ShouldBe("Mr");
            message.Involvements.Last().InvolvementFirstName.ShouldBe("Smith");
            message.Involvements.Last().InvolvementSurname.ShouldBe("Bob");
            message.Involvements.Last().InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidNine);
            message.Involvements.Last().InvolvementType.ShouldBe("Partner");

            message.ReportingPeriods.First().Id.ShouldBe(TestHelpers.Guids.GuidSeven);
            message.ReportingPeriods.First().EndDate.ShouldBe(new DateTime(2022, 2, 2));

            message.PracticeDetails.AddressTown.ShouldBe("town");
            message.PracticeDetails.AddressCountry.ShouldBe("country");

            message.EntitySetup.IndependentReviewType.ShouldBe("Accountants");

            message.ProfitShareData.ProfitShares.Count.ShouldBe(1);
            message.ProfitShareData.ProfitShares[0].InvolvementId.ShouldBe(0);
            message.ProfitShareData.ProfitShares[0].CumulativeAmount.ShouldBe(100);
            message.ProfitShareData.ProfitShares[0].AccountPeriodId.ShouldBe(TestHelpers.Guids.GuidThree);

            message.DataScreenValue.ClientId.ShouldBe(TestHelpers.Guids.GuidOne);
            message.DataScreenValue.PeriodId.ShouldBe(TestHelpers.Guids.GuidTwo);
            message.DataScreenValue.PreviousPeriodId.ShouldBe(TestHelpers.Guids.GuidThree);
            message.DataScreenValue.TenantId.ShouldBe(TestHelpers.Guids.GuidFour);
            message.DataScreenValue.CurrentPeriod.Count.ShouldBe(2);
            message.DataScreenValue.PreviousPeriod.Count.ShouldBe(2);

            message.AccountPeriod.ReviseType.ShouldBe("SupplementaryNote");
        }
    }
}