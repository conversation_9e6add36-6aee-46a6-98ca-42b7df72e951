﻿using AccountsProduction.AccountsBuilder.Application.Reporting.Common;
using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Application.Commands;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Application.Dto;
using Iris.AccountsProduction.AccountsBuilder.Messages.AccountPeriod;
using Iris.AccountsProduction.AccountsBuilder.Messages.DataScreenValue;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;
using Iris.AccountsProduction.AccountsBuilder.Messages.ProfitShare;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.StaticData
{
    public static class Data
    {
        public static Guid ClientId = TestHelpers.Guids.GuidNine;
        public static Guid TenantId = TestHelpers.Guids.GuidEight;
        public static Guid ProcessId = TestHelpers.Guids.GuidSeven;
        public static Guid CurrentPeriodId = TestHelpers.Guids.GuidTwo;
        public static Guid PreviousPeriodId = TestHelpers.Guids.GuidOne;
        public static DateTime CurrentPeriodStartDate = new DateTime(2020, 1, 1);
        public static DateTime PreviousPeriodStartDate = new DateTime(2019, 1, 1);
        public static DateTime CurrentPeriodEndDate = new DateTime(2021, 1, 1);
        public static DateTime PreviousPeriodEndDate = new DateTime(2020, 1, 1);
        public static readonly EntitySetupDto EntitySetupDto = new EntitySetupDto
        {
            ReportingStandard = "FRS102 1A",
            EntitySize = "Small",
            Terminology = null,
            TradingStatus = "Trading",
            ChoiceOfStatement = null,
            DormantStatus = null,
            IndependentReviewType = "Accountants"
        };

        public static List<BalanceSheetMessage> GetBalanceSheetData()
        {
            return new List<BalanceSheetMessage>
            {
                GetBalanceSheetMessage(CurrentPeriodId),
                GetBalanceSheetMessage(PreviousPeriodId)
            };
        }

        public static ClientDto GetClientData()
        {
            return new ClientDto
            {
                CompanyName = "CompanyName",
                CompanyRegistrationNumber = "CompanyRegistrationNumber",
                CompanySubType = "CompanySubtype",
                CompanyType = "CompanyType",
                CompanyCategory = "CompanyCategory",
                Addresses = new ClientAddressDto
                {
                    MainAddress = new AddressDto { County = "country", PostCode = "postcode", Town = "town", Line1 = "line1", Line2 = "line2" },
                    RegisteredAddress = new AddressDto { County = "country2", PostCode = "postcode2", Town = "town2", Line1 = "line11", Line2 = "line22" }
                }
            };
        }

        public static ClientDto GetLlpClientData()
        {
            return new ClientDto
            {
                CompanyName = "CompanyName",
                CompanyRegistrationNumber = "CompanyRegistrationNumber",
                CompanySubType = "CompanySubtype",
                CompanyCategory = "CompanyCategory",
                CompanyType = CompanyTypes.LlpBusinessType,
                Addresses = new ClientAddressDto
                {
                    MainAddress = new AddressDto { County = "country", PostCode = "postcode", Town = "town", Line1 = "line1", Line2 = "line2" },
                    RegisteredAddress = new AddressDto { County = "country2", PostCode = "postcode2", Town = "town2", Line1 = "line11", Line2 = "line22" }
                }
            };
        }

        public static InvolvementDto GetInvolvement()
        {
            return new InvolvementDto
            {
                InvolvementClientGuid = TestHelpers.Guids.GuidThree,
                InvolvedClientType = "InvolvedClientType",
                InvolvementClientName = "InvolvementClientName",
                InvolvementType = "InvolvementType",
                InvolvementSurname = "InvolvementSurname",
                InvolvementTitle = "InvolvementTitle",
                InvolvementFirstName = "InvolvementFirstName",
                StartDate = new DateTime(2020, 1, 1),
                EndDate = new DateTime(2022, 1, 1),
                InvolvedDateOfDeath = new DateTime(2022, 1, 1),
                PdoCode = 1,
                IsDeleted = false
            };
        }

        public static List<ProfitAndLossMessage> GetProfitAndLossData()
        {
            return new List<ProfitAndLossMessage>
            {
                GetProfitAndLossMessage(CurrentPeriodId, CurrentPeriodEndDate),
                GetProfitAndLossMessage(PreviousPeriodId, PreviousPeriodEndDate)
            };
        }

        public static List<PeriodDto> GetReportingPeriods()
        {
            return new List<PeriodDto>
            {
                new PeriodDto
                {
                    StartDate = CurrentPeriodStartDate,
                    EndDate = CurrentPeriodEndDate,
                    Id = CurrentPeriodId
                },
                new PeriodDto
                {
                    StartDate = PreviousPeriodStartDate,
                    EndDate = PreviousPeriodEndDate,
                    Id = PreviousPeriodId
                }
            };
        }

        public static SignatureDto GetSignatures()
        {
            return new SignatureDto
            {
                AccountantSigningDate = new DateTime(),
                Signatures = new List<SignatureDetailDto>
                {
                    new SignatureDetailDto
                    {
                        SignatoryTitle = "Mr",
                        SignatorySurname = "Carter",
                        SignatoryFirstName = "Adam",
                        SignatureDate = new DateTime(2022, 2, 2),
                        DisplayOrder = 1,
                        SignatureType = SignatureType.BalanceSheet,
                        InvolvementUUID = TestHelpers.Guids.GuidOne,
                        InvolvementType = "Director"
                    },
                    new SignatureDetailDto
                    {
                        SignatoryTitle = "Mr",
                        SignatorySurname = "Carter",
                        SignatoryFirstName = "Ben",
                        SignatureDate = new DateTime(2022, 2, 2),
                        DisplayOrder = 2,
                        SignatureType = SignatureType.BalanceSheet,
                        InvolvementUUID = TestHelpers.Guids.GuidTwo,
                        InvolvementType = "Secretary"
                    },
                    new SignatureDetailDto
                    {
                        SignatoryTitle = "Mr",
                        SignatorySurname = "Carter",
                        SignatoryFirstName = "Jack",
                        SignatureDate = new DateTime(2022, 2, 3),
                        DisplayOrder = 1,
                        SignatureType = SignatureType.DirectorsReport,
                        InvolvementUUID = TestHelpers.Guids.GuidThree,
                        InvolvementType = "Director"
                    },
                    new SignatureDetailDto
                    {
                        SignatoryTitle = "Mr",
                        SignatorySurname = "Carter",
                        SignatoryFirstName = "Jones",
                        SignatureDate = new DateTime(2022, 2, 4),
                        DisplayOrder = 3,
                        SignatureType = SignatureType.CIC34Report,
                        InvolvementUUID = TestHelpers.Guids.GuidFour,
                        InvolvementType = "Director"
                    }
                }
            };
        }

        public static NotesResponseDataMessage GetNotes()
        {
            return new NotesResponseDataMessage
            {

                AverageNumberOfEmployees = new AverageNumberOfEmployeesMessage
                {
                    PreviousPeriod = 1,
                    CurrentPeriod = 2
                },
                OperatingProfitLoss = new OperatingProfitLossMessage
                {
                    IsEnabled = true,
                    Items = new List<OperatingProfitLossItemMessage>
                        {
                            new OperatingProfitLossItemMessage
                            {
                                Index = 1,
                                Description = "Description1",
                                Value = 1
                            }
                        }
                },
                IntangibleAssetsRevaluation = "IntangibleAssetsRevaluation",
                TangibleFixedAssetsNotes = new TangibleFixedAssetsNotesMessage
                {
                    ValuationInCurrentReportingPeriod = new ValuationInCurrentReportingPeriodMessage
                    {
                        ValuationDetails = "Valuation Details long message",
                        IndependentValuerInvolved = true,
                        RevaluationBasis = "Revaluation Basis short message",
                        DateOfRevaluation = new DateTime(2019, 05, 09, 09, 15, 00)

                    },
                    HistoricalCostBreakdown = new HistoricalCostBreakdownMessage
                    {
                        RevaluedAssetClass = "Revalued Asset Class",
                        RevaluedClassPronoun = "Revalued Class Pronoun",
                        CurrentReportingPeriodAccumulatedDepreciation = (decimal)1.33,
                        CurrentReportingPeriodCost = (decimal)1.45,
                    },
                    AnalysisOfCostOrValuation = new AnalysisOfCostOrValuationMessage
                    {
                        AnalysisOfCostOrValuationItems = new System.Collections.Generic.List<AnalysisOfCostOrValuationItemMessage>
                            {
                                new AnalysisOfCostOrValuationItemMessage
                                {
                                    Index = 1,
                                    Year = 2020,
                                    LandAndBuildings = (decimal)1.1,
                                    PlantAndMachineryEtc = (decimal)2.2
                                },
                                new AnalysisOfCostOrValuationItemMessage
                                {
                                    Index = 3,
                                    Year = 2021,
                                    LandAndBuildings = (decimal)3.3,
                                    PlantAndMachineryEtc = (decimal)4.4
                                },
                                new AnalysisOfCostOrValuationItemMessage
                                {
                                    Index = 4,
                                    Year = 2022,
                                    LandAndBuildings = (decimal)-3.3,
                                    PlantAndMachineryEtc = (decimal)-4.4
                                }
                            },
                        CostLandAndBuildings = (decimal)5.5,
                        CostPlantAndMachineryEtc = (decimal)6.6,
                        TotalLandAndBuildings = 10,
                        TotalPlantAndMachineryEtc = 11
                    }
                },
                AdditionalNote1 = new AdditionalNoteMessage
                {
                    NoteTitle = "AdditionalNote1Title",
                    NoteText = "AdditionalNote1Text"
                },
                AdditionalNote2 = new AdditionalNoteMessage
                {
                    NoteTitle = "AdditionalNote2Title",
                    NoteText = "AdditionalNote2Text"
                },
            };
        }

        public static AccountingPoliciesResponseDataMessage GetAccountingPoliciesNotes()
        {
            return new AccountingPoliciesResponseDataMessage
            {
                ExemptionsFinancialStatements = new ExemptionsFinancialStatementsMessage
                {
                    Section = ExemptionsFinancialStatementsEnum.Section_400,
                    ParentName = "ParentName",
                    ParentAddress = "ParentAddress"
                },
                GoodwillMaterial = true,
                ChangesInAccountingPolicies = "ChangesInAccountingPolicies",
                FinancialInstrumentsAccountingPolicy = "FinancialInstrumentsAccountingPolicy",
                GovernmentGrantsAccountingPolicy = "GovernmentGrantsAccountingPolicy",
                MembersTransactionsWithTheLlpText = "MembersTransactionsWithTheLlpText",
                ForeignCurrencies = true,
                PresentationCurrency = true,
                ResearchAndDevelopment = true,
                TangibleFixedAssets = new TangibleFixedAssetsMessage
                {
                    PlantAndMachinery = new PlantAndMachineriesMessage
                    {
                        ClassNameCustomization = "ClassNameCustomization",
                        PlantAndMachinery = new AssetsAdjustmentMessage
                        {
                            AlternativeBasis = "PlantAndMachineryAlternativeBasis",
                            CategoryDescription = "PlantAndMachineryCategoryDescription",
                            ReducingBalanceBasis = 1,
                            StraightLineBasis = 11
                        },
                        ComputerEquipment = new AssetsAdjustmentMessage
                        {
                            AlternativeBasis = "ComputerEquipmentAlternativeBasis",
                            CategoryDescription = "ComputerEquipmentCategoryDescription",
                            ReducingBalanceBasis = 2,
                            StraightLineBasis = 22
                        },
                        FixturesAndFittings = new AssetsAdjustmentMessage
                        {
                            AlternativeBasis = "FixturesAndFittingsAlternativeBasis",
                            CategoryDescription = "FixturesAndFittingsCategoryDescription",
                            ReducingBalanceBasis = 3,
                            StraightLineBasis = 33
                        },
                        ImprovementsToProperty = new AssetsAdjustmentMessage
                        {
                            AlternativeBasis = "ImprovementsToPropertyAlternativeBasis",
                            CategoryDescription = "ImprovementsToPropertyCategoryDescription",
                            ReducingBalanceBasis = 4,
                            StraightLineBasis = 44
                        },
                        MotorVehicles = new AssetsAdjustmentMessage
                        {
                            AlternativeBasis = "MotorVehiclesAlternativeBasis",
                            CategoryDescription = "MotorVehiclesCategoryDescription",
                            ReducingBalanceBasis = 5,
                            StraightLineBasis = 55
                        }
                    },
                    LandAndBuildings = new LandAndBuildingsMessage
                    {
                        ClassNameCustomization = "ClassNameCustomization",
                        FreeholdProperty = new AssetsAdjustmentMessage
                        {
                            AlternativeBasis = "FreeholdPropertyAlternativeBasis",
                            CategoryDescription = "FreeholdPropertyCategoryDescription",
                            ReducingBalanceBasis = 6,
                            StraightLineBasis = 66
                        },
                        ShortLeaseholdProperty = new AssetsAdjustmentMessage
                        {
                            AlternativeBasis = "ShortLeaseholdPropertyAlternativeBasis",
                            CategoryDescription = "ShortLeaseholdPropertyCategoryDescription",
                            ReducingBalanceBasis = 7,
                            StraightLineBasis = 77
                        },
                        LongLeaseholdProperty = new AssetsAdjustmentMessage
                        {
                            AlternativeBasis = "LongLeaseholdPropertyAlternativeBasis",
                            CategoryDescription = "LongLeaseholdPropertyCategoryDescription",
                            ReducingBalanceBasis = 8,
                            StraightLineBasis = 88
                        }
                    }
                },
                IntangibleAssets = new IntangibleAssetsMessage
                {
                    Goodwill = new AssetsAdjustmentMessage
                    {
                        AlternativeBasis = "GoodwillAmortisationPolicy",
                        CategoryDescription = "GoodwillDescription",
                        ReducingBalanceBasis = 6,
                        StraightLineBasis = 66
                    },
                    PatentsAndLicenses = new AssetsAdjustmentMessage
                    {
                        AlternativeBasis = "PatentsAndLicencesAmortisationPolicy",
                        CategoryDescription = "PatentsAndLicencesDescription",
                        ReducingBalanceBasis = 7,
                        StraightLineBasis = 77
                    },
                    DevelopmentCosts = new AssetsAdjustmentMessage
                    {
                        AlternativeBasis = "DevelopmentCostsAmortisationPolicy",
                        CategoryDescription = "DevelopmentCostsDescription",
                        ReducingBalanceBasis = 8,
                        StraightLineBasis = 88
                    },
                    ComputerSoftware = new AssetsAdjustmentMessage
                    {
                        AlternativeBasis = "ComputerSoftwareAmortisationPolicy",
                        CategoryDescription = "ComputerSoftwareDescription",
                        ReducingBalanceBasis = 9,
                        StraightLineBasis = 99
                    }
                }
            };
        }

        public static ProcessAccountsBuilderDataCommand GetProcessAccountsBuilderDataCommand(string reportType, string businessType = "")
        {
            var command = new ProcessAccountsBuilderDataCommand
            {
                Message = new AccountsBuilderReportingMessageDto
                {
                    PeriodId = CurrentPeriodId,
                    ClientId = ClientId,
                    TenantId = TenantId,
                    ReportType = reportType,
                    ProfitAndLossData = GetProfitAndLossData(),
                    BalanceSheetData = GetBalanceSheetData(),
                    ClientData = businessType == CompanyTypes.LlpBusinessType ? GetLlpClientData() : GetClientData(),
                    Involvements = GetInvolvements(),
                    ReportingPeriods = GetReportingPeriods(),
                    Notes = GetNotes(),
                    Signatures = GetSignatures(),
                    NoteAccountingPolicies = GetAccountingPoliciesNotes(),
                    ProfitShareData = new ProfitShareDataDto
                    {
                        ProfitShares = businessType == CompanyTypes.LlpBusinessType ? new List<ProfitShareMessage>() { GetProfitShare(CurrentPeriodId) } : new List<ProfitShareMessage>()
                    }, 
                    EntitySetup = EntitySetupDto,
                    PracticeDetails = new PracticeDetailsDto
                    {
                        ReferredType = 1,
                        SupervisingBody = 2,
                        Name = "PracticeName",
                        AddressLine1 = "AddressLine1",
                        AddressLine2 = "AddressLine2",
                        AddressLine3 = "AddressLine3",
                        AddressTown = "Town",
                        AddressCounty = "County",
                        AddressCountry = "Country",
                        AddressPostcode = "PostCode",
                    },
                    AccountPeriod = new AccountPeriodMessage
                    {
                        ClientId = ClientId,
                        PeriodId = CurrentPeriodId,
                        ReviseType = "SupplementaryNote"
                    },
                    DataScreenValue = GetReportsDataScreenValue(),
                    OtherData = GetOtherData(),
                    TrialBalanceData = GetPeriodTrialBalanceData()
                }
            };

            return command;
        }

        public static BalanceSheetMessage GetBalanceSheetMessage(Guid periodId)
        {
            return new BalanceSheetMessage
            {
                PeriodId = periodId,
                CalledUpShareCapitalNotPaid = new FinancialDataCategoryMessage { Value = "123.1" },
                FixedAssets = new FinancialDataCategoryMessage { Value = "123.2" },
                CurrentAssets = new FinancialDataCategoryMessage { Value = "123.3" },
                PrepaymentsAndAccruedIncome = new FinancialDataCategoryMessage { Value = "123.4" },
                CreditorsAmountsFallingDueWithinOneYear = new FinancialDataCategoryMessage { Value = "123.5" },
                NetCurrentAssetsOrLiabilities = new FinancialDataCategoryMessage { Value = "123.6" },
                TotalAssetsLessCurrentLiabilities = new FinancialDataCategoryMessage { Value = "123.7" },
                CreditorsAmountsFallingAfterMoreThanOneYear = new FinancialDataCategoryMessage { Value = "123.8" },
                ProvisionsForLiabilities = new FinancialDataCategoryMessage { Value = "123.9" },
                AccrualsAndDeferredIncome = new FinancialDataCategoryMessage { Value = "123.10" },
                NetAssets = new FinancialDataCategoryMessage { Value = "123.11" },
                CapitalAndReserves = new FinancialDataCategoryMessage
                {
                    Value = "123.12",
                    DrilldownData = new List<FinancialDataDrilldownMessage>
                    {
                        new FinancialDataDrilldownMessage
                        {
                            AccountCode = 1,
                            Amount = 22,
                            Description = "description2",
                            SubAccountCode = 33,
                            DirectorCode = 1,
                            SectorId = TestHelpers.Guids.GuidOne
                        }
                    }
                },
                IntangibleAssets = new FinancialDataCategoryMessage { Value = "123.13" },
                TangibleFixedAssets = new FinancialDataCategoryMessage { Value = "123.14" },
                FixedAssetInvestments = new FinancialDataCategoryMessage { Value = "123.15" },
                InvestmentProperty = new FinancialDataCategoryMessage { Value = "123.16" },
                Stock = new FinancialDataCategoryMessage { Value = "123.17" },
                CurrentAssetInvestments = new FinancialDataCategoryMessage { Value = "123.18" },
                Debtors = new FinancialDataCategoryMessage { Value = "123.19" },
                CashAtBankAndInHand = new FinancialDataCategoryMessage { Value = "123.20" },
                CapitalAccount = new FinancialDataCategoryMessage { Value = "123.21" },
                PartnersCapitalAccounts = new FinancialDataCategoryMessage { Value = "123.22" },
                PartnersCurrentAccounts = new FinancialDataCategoryMessage { Value = "123.23" },
                OtherReserves = new FinancialDataCategoryMessage { Value = "123.24" },
                PensionSchemeAssetsLiabilities = new FinancialDataCategoryMessage { Value = "123.25" },
                HealthcareObligatons = new FinancialDataCategoryMessage { Value = "123.26" },
                CalledUpShareCapital = new FinancialDataCategoryMessage { Value = "123.27" },
                SharePremiumReserve = new FinancialDataCategoryMessage { Value = "123.28" },
                RevaluationReserve = new FinancialDataCategoryMessage { Value = "123.29" },
                CapitalRedemptionReserve = new FinancialDataCategoryMessage { Value = "123.30" },
                OtherReserves1 = new FinancialDataCategoryMessage { Value = "123.31" },
                OtherReserves2 = new FinancialDataCategoryMessage { Value = "123.32" },
                FairValueReserve = new FinancialDataCategoryMessage { Value = "123.33" },
                ProfitAndLossReserve = new FinancialDataCategoryMessage { Value = "123.34" },
                Goodwill = new FinancialDataCategoryMessage { Value = "123.35" },
                NonControllingInterests = new FinancialDataCategoryMessage { Value = "123.36" },
                LoansAndOtherDebtsDueToMembers = new FinancialDataCategoryMessage { Value = "123.37" },
                MembersCapital = new FinancialDataCategoryMessage { Value = "123.38" },
                MembersOtherInterests = new FinancialDataCategoryMessage { Value = "123.39" },
                OtherDebtsDueToMembers = new FinancialDataCategoryMessage { Value = "123.40" },
                TotalMembersInterests = new FinancialDataCategoryMessage { Value = "123.41" },
                HerdBasis = new FinancialDataCategoryMessage { Value = "123.42" }
            };
        }

        public static ProfitAndLossMessage GetProfitAndLossMessage(Guid periodId, DateTime periodDate)
        {
            return new ProfitAndLossMessage
            {
                Period = periodDate,
                PeriodId = periodId,
                Turnover = new FinancialDataCategoryMessage { Value = "10" },
                OtherIncome = new FinancialDataCategoryMessage { Value = "20" },
                CostOfRawMaterialsAndConsumables = new FinancialDataCategoryMessage { Value = "30" },
                StaffCosts = new FinancialDataCategoryMessage { Value = "40" },
                DepreciationAndOtherAmountsWrittenOffAssets = new FinancialDataCategoryMessage { Value = "50" },
                OtherCharges = new FinancialDataCategoryMessage { Value = "60" },
                Tax = new FinancialDataCategoryMessage
                {
                    Value = "601",
                    DrilldownData = new List<FinancialDataDrilldownMessage>
                    {
                        new FinancialDataDrilldownMessage
                        {
                            AccountCode = 1,
                            Amount = 2,
                            Description = "description",
                            SubAccountCode = 3,
                            DirectorCode = 2,
                            SectorId = TestHelpers.Guids.GuidOne
                        }
                    }
                },
                Sales = new FinancialDataCategoryMessage { Value = "80" },
                CostOfSales = new FinancialDataCategoryMessage { Value = "90" },
                Expenses = new FinancialDataCategoryMessage { Value = "100" },
                FinanceCosts = new FinancialDataCategoryMessage { Value = "110" },
                PartnerAppropriations = new FinancialDataCategoryMessage { Value = "120" },
                Depreciation = new FinancialDataCategoryMessage { Value = "130" },
                GrossProfitLoss = new FinancialDataCategoryMessage { Value = "140" },
                DistributionExpenses = new FinancialDataCategoryMessage { Value = "150" },
                AdministrativeExpenses = new FinancialDataCategoryMessage { Value = "160" },
                OtherOperatingIncome = new FinancialDataCategoryMessage { Value = "170" },
                GainLossOnRevaluation1 = new FinancialDataCategoryMessage { Value = "180" },
                OperatingProfitLoss = new FinancialDataCategoryMessage { Value = "190" },
                ExceptionalItems = new FinancialDataCategoryMessage { Value = "200" },
                IncomeFromSharesInGroupUndertakings = new FinancialDataCategoryMessage { Value = "210" },
                IncomeFromInterestInAssociatedUndertakings = new FinancialDataCategoryMessage { Value = "220" },
                IncomeFromOtherParticipatingInterests = new FinancialDataCategoryMessage { Value = "230" },
                IncomeFromFixedAssetInvestments = new FinancialDataCategoryMessage { Value = "240" },
                InterestReceivableAndSimilarIncome = new FinancialDataCategoryMessage { Value = "250" },
                OtherFinanceIncome = new FinancialDataCategoryMessage { Value = "260" },
                AmountsWrittenOffInvestments = new FinancialDataCategoryMessage { Value = "270" },
                GainLossOnRevaluation2 = new FinancialDataCategoryMessage { Value = "280" },
                InterestPayableAndSimilarExpenses = new FinancialDataCategoryMessage { Value = "290" },
                OtherFinanceCosts = new FinancialDataCategoryMessage { Value = "300" },
                ProfitLossOnOrdinaryActivitiesBeforeTaxation = new FinancialDataCategoryMessage { Value = "310" },
                Taxation = new FinancialDataCategoryMessage { Value = "320" },
                ProfitLossForTheFinancialYear = new FinancialDataCategoryMessage { Value = "330" },
                ProfitLossAvailableForDiscretionaryDivision = new FinancialDataCategoryMessage { Value = "340" },
                NonControllingInterests = new FinancialDataCategoryMessage { Value = "350" },
                MembersRemunerationAsExpense = new FinancialDataCategoryMessage { Value = "360" }
            };
        }

        public static ProfitShareMessage GetProfitShare(Guid periodId)
        {
            return new ProfitShareMessage
            {
                CumulativeAmount = 100,
                InvolvementId = 1,
                AccountPeriodId = periodId
            };
        }

        public static NotesResponseDataMessage GetNotes(string reportType)
        {
            var noteDto = new NotesResponseDataMessage();
            if (reportType == ReportType.FRS105)
            {
                noteDto = new NotesResponseDataMessage
                {
                    AverageNumberOfEmployees = new AverageNumberOfEmployeesMessage
                    {
                        CurrentPeriod = 1,
                        PreviousPeriod = 2
                    },
                    RelatedPartyTransactions = "RelatedPartyTransactions",
                    MembersLiabilityText = new MembersLiabilityTextMessage
                    {
                        NoteTitle = "MembersLiabilityTextTitle",
                        NoteText = "MembersLiabilityTextText"
                    },
                    AdditionalNote1 = new AdditionalNoteMessage
                    {
                        NoteTitle = "NoteTitle1",
                        NoteText = "NoteText1"
                    },
                    AdditionalNote2 = new AdditionalNoteMessage
                    {
                        NoteTitle = "NoteTitle2",
                        NoteText = "NoteText2"
                    },
                    AdvancesCreditAndGuaranteesGrantedToDirectors = "AdvancesCreditAndGuaranteesGrantedToDirectors",
                    ControllingPartyNote = "ControllingPartyNote",
                    GuaranteesAndOtherFinancialCommitments = "GuaranteesAndOtherFinancialCommitments",
                    OffBalanceSheetArrangements = "OffBalanceSheetArrangements",
                    IntangibleAssetsRevaluation = "IntangibleAssetsRevaluation",
                    TangibleFixedAssetsNotes = new TangibleFixedAssetsNotesMessage
                    {
                        ValuationInCurrentReportingPeriod = new ValuationInCurrentReportingPeriodMessage
                        {
                            ValuationDetails = "Valuation Details long message",
                            IndependentValuerInvolved = true,
                            RevaluationBasis = "Revaluation Basis short message",
                            DateOfRevaluation = new DateTime(2019, 05, 09, 09, 15, 00)

                        },
                        HistoricalCostBreakdown = new HistoricalCostBreakdownMessage
                        {
                            RevaluedAssetClass = "Revalued Asset Class",
                            RevaluedClassPronoun = "Revalued Class Pronoun",
                            CurrentReportingPeriodAccumulatedDepreciation = (decimal)1.33,
                            CurrentReportingPeriodCost = (decimal)1.45,
                        },
                        AnalysisOfCostOrValuation = new AnalysisOfCostOrValuationMessage
                        {
                            AnalysisOfCostOrValuationItems = new System.Collections.Generic.List<AnalysisOfCostOrValuationItemMessage>
                            {
                                new AnalysisOfCostOrValuationItemMessage
                                {
                                    Index = 1,
                                    Year = 2020,
                                    LandAndBuildings = (decimal)1.1,
                                    PlantAndMachineryEtc = (decimal) 2.2
                                },
                                new AnalysisOfCostOrValuationItemMessage
                                {
                                    Index = 3,
                                    Year = 2021,
                                    LandAndBuildings = (decimal)3.3,
                                    PlantAndMachineryEtc = (decimal) 4.4
                                }
                            },
                            CostLandAndBuildings = (decimal)5.5,
                            CostPlantAndMachineryEtc = (decimal)6.6,
                            TotalLandAndBuildings = 10,
                            TotalPlantAndMachineryEtc = 11
                        }
                    }
                };
            }

            if (reportType == ReportType.FRS102_1A)
            {
                noteDto = new NotesResponseDataMessage
                {
                    AverageNumberOfEmployees = new AverageNumberOfEmployeesMessage
                    {
                        CurrentPeriod = 1,
                        PreviousPeriod = 2
                    },
                    RelatedPartyTransactions = "RelatedPartyTransactions",
                    MembersLiabilityText = new MembersLiabilityTextMessage
                    {
                        NoteTitle = "MembersLiabilityTextTitle",
                        NoteText = "MembersLiabilityTextText"

                    },
                    AdditionalNote1 = new AdditionalNoteMessage
                    {
                        NoteTitle = "NoteTitle1",
                        NoteText = "NoteText1"
                    },
                    AdditionalNote2 = new AdditionalNoteMessage
                    {
                        NoteTitle = "NoteTitle2",
                        NoteText = "NoteText2"
                    },
                    AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = new AdvancesCreditAndGuaranteesGrantedToDirectorsMessage
                    {
                        Items = new List<AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage>{
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage
                            {
                                Index = 1,
                                InvolvementClientGuid = TestHelpers.Guids.GuidOne,
                                DirectorName = "Test director 1",
                                BalanceOutstandingAtStartOfYear = (decimal)1.1,
                                AmountsAdvanced = (decimal)1.2,
                                AmountsRepaid = (decimal)1.3,
                                AmountsWrittenOff = (decimal) 1.4,
                                AmountsWaived = (decimal) 1.5,
                                BalanceOutstandingAtEndOfYear = (decimal) 1.6,
                                AdvanceCreditConditions = "Added text 1"
                            },
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage
                            {
                                Index = 2,
                                InvolvementClientGuid = TestHelpers.Guids.GuidTwo,
                                DirectorName = "Test director 2",
                                BalanceOutstandingAtStartOfYear = (decimal)2.1,
                                AmountsAdvanced = (decimal)2.2,
                                AmountsRepaid = (decimal)2.3,
                                AmountsWrittenOff = (decimal) 2.4,
                                AmountsWaived = (decimal) 2.5,
                                BalanceOutstandingAtEndOfYear = (decimal) 2.6,
                                AdvanceCreditConditions = "Added text 2"
                            },
                        },
                        Guarantees = "guarantee text"
                    },
                    ControllingPartyNote = "ControllingPartyNote",
                    GuaranteesAndOtherFinancialCommitments = "GuaranteesAndOtherFinancialCommitments",
                    OffBalanceSheetArrangements = "OffBalanceSheetArrangements",
                    IntangibleAssetsRevaluation = "IntangibleAssetsRevaluation",
                    TangibleFixedAssetsNotes = new TangibleFixedAssetsNotesMessage
                    {
                        ValuationInCurrentReportingPeriod = new ValuationInCurrentReportingPeriodMessage
                        {
                            ValuationDetails = "Valuation Details long message",
                            IndependentValuerInvolved = true,
                            RevaluationBasis = "Revaluation Basis short message",
                            DateOfRevaluation = new DateTime(2019, 05, 09, 09, 15, 00)

                        },
                        HistoricalCostBreakdown = new HistoricalCostBreakdownMessage
                        {
                            RevaluedAssetClass = "Revalued Asset Class",
                            RevaluedClassPronoun = "Revalued Class Pronoun",
                            CurrentReportingPeriodAccumulatedDepreciation = (decimal)1.33,
                            CurrentReportingPeriodCost = (decimal)1.45,
                        },
                        AnalysisOfCostOrValuation = new AnalysisOfCostOrValuationMessage
                        {
                            AnalysisOfCostOrValuationItems = new System.Collections.Generic.List<AnalysisOfCostOrValuationItemMessage>
                            {
                                new AnalysisOfCostOrValuationItemMessage
                                {
                                    Index = 1,
                                    Year = 2020,
                                    LandAndBuildings = (decimal)1.1,
                                    PlantAndMachineryEtc = (decimal) 2.2
                                },
                                new AnalysisOfCostOrValuationItemMessage
                                {
                                    Index = 3,
                                    Year = 2021,
                                    LandAndBuildings = (decimal)3.3,
                                    PlantAndMachineryEtc = (decimal) 4.4
                                }
                            },
                            CostLandAndBuildings = (decimal)5.5,
                            CostPlantAndMachineryEtc = (decimal)6.6,
                            TotalLandAndBuildings = 10,
                            TotalPlantAndMachineryEtc = 11
                        }
                    },
                    OperatingProfitLoss = new OperatingProfitLossMessage
                    {
                        IsEnabled = true,
                        Items = new List<OperatingProfitLossItemMessage>
                            {
                                new OperatingProfitLossItemMessage
                                {
                                    Index = 1,
                                    Description = "Description1",
                                    Value = 1
                                }
                            }
                    }
                };
            }

            return noteDto;
        }

        public static List<InvolvementDto> GetInvolvements()
        {
            return new List<InvolvementDto>
            {
                GetInvolvement()
            };
        }

        public static DataScreenValueMessage GetReportsDataScreenValue()
        {
            return new DataScreenValueMessage
            {
                ClientId = ClientId,
                PeriodId = CurrentPeriodId,
                TenantId = TenantId,
                CurrentPeriod = new List<PeriodScreenValueMessage>
                {
                    new PeriodScreenValueMessage
                    {
                        ReportMappingTable = "Reports",
                        ScreenId = "ScreenId3",
                        ScreenFields = new List<ScreenFieldMessage>
                        {
                            new ScreenFieldMessage
                            {
                                Name = "FieldId1",
                                Value = "Value1"
                            },
                            new ScreenFieldMessage
                            {
                                Name = "FieldId2",
                                Value = "Value2"
                            }
                        }
                    }
                }
            };
        }

        public static List<OtherMessage> GetOtherData()
        {
            return new List<OtherMessage>
            {
                new OtherMessage
                {
                    Period = CurrentPeriodEndDate,
                    PeriodId = CurrentPeriodId,
                    WagesAndSalaries = new FinancialDataCategoryMessage{
                        DrilldownData = new List<FinancialDataDrilldownMessage>
                        {
                            new FinancialDataDrilldownMessage
                            {
                                Amount = 200,
                            }
                        },
                        Value = "200"
                    }
                },
                new OtherMessage
                {
                    Period = PreviousPeriodEndDate,
                    PeriodId = PreviousPeriodId,
                    WagesAndSalaries = new FinancialDataCategoryMessage{
                        DrilldownData = new List<FinancialDataDrilldownMessage>
                        {
                            new FinancialDataDrilldownMessage
                            {
                                Amount = 200,
                            }
                        },
                        Value = "200"
                    }
                }
            };
        }

        public static List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance> GetPeriodTrialBalanceData()
        {
            return new List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance>
            {
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 1,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 966,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 969,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 968,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 970,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 970,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 972,
                    Amount = 100,
                    PeriodId = PreviousPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 973,
                    Amount = 100,
                    PeriodId = PreviousPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 974,
                    Amount = 100,
                    PeriodId = PreviousPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 512,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 513,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 514,
                    Amount = 0,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 522,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 523,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 524,
                    Amount = 0,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 542,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 548,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 549,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 550,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 551,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 552,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 553,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 985,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 990,
                    Amount = 100,
                    PeriodId = PreviousPeriodId
                }
            };
        }

        public static List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance> GetPeriodTrialBalanceDataSOCIEAccountCodes()
        {
            return new List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance>
            {
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 1,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 966,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 969,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 969,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 970,
                    Amount = 100,
                    PeriodId = PreviousPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 970,
                    Amount = 100,
                    PeriodId = PreviousPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 971,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                }
            };
        }

        public static List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance> GetPeriodTrialBalanceDataRomiLLPAccountCodes()
        {
            return new List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance>
            {
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 1,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 970,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 972,
                    Amount = 100,
                    PeriodId = CurrentPeriodId
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 990,
                    Amount = 100,
                    PeriodId = PreviousPeriodId
                }
            };
        }
    }
}
