﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.Message;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.AggregationStrategies
{
    public class DataProcessingLayerEventbusFinishedSuccessfulEventStrategyTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<IDomainEventService> _domainEventService;
        private readonly Mock<ILogger<DataProcessingLayerEventbusFinishedSuccessfulEventStrategy>> _logger;
        private readonly Mock<UserContext> _userContext;
        private readonly IMapper _mapper;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = TestHelpers.Guids.GuidTwo;
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;

        public DataProcessingLayerEventbusFinishedSuccessfulEventStrategyTests()
        {
            var mapperConfig = new MapperConfiguration(cfg => { cfg.AddProfile<TrialBalanceMapper>(); });
            mapperConfig.AssertConfigurationIsValid();
            _repository = new Mock<IAccountsBuilderRepository>();
            _mapper = mapperConfig.CreateMapper();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _logger = new Mock<ILogger<DataProcessingLayerEventbusFinishedSuccessfulEventStrategy>>();
            _userContext = new Mock<UserContext>();
            _userContext.Setup(q => q.TenantId).Returns(_tenantId.ToString());
            _domainEventService = new Mock<IDomainEventService>();
        }

        [Fact]
        public async Task Should_update_financial_data()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId, null!)
            {
                FinancialData = new FinancialData
                {
                    Status = (int)FinancialDataStatusDto.Success,
                    Financials = []
                }
            };

            _repository.Setup(repository => repository.Get(_processId, CancellationToken.None)).ReturnsAsync(accountsBuilder);

            var requestMessage = GetSuccessfulRequestMessage(_processId, DateTime.UtcNow);
            var strategy = new DataProcessingLayerEventbusFinishedSuccessfulEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.ExecuteAsync(requestMessage);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Once);
        }

        [Fact]
        public async Task Should_update_financial_data_when_one_period_missing()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, null!)
            {
                TrialBalance = new TrialBalance
                {
                    ReportingPeriods =
                    [
                        new() { Id = Guid.NewGuid(), EndDate = new DateTime(2020, 1, 1) },
                        new() { Id = Guid.NewGuid(), EndDate = new DateTime(2021, 1, 1) }
                    ]
                },
                FinancialData = new FinancialData
                {
                    Status = (int)FinancialDataStatusDto.Success,
                    Financials = []
                }
            };
            _repository.Setup(repository => repository.Get(It.IsAny<Guid>(), CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            var requestMessage = GetSuccessfulRequestMessage(_processId, new DateTime(2021, 1, 1));
            var strategy = new DataProcessingLayerEventbusFinishedSuccessfulEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.ExecuteAsync(requestMessage);

            _repository.Verify(
                repository => repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(entity =>
                    entity.FinancialData.Financials.Count == 2 &&
                    entity.FinancialData.Financials.FirstOrDefault(x => x.Period == new DateTime(2020, 1, 1)) != null),
                    CancellationToken.None),
                Times.Once);
        }

        [Fact]
        public async Task Should_not_update_financial_data_when_no_process_found()
        {
            var requestMessage = GetSuccessfulRequestMessage(_processId, new DateTime(2021, 1, 1));
            var strategy = new DataProcessingLayerEventbusFinishedSuccessfulEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.ExecuteAsync(requestMessage);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_not_update_financial_data_when_no_accounts_builder_found()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(() => null!);
            var requestMessage = GetSuccessfulRequestMessage(_processId, new DateTime(2021, 1, 1));
            var strategy = new DataProcessingLayerEventbusFinishedSuccessfulEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.ExecuteAsync(requestMessage);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_throw_when_exception()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, null!)
            {
                FinancialData = new FinancialData
                {
                    Status = (int)FinancialDataStatusDto.Success,
                    Financials = []
                }
            };
            _repository.Setup(repository => repository.Get(It.IsAny<Guid>(), CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            _repository.Setup(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None)).Throws<Exception>();
            var requestMessage = GetSuccessfulRequestMessage(_processId, new DateTime(2021, 1, 1));
            var strategy = new DataProcessingLayerEventbusFinishedSuccessfulEventStrategy(_repository.Object, _logger.Object, _mapper);

            await Should.ThrowAsync<Exception>(async () =>
            {
                await strategy.ExecuteAsync(requestMessage);
            });
        }

        [Fact]
        public async Task Should_set_correct_process_status()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, null!);
            _repository.Setup(repository => repository.Get(_processId, CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            var requestMessage = GetSuccessfulRequestMessage(_processId, new DateTime(2021, 1, 1));
            var strategy = new DataProcessingLayerEventbusFinishedSuccessfulEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.ExecuteAsync(requestMessage);
            _repository.Verify(
                repository => repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(entity =>
                    entity.Status == Status.Successful &&
                    entity.ErrorCode == null),
                    CancellationToken.None),
                Times.Once);
        }

        [Fact]
        public async Task Execute_ShouldDeserializePayloadAndCallExecuteAsync()
        {
            var strategy = new DataProcessingLayerEventbusFinishedSuccessfulEventStrategy(_repository.Object, _logger.Object, _mapper);
            var payload = new FinancialData { Status = 1, Error = "None" };
            var message = new EventBusMessage<string>
            {
                Payload = JsonSerializer.Serialize(payload)
            };

            await Should.NotThrowAsync(async () =>
            {
                await strategy.Execute(message);
            });
        }

        private static EventBusMessage<FinancialDataEventbusDto> GetSuccessfulRequestMessage(Guid processId, DateTime period)
        {
            var drilldown = new List<FinancialDataDrilldownMessage>
            {
                new() { AccountCode = 1, Amount = 100, Description = "Bank deposit", SubAccountCode = null }
            };
            var turnoverCategoryDto = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "-500.0"
            };
            var otherIncomeCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "1300.0"
            };

            var staffCostsCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "100"
            };
            var otherChargesCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "1000"
            };
            var taxCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "100"
            };
            var zeroValueCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "0"
            };
            var fixedAssetsCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "100"
            };
            var currentAssetsCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "300"
            };
            var netAssetsCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "200"
            };
            var capitalAndReservesCategory = new FinancialDataCategoryMessage
            {
                DrilldownData = drilldown,
                Value = "400"
            };

            EventBusMessage<FinancialDataEventbusDto> result = new EventBusMessage<FinancialDataEventbusDto>()
            {
                Topic = new Iris.Platform.Eventbus.Client.Dotnet.Common.Models.Message.Attributes.Topic("dataprocessinglayer:successful-generation"),
                Payload = new FinancialDataEventbusDto
                {
                    ProcessId = processId,
                    Data = [
                        new()
                        {
                            Period = period,
                            Turnover = turnoverCategoryDto,
                            OtherIncome = otherIncomeCategory,
                            CostOfRawMaterialsAndConsumables = zeroValueCategory,
                            StaffCosts = staffCostsCategory,
                            DepreciationAndOtherAmountsWrittenOffAssets = zeroValueCategory,
                            OtherCharges = otherChargesCategory,
                            Tax = taxCategory,
                            CalledUpShareCapitalNotPaid = zeroValueCategory,
                            FixedAssets = fixedAssetsCategory,
                            CurrentAssets = currentAssetsCategory,
                            PrepaymentsAndAccruedIncome = zeroValueCategory,
                            CreditorsAmountsFallingDueWithinOneYear = zeroValueCategory,
                            NetCurrentAssetsOrLiabilities = zeroValueCategory,
                            TotalAssetsLessCurrentLiabilities = zeroValueCategory,
                            CreditorsAmountsFallingAfterMoreThanOneYear = zeroValueCategory,
                            ProvisionsForLiabilities = zeroValueCategory,
                            AccrualsAndDeferredIncome = zeroValueCategory,
                            NetAssets = netAssetsCategory,
                            CapitalAndReserves = capitalAndReservesCategory
                        }
                    ]
                }
            };

            return result;
        }
    }
}