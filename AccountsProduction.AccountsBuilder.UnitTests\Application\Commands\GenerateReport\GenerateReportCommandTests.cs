﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.FRS102;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.FRS1021A;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.FRS105;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.Unincorporated;
using AccountsProduction.AccountsBuilder.Application.Commands.GenerateReport;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Application.Queries.AccountsBuilder;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper;
using AutoMapper;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.GenerateReport
{
    public class GenerateReportCommandTests
    {
        private readonly Mock<IMediator> _mediator;
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<IDomainEventService> _domainEventService;
        private readonly Mock<IAccountsBuilderService> _accountsBuilderService;
        private readonly Mock<IGenerateReportDataService> _generateReportDataService;
        private readonly List<IMap> _reportMappers;
        private readonly Mock<ILogger<GenerateReportCommandHandler>> _logger;
        private readonly Mock<UserContext> _userContext;
        private readonly Mock<IValidationHandler> _validationHandler;
        private readonly Mock<IAccountPeriodService> _accountPeriodService;
        private readonly IMapper _mapper;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = TestHelpers.Guids.GuidTwo;
        private readonly Guid _previousPeriodId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;

        public GenerateReportCommandTests()
        {
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _userContext = new Mock<UserContext>();
            _repository = new Mock<IAccountsBuilderRepository>();
            _generateReportDataService = new Mock<IGenerateReportDataService>();
            _accountsBuilderService = new Mock<IAccountsBuilderService>();
            _reportMappers = new List<IMap>()
            {
                new FRS1021AReportingDomainMapper(_userContext.Object,_mapper),
                new FRS102ReportingDomainMapper(_userContext.Object,_mapper),
                new FRS105ReportingDomainMapper(_userContext.Object,_mapper),
                new UnincorporatedReportingDomainMapper(_userContext.Object,_mapper)
            };
            _logger = new Mock<ILogger<GenerateReportCommandHandler>>();
            _mediator = new Mock<IMediator>();
            _accountPeriodService = new Mock<IAccountPeriodService>();
            _validationHandler = new Mock<IValidationHandler>();
            _domainEventService = new Mock<IDomainEventService>();

            InitData();
        }

        private void InitData()
        {
            _userContext.Setup(q => q.TenantId).Returns(_tenantId.ToString());
            _accountPeriodService.Setup(x => x.GetEntitySetupAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).ReturnsAsync(new EntitySetupDto
            {
                ReportingStandard = "FRS105",
                EntitySize = "Micro",
                IndependentReviewType = "Accountants",
                TradingStatus = "Trading",
                Terminology = "Companies Act"
            });

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId)
                {
                    FinancialData = new FinancialData
                    {
                        Status = (int)FinancialDataStatusDto.Success,
                        Financials = new List<Financial>()
                    },
                    Signatory = new Signatory
                    {
                        Signature = new SignatureDetail
                        {
                            Signatures = new()
                        }
                    }
                });

            _repository.Setup(repository => repository.Get(_clientId, _previousPeriodId, CancellationToken.None))
              .ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _previousPeriodId)
              {
                  FinancialData = new FinancialData
                  {
                      Status = (int)FinancialDataStatusDto.Success,
                      Financials = new List<Financial>()
                  },
                  Signatory = new Signatory
                  {
                      Signature = new SignatureDetail
                      {
                          Signatures = new()
                      }
                  },
                  AccountingPolicies = new AccountsBuilder.Domain.AccountingPoliciesModels.AccountingPolicies(),
                  Notes = new AccountsBuilder.Domain.NoteModels.Notes()
              });
            _generateReportDataService.Setup(c => c.GetReportData(_clientId, _periodId)).ReturnsAsync(new AccountsBuilder.Application.Common.Dto.Report.GenerateReportDto()
            {
                ClientAddressDto = new AccountsBuilder.Application.Common.Dto.Address.ClientAddressDto(),
                ClientResponse = new AccountsBuilder.Application.Common.Dto.Client.ClientResponse(),
                EntitySetupDto = new EntitySetupDto(),
                InvolvementDtos = new List<AccountsBuilder.Application.Common.Dto.Client.InvolvementDto>(),
                PracticeDetailsDto = new AccountsBuilder.Application.Common.Dto.PracticeDetails.PracticeDetailMessage(),
                TrialBalanceDto = new TrialBalanceDto()
            });

            _accountsBuilderService.Setup(c => c.PostAccountsBuilderData(_clientId, _periodId, It.IsAny<BaseReportingMessage>())).Returns(Task.CompletedTask);
        }

        [Fact]
        public async Task Should_generate_new_report_frs105()
        {
            var clientId = TestHelpers.Guids.GuidOne;
            var periodId = TestHelpers.Guids.GuidTwo;

            _mediator.Setup(mediator => mediator.Send(It.IsAny<GetReportQuery>(), CancellationToken.None))
                .ReturnsAsync(() => null);

            var handler = new GenerateReportCommandHandler(_userContext.Object, _mapper, _logger.Object, _generateReportDataService.Object, _repository.Object, _accountsBuilderService.Object, _validationHandler.Object, _reportMappers, _mediator.Object, _domainEventService.Object);

            var reportingStandardDto = new ReportingStandardDto
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = ReportStandardType.FRS105,
                Version = ReportingStandardVersion.Full
            };

            var command = new GenerateReportCommand
            {
                ClientId = clientId,
                PeriodId = periodId,
                ReportingStandard = reportingStandardDto,
            };

            var result = await handler.Handle(command, CancellationToken.None);

            result.ShouldBe(Unit.Value);

        }

        [Fact]
        public async Task Should_update_existing_report()
        {
            var clientId = TestHelpers.Guids.GuidOne;
            var periodId = TestHelpers.Guids.GuidTwo;
            var previousPeriodId = TestHelpers.Guids.GuidFour;
            var previousPeriodIdGetReportQuery = TestHelpers.Guids.GuidFive;


            _mediator.Setup(mediator => mediator.Send(It.IsAny<GetReportQuery>(), CancellationToken.None)).ReturnsAsync(
                () => new AccountsBuilderDto
                {
                    PeriodId = periodId,
                    ClientId = clientId,
                    PreviousPeriodId = previousPeriodIdGetReportQuery,
                    IsDirty = false,
                    Status = Status.Successful,
                    ErrorCode = null,
                    EntitySetup = null
                });


            var handler = new GenerateReportCommandHandler(_userContext.Object, _mapper, _logger.Object, _generateReportDataService.Object, _repository.Object, _accountsBuilderService.Object, _validationHandler.Object, _reportMappers, _mediator.Object, _domainEventService.Object);

            var reportingStandardDto = new ReportingStandardDto
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = ReportStandardType.FRS105,
                Version = ReportingStandardVersion.Full
            };

            var command = new GenerateReportCommand
            {
                ClientId = clientId,
                PeriodId = periodId,
                PreviousPeriodId = previousPeriodId,
                ReportingStandard = reportingStandardDto
            };
            await handler.Handle(command, CancellationToken.None);

            _repository.Verify(repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Once);

        }


        [Fact]
        public async Task Should_not_generate_report_if_financial_data_has_error()
        {
            var clientId = TestHelpers.Guids.GuidOne;
            var periodId = TestHelpers.Guids.GuidTwo;
            var previousPeriodId = TestHelpers.Guids.GuidFour;
            var previousPeriodIdGetReportQuery = TestHelpers.Guids.GuidFive;

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
               .ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId)
               {
                   FinancialData = new FinancialData
                   {
                       Status = (int)FinancialDataStatusDto.Error,
                       Financials = new List<Financial>()
                   },
                   Signatory = new Signatory
                   {
                       Signature = new SignatureDetail
                       {
                           Signatures = new()
                       }
                   }
               });

            _mediator.Setup(mediator => mediator.Send(It.IsAny<GetReportQuery>(), CancellationToken.None)).ReturnsAsync(
                () => new AccountsBuilderDto
                {
                    PeriodId = periodId,
                    ClientId = clientId,
                    PreviousPeriodId = previousPeriodIdGetReportQuery,
                    IsDirty = false,
                    Status = Status.Successful,
                    ErrorCode = null,
                    EntitySetup = null
                });


            var handler = new GenerateReportCommandHandler(_userContext.Object, _mapper, _logger.Object, _generateReportDataService.Object, _repository.Object, _accountsBuilderService.Object, _validationHandler.Object, _reportMappers, _mediator.Object, _domainEventService.Object);

            var reportingStandardDto = new ReportingStandardDto
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = ReportStandardType.FRS105,
                Version = ReportingStandardVersion.Full
            };

            var command = new GenerateReportCommand
            {
                ClientId = clientId,
                PeriodId = periodId,
                PreviousPeriodId = previousPeriodId,
                ReportingStandard = reportingStandardDto
            };

            await handler.Handle(command, CancellationToken.None);

            _repository.Verify(
                repository => repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(entity =>
                    entity.FinancialData.Status == (int)FinancialDataStatusDto.Error),
                    CancellationToken.None),
                Times.Once);

        }

        [Fact]
        public async Task Should_create_accounts_builder_if_not_exist()
        {
            var clientId = TestHelpers.Guids.GuidOne;
            var periodId = TestHelpers.Guids.GuidTwo;
            var previousPeriodId = TestHelpers.Guids.GuidFour;
            var previousPeriodIdGetReportQuery = TestHelpers.Guids.GuidFive;

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
               .ReturnsAsync(await Task.FromResult<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(null!));

            _mediator.Setup(mediator => mediator.Send(It.IsAny<GetReportQuery>(), CancellationToken.None)).ReturnsAsync(
                () => new AccountsBuilderDto
                {
                    PeriodId = periodId,
                    ClientId = clientId,
                    PreviousPeriodId = previousPeriodIdGetReportQuery,
                    IsDirty = false,
                    Status = Status.Successful,
                    ErrorCode = null,
                    EntitySetup = null
                });


            var handler = new GenerateReportCommandHandler(_userContext.Object, _mapper, _logger.Object, _generateReportDataService.Object, _repository.Object, _accountsBuilderService.Object, _validationHandler.Object, _reportMappers, _mediator.Object, _domainEventService.Object);

            var reportingStandardDto = new ReportingStandardDto
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = ReportStandardType.FRS105,
                Version = ReportingStandardVersion.Full
            };

            var command = new GenerateReportCommand
            {
                ClientId = clientId,
                PeriodId = periodId,
                PreviousPeriodId = previousPeriodId,
                ReportingStandard = reportingStandardDto
            };

            await handler.Handle(command, CancellationToken.None);

            _repository.Verify(
                repository => repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(entity =>
                    entity.ReportingStandard.Id == reportingStandardDto.Id &&
                    entity.ClientId == command.ClientId &&
                    entity.PeriodId == command.PeriodId),
                    CancellationToken.None),
                Times.Once);

        }


        [Fact]
        public async Task Should_add_non_financial_data_to_accounts_builder_when_business_type_not_exist()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId,
                    _periodId);
            accountsBuilder.UpdateNonFinancialData(new NonFinancialData
            {
                CompanyName = "test"
            });
            accountsBuilder.AddEntitySetup(new EntitySetup
            {
                EntitySize = "test"
            });
            accountsBuilder.AddTrialBalance(new TrialBalance
            {
                TrialBalances = new List<PeriodTrialBalance> {
                 new PeriodTrialBalance
                 {
                     AccountCode = 1,
                     Amount = 1,
                 }
                }
            });

            var clientId = TestHelpers.Guids.GuidOne;
            var periodId = TestHelpers.Guids.GuidTwo;
            var previousPeriodId = TestHelpers.Guids.GuidFour;
            var previousPeriodIdGetReportQuery = TestHelpers.Guids.GuidFive;


            _mediator.Setup(mediator => mediator.Send(It.IsAny<GetReportQuery>(), CancellationToken.None)).ReturnsAsync(
                () => new AccountsBuilderDto
                {
                    PeriodId = periodId,
                    ClientId = clientId,
                    PreviousPeriodId = previousPeriodIdGetReportQuery,
                    IsDirty = false,
                    Status = Status.Successful,
                    ErrorCode = null,
                    EntitySetup = null
                });


            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => accountsBuilder);

            var handler = new GenerateReportCommandHandler(_userContext.Object, _mapper, _logger.Object, _generateReportDataService.Object, _repository.Object, _accountsBuilderService.Object, _validationHandler.Object, _reportMappers, _mediator.Object, _domainEventService.Object);

            var reportingStandardDto = new ReportingStandardDto
            {
                Id = "123",
                Name = "FRS 105 - Full",
                Type = ReportStandardType.FRS105,
                Version = ReportingStandardVersion.Full
            };

            var command = new GenerateReportCommand
            {
                ClientId = clientId,
                PeriodId = periodId,
                PreviousPeriodId = previousPeriodId,
                ReportingStandard = reportingStandardDto
            };
            await handler.Handle(command, CancellationToken.None);
            _repository.Verify(repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Once);
        }

    }
}
