﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<SonarQubeTestProject>false</SonarQubeTestProject>
		<PublishReadyToRun>true</PublishReadyToRun>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Iris.AccountsProduction.Common.Toolkit" Version="2.0.0.259" />
		<PackageReference Include="Iris.Elements.Domain.Seedwork" Version="1.0.0.15" />
		<PackageReference Include="Iris.Elements.DynamoDb" Version="1.1.0.9" />
	</ItemGroup>
</Project>
