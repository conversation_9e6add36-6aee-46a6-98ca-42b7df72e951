﻿using Amazon.DynamoDBv2.DataModel;
using Iris.Elements.DynamoDb.Converters;

namespace AccountsProduction.AccountsBuilder.Domain
{
    public class Signatory
    {
        public Signatory()
        {
        }
        public Guid ClientId { get; set; }
        public Guid PeriodId { get; set; }
        public SignatureDetail Signature { get; set; }
        public string Error { get; set; }
        public bool? IsSuccessful { get; set; }
        [DynamoDBProperty(typeof(DateTimeConverter))]
        public DateTime EntityModificationTime { get; set; }
        public void UpdateModificationTime()
        {
            EntityModificationTime = DateTime.UtcNow;
        }
    }
    public class SignatureDetail
    {
        public List<Signature> Signatures { get; set; }
        public DateTime? AccountantSigningDate { get; set; }
        public string? AccountantSigningName { get; set; }
        public bool? IncludeAccountantsReport { get; set; }
    }
}
