﻿using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.PracticeDetails
{
    [ExcludeFromCodeCoverage]
    public class PracticeDetailMessage
    {
        public Guid PracticeDetailId { get; set; }

        public string Name { get; set; } = null!;

        public Guid TenantId { get; set; }

        public string Description { get; set; } = null!;

        public string Phone { get; set; } = null!;

        public string BranchId { get; set; } = null!;

        public bool IsPrimaryPracticeOffice { get; set; }

        public string Email { get; set; } = null!;

        public string Exchange { get; set; } = null!;

        public string Fax { get; set; } = null!;

        public string Dx { get; set; } = null!;

        public string Website { get; set; } = null!;

        public Practices Type { get; set; }

        public AddressMessage Address { get; set; } = null!;

        public ActingNameMessage ActingNames { get; set; } = null!;
    }

    [ExcludeFromCodeCoverage]
    public class AddressMessage
    {
        public string Postcode { get; set; } = null!;

        public string Address1 { get; set; } = null!;

        public string Address2 { get; set; } = null!;

        public string Address3 { get; set; } = null!;

        public string City { get; set; } = null!;

        public string County { get; set; } = null!;

        public string Country { get; set; } = null!;
    }

    [ExcludeFromCodeCoverage]
    public class ActingNameMessage
    {
        public string AuditorName { get; set; } = null!;

        public string AccountantName { get; set; } = null!;

        public Referred ReferredType { get; set; }

        public SupervisoryBody SupervisoryBody { get; set; }
    }

    public enum Practices
    {
        Firm = 0,
        Partnership = 1,
        Individual = 2,
    }

    public enum Referred
    {
        Plural = 0,
        Singular = 1,
    }

    public enum SupervisoryBody
    {
        NULL = 0,
        ACCA = 1,
        ICAEW = 2,
        ICAS = 3,
        CAI = 4,
        CIMA = 5,
        CIPFA = 6,
        NONE = 7,
        AAT = 8
    }
}