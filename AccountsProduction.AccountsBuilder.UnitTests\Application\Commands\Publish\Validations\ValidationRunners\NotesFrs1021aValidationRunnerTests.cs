﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NotesRunner.NoteFrs1021aRunner;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.NoteModels;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations.ValidationRunners
{
    public class NotesFrs1021aValidationRunnerTests
    {
        public class When_validating_intangible_assets_revaluation : NotesFrs1021aValidationRunnerTests
        {

            [Fact]
            public void Should_return_null_if_required_accounts_have_data()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(505, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            IntangibleAssetsRevaluation = null
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);

                var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.IntangibleAssetsRevaluation);
                issue.ShouldBeNull();
            }

            [Fact]
            public void Should_return_warning_if_required_accounts_dont_have_data()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(555, 100),

                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            IntangibleAssetsRevaluation = null
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);

                var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.IntangibleAssetsRevaluation);
                issue.ShouldNotBeNull();
                issue.ErrorCode.ShouldBe(NotesFrs1021aValidations.RevaluationConfig.ErrorCode);
                issue.DisplayName.ShouldBe(NotesFrs1021aValidations.RevaluationConfig.DisplayName);
                issue.ErrorCategory.ShouldBe(NotesFrs1021aValidations.RevaluationConfig.ErrorCategory);
                issue.Target.ShouldBe(NotesFrs1021aValidations.RevaluationConfig.Target);
                issue.Type.ShouldBe(NotesFrs1021aValidations.RevaluationConfig.Type);
                issue.Name.ShouldBe(NotesFrs1021aValidations.RevaluationConfig.Name);
                issue.Description.ShouldBe(NotesFrs1021aValidations.RevaluationConfig.Description);
                issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Notes to the financial statements > Intangible Assets > Revaluation");
            }
        }

        public class When_validating_advance_credit_and_guarantees_granted_to_directors : NotesFrs1021aValidationRunnerTests
        {

            [Fact]
            public void Should_return_null_if_required_accounts_dont_have_data()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(555, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = null
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                            new GroupAccountSubAccountInterval
                            {
                                GroupNo = 631,
                                 AccountIntervalFrom = 727,
                                AccountIntervalTo = 728
                            }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);

                var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.AdvancesCreditGuarantees);
                issue.ShouldBeNull();
            }

            [Fact]
            public void Should_not_return_warning_if_required_accounts_have_data_and_note_is_not_empty()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(727, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = new AdvancesCreditAndGuaranteesGrantedToDirectors
                            {
                                Items = new List<AdvancesCreditAndGuaranteesGrantedToDirectorItem>
                             {
                                 new AdvancesCreditAndGuaranteesGrantedToDirectorItem
                                 {
                                     BalanceOutstandingAtStartOfYear = 1000
                                 }
                             }
                            }
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                            new GroupAccountSubAccountInterval
                            {
                                GroupNo = 631,
                                 AccountIntervalFrom = 727,
                                AccountIntervalTo = 728
                            }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);

                var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.AdvancesCreditGuarantees);
                issue.ShouldBeNull();
            }

            [Fact]
            public void Should_return_warning_if_required_accounts_have_data_and_notes_is_null()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(727, 100),
                    Notes = null
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                            new GroupAccountSubAccountInterval
                            {
                                GroupNo = 631,
                                 AccountIntervalFrom = 727,
                                AccountIntervalTo = 728
                            }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);

                var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.AdvancesCreditGuarantees);
                issue.ShouldNotBeNull();
                issue.ErrorCode.ShouldBe(NotesFrs1021aValidations.AdvancesCreditGuaranteesConfig.ErrorCode);
                issue.DisplayName.ShouldBe(NotesFrs1021aValidations.AdvancesCreditGuaranteesConfig.DisplayName);
                issue.ErrorCategory.ShouldBe(NotesFrs1021aValidations.AdvancesCreditGuaranteesConfig.ErrorCategory);
                issue.Target.ShouldBe(NotesFrs1021aValidations.AdvancesCreditGuaranteesConfig.Target);
                issue.Type.ShouldBe(NotesFrs1021aValidations.AdvancesCreditGuaranteesConfig.Type);
                issue.Name.ShouldBe(NotesFrs1021aValidations.AdvancesCreditGuaranteesConfig.Name);
                issue.Description.ShouldBe(NotesFrs1021aValidations.AdvancesCreditGuaranteesConfig.Description);
                issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Notes to the financial statements > Advances, credits and guarantees granted to Directors > [Select Director]");
            }

            [Fact]
            public void Should_return_warning_if_required_accounts_have_data_and_advance_credit_and_guarantees_is_null()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    TrialBalance = BuildTrialBalance(727, 100),

                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = null
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                            new GroupAccountSubAccountInterval
                            {
                                GroupNo = 631,
                                AccountIntervalFrom = 727,
                                AccountIntervalTo = 728
                            }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);

                var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.AdvancesCreditGuarantees);
                issue.ShouldNotBeNull();
                issue.ErrorCode.ShouldBe(NotesFrs1021aValidations.AdvancesCreditGuaranteesConfig.ErrorCode);
                issue.DisplayName.ShouldBe(NotesFrs1021aValidations.AdvancesCreditGuaranteesConfig.DisplayName);
                issue.ErrorCategory.ShouldBe(NotesFrs1021aValidations.AdvancesCreditGuaranteesConfig.ErrorCategory);
                issue.Target.ShouldBe(NotesFrs1021aValidations.AdvancesCreditGuaranteesConfig.Target);
                issue.Type.ShouldBe(NotesFrs1021aValidations.AdvancesCreditGuaranteesConfig.Type);
                issue.Name.ShouldBe(NotesFrs1021aValidations.AdvancesCreditGuaranteesConfig.Name);
                issue.Description.ShouldBe(NotesFrs1021aValidations.AdvancesCreditGuaranteesConfig.Description);
                issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Notes to the financial statements > Advances, credits and guarantees granted to Directors > [Select Director]");
            }
        }

        public class When_validating_tangible_fixed_assets_notes : NotesFrs1021aValidationRunnerTests
        {

            [Fact]
            public void Should_not_return_warning_as_accounts_have_data_tangible_fixed_assets_notes()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(420, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                            {
                                ValuationInCurrentReportingPeriod = new ValuationInCurrentReportingPeriod
                                {
                                    ValuationDetails = null,
                                    IndependentValuerInvolved = false,
                                    RevaluationBasis = null,
                                    DateOfRevaluation = System.DateTime.MinValue
                                }

                            }
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                                new GroupAccountSubAccountInterval
                                {
                                    GroupNo = 430,
                                    AccountIntervalFrom = 410,
                                    AccountIntervalTo = 430
                                }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);

                var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.TangibleFixedAssetsNotes);
                issue.ShouldBeNull();
            }
            [Fact]
            public void Should_not_return_warning_as_tangible_fixed_assets_historic_and_analysis_notes_have_content()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(420, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                            {
                                ValuationInCurrentReportingPeriod = null,
                                HistoricalCostBreakdown = null,
                                AnalysisOfCostOrValuation = null,

                            }
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                                new GroupAccountSubAccountInterval
                                {
                                    GroupNo = 430,
                                    AccountIntervalFrom = 410,
                                    AccountIntervalTo = 430
                                }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);

                var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.TangibleFixedAssetsNotes);
                issue.ShouldBeNull();
            }

            [Fact]
            public void Should_return_warning_as_no_accounts_have_data_tangible_fixed_assets_notes()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    TrialBalance = BuildTrialBalance(420, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                            {
                                ValuationInCurrentReportingPeriod = new ValuationInCurrentReportingPeriod
                                {
                                    ValuationDetails = null,
                                    IndependentValuerInvolved = false,
                                    RevaluationBasis = null,
                                    DateOfRevaluation = System.DateTime.MinValue
                                }

                            }
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                            new GroupAccountSubAccountInterval
                            {
                                GroupNo = 430,
                                AccountIntervalFrom = 410,
                                AccountIntervalTo = 430
                            }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);

                var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.TangibleFixedAssetsNotes);
                issue.ShouldBeNull();
            }
        }

        public class When_validating_tangible_fixed_assets_historic_and_analysis_notes : NotesFrs1021aValidationRunnerTests
        {
            [Fact]
            public void Should_not_return_error_as_no_accounts_have_no_data_tangible_fixed_assets_historic_and_analysis_notes()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(345, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                            {
                                ValuationInCurrentReportingPeriod = null,
                                HistoricalCostBreakdown = null,
                                AnalysisOfCostOrValuation = null,
                            }
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                                new GroupAccountSubAccountInterval
                                {
                                    GroupNo = 430,
                                    AccountIntervalFrom = 410,
                                    AccountIntervalTo = 430
                                }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);

                var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.TangibleFixedAssetsHistoricAndAnalysisNotes);
                issue.ShouldBeNull();
            }

            [Fact]
            public void Should_not_return_error_as_accounts_have_no_data_tangible_fixed_assets_historic_and_analysis_notes()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(445, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                            {
                                ValuationInCurrentReportingPeriod = null,
                                HistoricalCostBreakdown = null,
                                AnalysisOfCostOrValuation = null,
                            }
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                                new GroupAccountSubAccountInterval
                                {
                                    GroupNo = 430,
                                    AccountIntervalFrom = 410,
                                    AccountIntervalTo = 430
                                }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);

                var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.TangibleFixedAssetsHistoricAndAnalysisNotes);
                issue.ShouldBeNull();
            }

            [Fact]
            public void Should_return_error_as_accounts_have_data_for_historic_revalued_asset_class_complete_but_not_analysis_notes()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(420, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                            {
                                ValuationInCurrentReportingPeriod = null,
                                HistoricalCostBreakdown = new HistoricalCostBreakdown
                                {
                                    RevaluedAssetClass = "Revalued Asset Class",
                                    RevaluedClassPronoun = "Revalued Class Pronoun",
                                    CurrentReportingPeriodAccumulatedDepreciation = null,
                                    CurrentReportingPeriodCost = null,
                                },
                                AnalysisOfCostOrValuation = null,
                            }
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                                new GroupAccountSubAccountInterval
                                {
                                    GroupNo = 430,
                                    AccountIntervalFrom = 410,
                                    AccountIntervalTo = 430
                                }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);
                CheckTangibleFixedAssestsAnalysisOfCostAndValuationNoteConfig(issues);
            }


            [Fact]
            public void Should_note_return_error_as_accounts_have_data_for_historic_revalued_asset_class_and_analysis_notes_not_complete()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(420, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                            {
                                ValuationInCurrentReportingPeriod = null,
                                HistoricalCostBreakdown = new HistoricalCostBreakdown
                                {
                                    RevaluedAssetClass = "Revalued Asset Class",
                                    RevaluedClassPronoun = null,
                                    CurrentReportingPeriodAccumulatedDepreciation = null,
                                    CurrentReportingPeriodCost = null,
                                },
                                AnalysisOfCostOrValuation = null,
                            }
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                                new GroupAccountSubAccountInterval
                                {
                                    GroupNo = 430,
                                    AccountIntervalFrom = 410,
                                    AccountIntervalTo = 430
                                }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);
                var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.TangibleFixedAssetsHistoricAndAnalysisNotes);
                issue.ShouldBeNull();
            }

            [Fact]
            public void Should_not_return_error_as_accounts_have_data_for_historic_revalued_class_pronoun_and_not_analysis_notes_not_complete()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(420, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                            {
                                ValuationInCurrentReportingPeriod = null,
                                HistoricalCostBreakdown = new HistoricalCostBreakdown
                                {
                                    RevaluedAssetClass = null,
                                    RevaluedClassPronoun = "Revalued Class Pronoun",
                                    CurrentReportingPeriodAccumulatedDepreciation = null,
                                    CurrentReportingPeriodCost = null,
                                },
                                AnalysisOfCostOrValuation = null,
                            }
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                                new GroupAccountSubAccountInterval
                                {
                                    GroupNo = 430,
                                    AccountIntervalFrom = 410,
                                    AccountIntervalTo = 430
                                }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);

                var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.TangibleFixedAssetsHistoricAndAnalysisNotes);
                issue.ShouldBeNull();
            }

            [Fact]
            public void Should_return_error_as_accounts_have_data_for_analysis_item_list_but_not_historic_notes()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(420, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                            {
                                ValuationInCurrentReportingPeriod = null,
                                HistoricalCostBreakdown = null,
                                AnalysisOfCostOrValuation = new AnalysisOfCostOrValuation
                                {
                                    AnalysisOfCostOrValuationItems = new List<AnalysisOfCostOrValuationItem>
                                        {
                                            new AnalysisOfCostOrValuationItem
                                            {
                                                Index = 3,
                                                Year = 2020,
                                                LandAndBuildings = 1.1m,
                                                PlantAndMachineryEtc = 2.2m
                                            },
                                            new AnalysisOfCostOrValuationItem
                                            {
                                                Index = 4,
                                                Year = 2021,
                                                LandAndBuildings = 3.3m,
                                                PlantAndMachineryEtc = 4.4m
                                            },
                                            new AnalysisOfCostOrValuationItem
                                            {
                                                Index = 5,
                                                Year = 2022,
                                                LandAndBuildings = -3.3m,
                                                PlantAndMachineryEtc = -4.4m
                                            }
                                        },
                                    CostLandAndBuildings = null,
                                    CostPlantAndMachineryEtc = null,
                                    TotalLandAndBuildings = 4.4m,
                                    TotalPlantAndMachineryEtc = 6.6m
                                }
                            }
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                                new GroupAccountSubAccountInterval
                                {
                                    GroupNo = 430,
                                    AccountIntervalFrom = 410,
                                    AccountIntervalTo = 430
                                }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);
                CheckTangibleFixedAssestsHistoricBreakdownNoteConfig(issues);
            }

            [Fact]
            public void Should_return_error_as_accounts_have_data_for_analysis_cost_land_and_buildings_but_not_historic_notes()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(420, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                            {
                                ValuationInCurrentReportingPeriod = null,
                                HistoricalCostBreakdown = null,
                                AnalysisOfCostOrValuation = new AnalysisOfCostOrValuation
                                {
                                    AnalysisOfCostOrValuationItems = null,
                                    CostLandAndBuildings = 3.3m,
                                    CostPlantAndMachineryEtc = null,
                                    TotalLandAndBuildings = 3.3m,
                                    TotalPlantAndMachineryEtc = null
                                }
                            }
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                                new GroupAccountSubAccountInterval
                                {
                                    GroupNo = 430,
                                    AccountIntervalFrom = 410,
                                    AccountIntervalTo = 430
                                }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);
                CheckTangibleFixedAssestsHistoricBreakdownNoteConfig(issues);
            }


            [Fact]
            public void Should_return_error_as_accounts_have_data_for_analysis_cost_land_and_buildings_and_listis_empty_but_not_historic_notes()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(420, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                            {
                                ValuationInCurrentReportingPeriod = null,
                                HistoricalCostBreakdown = null,
                                AnalysisOfCostOrValuation = new AnalysisOfCostOrValuation
                                {
                                    AnalysisOfCostOrValuationItems = new List<AnalysisOfCostOrValuationItem>(),
                                    CostLandAndBuildings = 3.3m,
                                    CostPlantAndMachineryEtc = null,
                                    TotalLandAndBuildings = 3.3m,
                                    TotalPlantAndMachineryEtc = null
                                }
                            }
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                                new GroupAccountSubAccountInterval
                                {
                                    GroupNo = 430,
                                    AccountIntervalFrom = 410,
                                    AccountIntervalTo = 430
                                }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);
                CheckTangibleFixedAssestsHistoricBreakdownNoteConfig(issues);
            }

            [Fact]
            public void Should_return_error_as_accounts_have_data_for_analysis_cost_plant_and_machinery_etc_but_not_historic_notes()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(420, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                            {
                                ValuationInCurrentReportingPeriod = null,
                                HistoricalCostBreakdown = null,
                                AnalysisOfCostOrValuation = new AnalysisOfCostOrValuation
                                {
                                    AnalysisOfCostOrValuationItems = null,
                                    CostLandAndBuildings = null,
                                    CostPlantAndMachineryEtc = 3.3m,
                                    TotalLandAndBuildings = null,
                                    TotalPlantAndMachineryEtc = 3.3m
                                }
                            }
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                                new GroupAccountSubAccountInterval
                                {
                                    GroupNo = 430,
                                    AccountIntervalFrom = 410,
                                    AccountIntervalTo = 430
                                }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);
                CheckTangibleFixedAssestsHistoricBreakdownNoteConfig(issues);
            }

            [Fact]
            public void Should_not_return_error_as_accounts_both_have_data_tangible_fixed_assets_historic_and_analysis__land_and_buildings_etc_notes()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(420, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                            {
                                ValuationInCurrentReportingPeriod = null,
                                HistoricalCostBreakdown = new HistoricalCostBreakdown
                                {
                                    RevaluedAssetClass = "Revalued Asset Class",
                                    RevaluedClassPronoun = "Revalued Class Pronoun",
                                    CurrentReportingPeriodAccumulatedDepreciation = null,
                                    CurrentReportingPeriodCost = null,
                                },
                                AnalysisOfCostOrValuation = new AnalysisOfCostOrValuation
                                {
                                    AnalysisOfCostOrValuationItems = null,
                                    CostLandAndBuildings = null,
                                    CostPlantAndMachineryEtc = 3.3m,
                                    TotalLandAndBuildings = null,
                                    TotalPlantAndMachineryEtc = 3.3m
                                }
                            }
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                                new GroupAccountSubAccountInterval
                                {
                                    GroupNo = 430,
                                    AccountIntervalFrom = 410,
                                    AccountIntervalTo = 430
                                }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);
                var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.TangibleFixedAssetsHistoricAndAnalysisNotes);
                issue.ShouldBeNull();
            }

            [Fact]
            public void Should_not_return_error_as_accounts_both_have_data_tangible_fixed_assets_historic_and_analysis_cost_plant_and_machinery_etc_notes()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {

                    TrialBalance = BuildTrialBalance(420, 100),
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            TangibleFixedAssetsNotes = new TangibleFixedAssetsNotes
                            {
                                ValuationInCurrentReportingPeriod = null,
                                HistoricalCostBreakdown = new HistoricalCostBreakdown
                                {
                                    RevaluedAssetClass = "Revalued Asset Class",
                                    RevaluedClassPronoun = "Revalued Class Pronoun",
                                    CurrentReportingPeriodAccumulatedDepreciation = null,
                                    CurrentReportingPeriodCost = null,
                                },
                                AnalysisOfCostOrValuation = new AnalysisOfCostOrValuation
                                {
                                    AnalysisOfCostOrValuationItems = null,
                                    CostLandAndBuildings = 3.3m,
                                    CostPlantAndMachineryEtc = null,
                                    TotalLandAndBuildings = null,
                                    TotalPlantAndMachineryEtc = 3.3m
                                }
                            }
                        }
                    }
                };

                var mockRepository = new Mock<IGroupAccountSubAccountIntervalRepository>();
                mockRepository.Setup(m =>
                        m.GetCachedGroupAccountSubAccountIntervalList())
                    .Returns(new[]
                    {
                                new GroupAccountSubAccountInterval
                                {
                                    GroupNo = 430,
                                    AccountIntervalFrom = 410,
                                    AccountIntervalTo = 430
                                }
                    });

                var issues = new NotesFrs1021aValidationRunner(mockRepository.Object).Validate(accountsBuilder);
                var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.TangibleFixedAssetsHistoricAndAnalysisNotes);
                issue.ShouldBeNull();
            }
        }

        private TrialBalance BuildTrialBalance(int accountCode, decimal amount)
        {
            var trialBalance = new TrialBalance
            {
                TrialBalances = new List<PeriodTrialBalance>
                            {
                                new PeriodTrialBalance
                                {
                                    AccountCode = accountCode,
                                    Amount = amount
                                }
                            }
            };

            return trialBalance;
        }

        private void CheckTangibleFixedAssestsHistoricBreakdownNoteConfig(IList<ValidationIssue> issues)
        {
            var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.TangibleFixedAssetsHistoricAndAnalysisNotes);
            issue.ShouldNotBeNull();
            issue.ErrorCode.ShouldBe(NotesFrs1021aValidations.TangibleFixedAssestsHistoricalCostBreakdownNoteConfig.ErrorCode);
            issue.DisplayName.ShouldBe(NotesFrs1021aValidations.TangibleFixedAssestsHistoricalCostBreakdownNoteConfig.DisplayName);
            issue.ErrorCategory.ShouldBe(NotesFrs1021aValidations.TangibleFixedAssestsHistoricalCostBreakdownNoteConfig.ErrorCategory);
            issue.Target.ShouldBe(NotesFrs1021aValidations.TangibleFixedAssestsHistoricalCostBreakdownNoteConfig.Target);
            issue.Type.ShouldBe(NotesFrs1021aValidations.TangibleFixedAssestsHistoricalCostBreakdownNoteConfig.Type);
            issue.Name.ShouldBe(NotesFrs1021aValidations.TangibleFixedAssestsHistoricalCostBreakdownNoteConfig.Name);
            issue.Description.ShouldBe(NotesFrs1021aValidations.TangibleFixedAssestsHistoricalCostBreakdownNoteConfig.Description);
            issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Notes to the financial statements > Tangible Fixed Assets Notes > Historical Cost Breakdown");
        }


        private void CheckTangibleFixedAssestsAnalysisOfCostAndValuationNoteConfig(IList<ValidationIssue> issues)
        {
            var issue = issues.SingleOrDefault(x => x.Name == NotesFrs1021aValidations.TangibleFixedAssetsHistoricAndAnalysisNotes);
            issue.ShouldNotBeNull();
            issue.ErrorCode.ShouldBe(NotesFrs1021aValidations.TangibleFixedAssestsAnalysisOfCostAndValuationNoteConfig.ErrorCode);
            issue.DisplayName.ShouldBe(NotesFrs1021aValidations.TangibleFixedAssestsAnalysisOfCostAndValuationNoteConfig.DisplayName);
            issue.ErrorCategory.ShouldBe(NotesFrs1021aValidations.TangibleFixedAssestsAnalysisOfCostAndValuationNoteConfig.ErrorCategory);
            issue.Target.ShouldBe(NotesFrs1021aValidations.TangibleFixedAssestsAnalysisOfCostAndValuationNoteConfig.Target);
            issue.Type.ShouldBe(NotesFrs1021aValidations.TangibleFixedAssestsAnalysisOfCostAndValuationNoteConfig.Type);
            issue.Name.ShouldBe(NotesFrs1021aValidations.TangibleFixedAssestsAnalysisOfCostAndValuationNoteConfig.Name);
            issue.Description.ShouldBe(NotesFrs1021aValidations.TangibleFixedAssestsAnalysisOfCostAndValuationNoteConfig.Description);
            issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Notes to the financial statements > Tangible Fixed Assets Notes > Analysis Of Cost Or Valuation");
        }

    }
}
