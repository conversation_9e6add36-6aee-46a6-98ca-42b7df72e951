﻿using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.ScreenValues;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class DataScreensValueMapper : Profile
    {
        public DataScreensValueMapper()
        {
            CreateMap<Domain.DataScreens.DataScreenValue, ScreenValuesResponseMessage>()
                .ReverseMap();
            CreateMap<Domain.DataScreens.PeriodScreenValue, ScreenValues>()
                .ReverseMap();
            CreateMap<Domain.DataScreens.ScreenField, ScreenField>()
                .ReverseMap();
        }
    }
}
