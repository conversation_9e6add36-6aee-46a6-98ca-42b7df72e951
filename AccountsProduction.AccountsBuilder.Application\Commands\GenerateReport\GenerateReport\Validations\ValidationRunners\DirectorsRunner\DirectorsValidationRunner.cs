﻿using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.
    DirectorsRunner
{
    public class DirectorsValidationRunner : ValidationRunner
    {
        private const string DirectorInvolvementType = "Director";

        protected override Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>>
            ValidationRules =>
            new Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>>
            {
                {
                    DirectorValidations.Director,
                    ValidateDirectors
                },
                {
                    DirectorValidations.ActiveDirector,
                    ValidateActiveDirector
                }
            };

        private static ValidationIssue ValidateDirectors(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var directors =
                accountsBuilder.InvolvementsData.Involvements.Where(involvement =>
                    involvement.InvolvementType == DirectorInvolvementType);

            if (!directors.Any())
                return DirectorValidations.DirectorRuleConfig.MapToValidationIssue();

            return null;
        }

        private static ValidationIssue ValidateActiveDirector(
            Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var activeDirectors =
                accountsBuilder.InvolvementsData.Involvements.Where(involvement =>
                    Validator.IsInvolvementActive(involvement, DirectorInvolvementType,
                        accountsBuilder.EntityModificationTime.Date));


            if (!activeDirectors.Any())
                return DirectorValidations.ActiveDirectorRuleConfig.MapToValidationIssue();

            return null;
        }
    }
}