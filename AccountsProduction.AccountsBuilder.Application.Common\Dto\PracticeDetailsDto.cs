﻿namespace AccountsProduction.AccountsBuilder.Application.Common.Dto
{
    public class PracticeDetailsDto
    {
        public int ReferredType { get; set; }

        public int SupervisingBody { get; set; }

        public string Name { get; set; } = null!;

        public string AddressLine1 { get; set; } = null!;

        public string AddressLine2 { get; set; } = null!;

        public string AddressLine3 { get; set; } = null!;

        public string AddressTown { get; set; } = null!;

        public string AddressCounty { get; set; } = null!;

        public string AddressPostcode { get; set; } = null!;

        public string AddressCountry { get; set; } = null!;
    }
}
