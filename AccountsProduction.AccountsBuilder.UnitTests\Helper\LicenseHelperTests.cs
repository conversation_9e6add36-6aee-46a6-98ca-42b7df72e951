using AccountsProduction.AccountsBuilder.Application.Common.Helper;
using Iris.AccountsProduction.Common.Toolkit.Utils;
using Iris.Platform.WebApi.Infrastructure.Licenses;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Helper
{
    public class LicenseHelperTests
    {
        [Fact]
        public void Should_return_false_if_accountsprod_license_is_active()
        {
            var result = LicenseHelper.IsAccountsProdTrialLicense(GetUserContext(false));

            result.ShouldBe(false);
        }

        [Fact]
        public void Should_return_true_if_accountsprod_license_is_free_trial()
        {
            var result = LicenseHelper.IsAccountsProdTrialLicense(GetUserContext(true));

            result.ShouldBe(true);
        }

        [Fact]
        public void Should_return_false_if_usercontext_is_null()
        {
            var result = LicenseHelper.IsAccountsProdTrialLicense(null);

            result.ShouldBe(false);
        }

        [Fact]
        public void Should_return_false_if_usercontext_has_empty_licenses_list()
        {
            var result = LicenseHelper.IsAccountsProdTrialLicense(new UserContext());

            result.ShouldBe(false);
        }

        [Fact]
        public void Should_return_true_if_user_has_enterprise_license()
        {
            var userContext = new UserContext
            {
                Licenses = new List<License>
                {
                    new License
                    {
                        Code = "AP_ENTERPRISE",
                        IsTrial = false
                    }
                }
            };

            var result = LicenseHelper.HasEnterpriseLicense(userContext);

            result.ShouldBe(true);
        }

        [Fact]
        public void Should_return_false_if_user_has_no_enterprise_license()
        {
            var userContext = new UserContext
            {
                Licenses = new List<License>
                {
                    new License
                    {
                        Code = APLicense.Name,
                        IsTrial = true
                    }
                }
            };

            var result = LicenseHelper.HasEnterpriseLicense(userContext);

            result.ShouldBe(false);
        }

        [Fact]
        public void Should_return_true_for_ifrs_eligibility_when_enterprise_license_and_ifrs_enabled()
        {
            var userContext = new UserContext
            {
                Licenses = new List<License>
                {
                    new License
                    {
                        Code = "AP_ENTERPRISE",
                        IsTrial = false
                    }
                }
            };

            var result = LicenseHelper.IsIFRSEligible(userContext, true);

            result.ShouldBe(true);
        }

        [Fact]
        public void Should_return_false_for_ifrs_eligibility_when_no_enterprise_license()
        {
            var userContext = new UserContext
            {
                Licenses = new List<License>
                {
                    new License
                    {
                        Code = APLicense.Name,
                        IsTrial = true
                    }
                }
            };

            var result = LicenseHelper.IsIFRSEligible(userContext, true);

            result.ShouldBe(false);
        }

        [Fact]
        public void Should_return_false_for_ifrs_eligibility_when_ifrs_not_enabled()
        {
            var userContext = new UserContext
            {
                Licenses = new List<License>
                {
                    new License
                    {
                        Code = "AP_ENTERPRISE",
                        IsTrial = false
                    }
                }
            };

            var result = LicenseHelper.IsIFRSEligible(userContext, false);

            result.ShouldBe(false);
        }

        private UserContext GetUserContext(bool isTrial)
        {
            return new UserContext
            {
                Licenses = new List<License>
                    {
                        new License
                        {
                            Code = APLicense.Name,
                            IsTrial = isTrial
                        }
                    }
            };
        }
    }
}
