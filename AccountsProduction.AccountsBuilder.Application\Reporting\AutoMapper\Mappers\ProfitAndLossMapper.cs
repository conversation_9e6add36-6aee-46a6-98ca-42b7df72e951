﻿using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Domain.ProfitAndLoss;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper.Mappers
{
    public class ProfitAndLossMapper : Profile
    {
        public ProfitAndLossMapper()
        {
            CreateMap<ProfitAndLossMessage, ProfitAndLossFRS105>()
                .ForMember(x => x.MembersRemunerationAsExpense, opt => opt.MapFrom(dto => dto.MembersRemunerationAsExpenseNumber()))
                .ForMember(x => x.ProfitLossAvailableForDiscretionaryDivision, opt => opt.MapFrom(dto => dto.ProfitLossAvailableForDiscretionaryDivisionNumber()))
                .ForMember(x => x.CostOfRawMaterialsAndConsumables, opt => opt.MapFrom(dto => dto.CostOfRawMaterialsAndConsumablesNumber()))
                .ForMember(x => x.DepreciationAndOtherAmountsWrittenOffAssets, opt => opt.MapFrom(dto => dto.DepreciationAndOtherAmountsWrittenOffAssetsNumber()))
                .ForMember(x => x.OtherCharges, opt => opt.MapFrom(dto => dto.OtherChargesNumber()))
                .ForMember(x => x.StaffCosts, opt => opt.MapFrom(dto => dto.StaffCostsNumber()))
                .ForMember(x => x.Tax, opt => opt.MapFrom(dto => dto.TaxNumber()))
                .ForMember(x => x.Turnover, opt => opt.MapFrom(dto => dto.TurnoverNumber()))
                .ForMember(x => x.OtherIncome, opt => opt.MapFrom(dto => dto.OtherIncomeNumber()))
                .ForMember(x => x.ReportingPeriod, expression => expression.UseDestinationValue())
                .ForMember(x => x.AccountPeriodId, expression => expression.Ignore())
                .ForMember(x => x.ClientId, expression => expression.Ignore());

            CreateMap<ProfitAndLossMessage, ProfitAndLossCompaniesAct>()
                .ForMember(x => x.Turnover, opt => opt.MapFrom(dto => dto.TurnoverNumber()))
                .ForMember(x => x.CostOfSales, opt => opt.MapFrom(dto => dto.CostOfSalesNumber()))
                .ForMember(x => x.GrossProfitLoss, opt => opt.MapFrom(dto => dto.GrossProfitLossNumber()))
                .ForMember(x => x.DistributionExpenses, opt => opt.MapFrom(dto => dto.DistributionExpensesNumber()))
                .ForMember(x => x.AdministrativeExpenses, opt => opt.MapFrom(dto => dto.AdministrativeExpensesNumber()))
                .ForMember(x => x.OtherOperatingIncome, opt => opt.MapFrom(dto => dto.OtherOperatingIncomeNumber()))
                .ForMember(x => x.GainLossOnRevaluation1, opt => opt.MapFrom(dto => dto.GainLossOnRevaluation1Number()))
                .ForMember(x => x.OperatingProfitLoss, opt => opt.MapFrom(dto => dto.OperatingProfitLossNumber()))
                .ForMember(x => x.ExceptionalItems, opt => opt.MapFrom(dto => dto.ExceptionalItemsNumber()))
                .ForMember(x => x.IncomeFromSharesInGroupUndertakings, opt => opt.MapFrom(dto => dto.IncomeFromSharesInGroupUndertakingsNumber()))
                .ForMember(x => x.IncomeFromInterestInAssociatedUndertakings, opt => opt.MapFrom(dto => dto.IncomeFromInterestInAssociatedUndertakingsNumber()))
                .ForMember(x => x.IncomeFromOtherParticipatingInterests, opt => opt.MapFrom(dto => dto.IncomeFromOtherParticipatingInterestsNumber()))
                .ForMember(x => x.IncomeFromFixedAssetInvestments, opt => opt.MapFrom(dto => dto.IncomeFromFixedAssetInvestmentsNumber()))
                .ForMember(x => x.InterestReceivableAndSimilarIncome, opt => opt.MapFrom(dto => dto.InterestReceivableAndSimilarIncomeNumber()))
                .ForMember(x => x.OtherFinanceIncome, opt => opt.MapFrom(dto => dto.OtherFinanceIncomeNumber()))
                .ForMember(x => x.AmountsWrittenOffInvestments, opt => opt.MapFrom(dto => dto.AmountsWrittenOffInvestmentsNumber()))
                .ForMember(x => x.GainLossOnRevaluation2, opt => opt.MapFrom(dto => dto.GainLossOnRevaluation2Number()))
                .ForMember(x => x.InterestPayableAndSimilarExpenses, opt => opt.MapFrom(dto => dto.InterestPayableAndSimilarExpensesNumber()))
                .ForMember(x => x.OtherFinanceCosts, opt => opt.MapFrom(dto => dto.OtherFinanceCostsNumber()))
                .ForMember(x => x.ProfitLossOnOrdinaryActivitiesBeforeTaxation, opt => opt.MapFrom(dto => dto.ProfitLossOnOrdinaryActivitiesBeforeTaxationNumber()))
                .ForMember(x => x.Taxation, opt => opt.MapFrom(dto => dto.TaxationNumber()))
                .ForMember(x => x.ProfitLossForTheFinancialYear, opt => opt.MapFrom(dto => dto.ProfitLossForTheFinancialYearNumber()))
                .ForMember(x => x.NonControllingInterests, opt => opt.MapFrom(dto => dto.NonControllingInterestsNumber()))
                .ForMember(x => x.MembersRemunerationAsExpense, opt => opt.MapFrom(dto => dto.MembersRemunerationAsExpenseNumber()))
                .ForMember(x => x.ProfitLossAvailableForDiscretionaryDivision, opt => opt.MapFrom(dto => dto.ProfitLossAvailableForDiscretionaryDivisionNumber()))
                .ForMember(x => x.ReportingPeriod, expression => expression.UseDestinationValue())
                .ForMember(x => x.AccountPeriodId, expression => expression.Ignore())
                .ForMember(x => x.ClientId, expression => expression.Ignore());

            CreateMap<ProfitAndLossMessage, ProfitAndLossNonCorp>()
                .ForMember(x => x.Sales, opt => opt.MapFrom(dto => dto.SalesNumber()))
                .ForMember(x => x.CostOfSales, opt => opt.MapFrom(dto => dto.CostOfSalesNumber()))
                .ForMember(x => x.OtherIncome, opt => opt.MapFrom(dto => dto.OtherIncomeNumber()))
                .ForMember(x => x.Expenses, opt => opt.MapFrom(dto => dto.ExpensesNumber()))
                .ForMember(x => x.FinanceCosts, opt => opt.MapFrom(dto => dto.FinanceCostsNumber()))
                .ForMember(x => x.PartnerAppropriations, opt => opt.MapFrom(dto => dto.PartnerAppropriationsNumber()))
                .ForMember(x => x.Depreciation, opt => opt.MapFrom(dto => dto.DepreciationNumber()))
                .ForMember(x => x.ReportingPeriod, expression => expression.UseDestinationValue())
                .ForMember(x => x.AccountPeriodId, expression => expression.Ignore())
                .ForMember(x => x.ClientId, expression => expression.Ignore());
        }
    }
}
