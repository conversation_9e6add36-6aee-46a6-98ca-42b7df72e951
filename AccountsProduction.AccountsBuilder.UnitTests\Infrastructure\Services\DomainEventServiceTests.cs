﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Application.IntegrationEvents;
using AccountsProduction.AccountsBuilder.Infrastructure.Services;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Infrastructure.Services
{
    public class DomainEventServiceTests
    {
        [Fact]
        public async Task Should_publish_notification()
        {
            var mediator = new Mock<IMediator>();
            var loggerMock = new Mock<ILogger<DomainEventService>>();

            var domainEventService = new DomainEventService(mediator.Object, loggerMock.Object);

            await domainEventService.Publish(new NotificationTrialBalanceChanged(It.IsAny<Guid>(), It.IsAny<Guid>(), It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<TrialBalanceDto>()), CancellationToken.None);

            mediator.Verify(x => x.Publish(It.IsAny<INotification>(), CancellationToken.None), Times.Once);
        }
    }
}
