using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.IFRS;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Domain;
using Iris.AccountsProduction.Common.Toolkit.Utils;
using Iris.Platform.WebApi.Infrastructure.Licenses;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.ReportingDomain.IFRS
{
    public class IFRSFullCHReportingDomainMapperTests
    {
        private readonly Mock<UserContext> _userContext;
        private readonly IFRSFullCHReportingDomainMapper _mapper;

        public IFRSFullCHReportingDomainMapperTests()
        {
            _userContext = new Mock<UserContext>();
            var autoMapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _mapper = new IFRSFullCHReportingDomainMapper(_userContext.Object, autoMapper);
        }

        [Theory]
        [InlineData("Active", null)]
        [InlineData("Trial", "IRIS Elements Accounts Production Trial Version")]
        public void Should_map_correct_data(string licenseStatus, string watermarkText)
        {
            var isTrialAPLicense = licenseStatus.Equals("Trial");

            _userContext.Setup(x => x.Licenses).Returns(new List<License>
                {
                    new License() { Code = APLicense.Name, IsTrial = isTrialAPLicense}
                });

            var entitySetup = new EntitySetup { IndependentReviewType = "Accountants" };
            var reportingStandardDetail = new ReportingStandard
            {
                Id = "632c777af91a933171f73314",
                Name = "IFRS - Full CH",
                Type = "IFRS - Full CH",
                Version = ReportingStandardVersion.Full
            };

            var accountsBuilder = TestData.GetAccountsBuilder(entitySetup, reportingStandardDetail, isTrialAPLicense);

            var result = _mapper.Map(accountsBuilder);

            result.ShouldNotBeNull();
            result.ReportType.ShouldBe(ReportStandardType.IFRS_FULL_CH);
            result.WatermarkText.ShouldBe(watermarkText);
        }

        [Fact]
        public void Should_have_correct_report_standard_type()
        {
            _mapper.ReportStandardType.ShouldBe(ReportStandardType.IFRS_FULL_CH);
        }
    }
}
