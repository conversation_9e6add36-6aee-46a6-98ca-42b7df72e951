﻿namespace AccountsProduction.AccountsBuilder.Domain
{
    public class EntitySetup
    {
        public string EntitySize { get; set; }

        public string IndependentReviewType { get; set; }

        public string ReportingStandard { get; set; }

        public string Terminology { get; set; }

        public string TradingStatus { get; set; }

        public string DormantStatus { get; set; }

        public string ChoiceOfStatement { get; set; }

        public string CIC34Report { get; set; }

        public string PracticeAddress { get; set; }

        public DateTime EntityModificationTime { get; set; }
        public void UpdateModificationTime()
        {
            EntityModificationTime = DateTime.UtcNow;
        }
    }
}
