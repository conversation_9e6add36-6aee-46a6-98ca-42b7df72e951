﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.IntangibleAssetsRunner;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels;
using AccountsProduction.AccountsBuilder.Domain.NoteModels;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations.ValidationRunners
{
    public class IntangibleAssetsValidationRunnerTests
    {
        private readonly AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder _accountsBuilder;
        private readonly Mock<IGroupAccountSubAccountIntervalRepository> _repository;

        public IntangibleAssetsValidationRunnerTests()
        {
            _accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder { TrialBalance = new TrialBalance() };
            _accountsBuilder.Notes = new Notes
            {
                CurrentPeriod = new NotesData
                {
                    IntangibleAssetsRevaluation = "IntangibleAssetsRevaluation"
                }
            };
            _repository = new Mock<IGroupAccountSubAccountIntervalRepository>();
            _repository.Setup(m => m.GetCachedGroupAccountSubAccountIntervalList()).Returns(new[]
            {
                new GroupAccountSubAccountInterval
                {
                    GroupNo = 421,
                    AccountIntervalFrom = 502,
                    AccountIntervalTo = 505
                }
            });
        }

        [Fact]
        public void Should_trigger_validation_if_no_accounts_found()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>();

            var issues = new IntangibleAssetsValidationRunner(new Mock<IGroupAccountSubAccountIntervalRepository>().Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == IntangibleAssetsValidations.IntangibleAssets);
            issue.ShouldNotBeNull();
            issue.ErrorCode.ShouldBe(IntangibleAssetsValidations.IntangibleAssetsConfig.ErrorCode);
            issue.DisplayName.ShouldBe(IntangibleAssetsValidations.IntangibleAssetsConfig.DisplayName);
            issue.ErrorCategory.ShouldBe(IntangibleAssetsValidations.IntangibleAssetsConfig.ErrorCategory);
            issue.Target.ShouldBe(IntangibleAssetsValidations.IntangibleAssetsConfig.Target);
            issue.Type.ShouldBe(IntangibleAssetsValidations.IntangibleAssetsConfig.Type);
            issue.Name.ShouldBe(IntangibleAssetsValidations.IntangibleAssetsConfig.Name);
            issue.Description.ShouldBe(IntangibleAssetsValidations.IntangibleAssetsConfig.Description);
            issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Intangible Assets");
        }

        [Fact]
        public void Should_not_trigger_validation_if_accounts_are_found()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 503,
                    Amount = 200
                }
            };

            var issues = new IntangibleAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == IntangibleAssetsValidations.IntangibleAssets);
            issue.ShouldBeNull();
        }

        [Fact]
        public void Should_trigger_Goodwill_validation_when_found_account_502()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 502,
                    Amount = 100
                }
            };

            var issues = new IntangibleAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == IntangibleAssetsValidations.IntangibleAssetsGoodwill);
            issue.ShouldNotBeNull();
            issue.ErrorCode.ShouldBe(IntangibleAssetsValidations.GoodwillConfig.ErrorCode);
            issue.DisplayName.ShouldBe(IntangibleAssetsValidations.GoodwillConfig.DisplayName);
            issue.ErrorCategory.ShouldBe(IntangibleAssetsValidations.GoodwillConfig.ErrorCategory);
            issue.Target.ShouldBe(IntangibleAssetsValidations.GoodwillConfig.Target);
            issue.Type.ShouldBe(IntangibleAssetsValidations.GoodwillConfig.Type);
            issue.Name.ShouldBe(IntangibleAssetsValidations.GoodwillConfig.Name);
            issue.Description.ShouldBe(IntangibleAssetsValidations.GoodwillConfig.Description);
            issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Intangible Assets > Goodwill");
        }

        [Fact]
        public void Should_not_trigger_Goodwill_validation_when_found_account_502_and_Goodwill_has_value()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 502,
                    Amount = 100
                }
            };
            _accountsBuilder.AccountingPolicies = new AccountingPolicies
            {
                CurrentPeriodAccountingPolicies = new AccountingPoliciesData
                {
                    IntangibleAssets = new IntangibleAssets
                    {
                        Goodwill = new AssetsAdjustment
                        {
                            AlternativeBasis = "text",
                            ReducingBalanceBasis = 1,
                            StraightLineBasis = 2
                        }
                    }
                }
            };

            var issues = new IntangibleAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == IntangibleAssetsValidations.IntangibleAssetsGoodwill);
            issue.ShouldBeNull();
        }

        [Fact]
        public void Should_trigger_PatentsAndLicenses_validation_when_found_account_503()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 503,
                    Amount = 100
                }
            };

            var issues = new IntangibleAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == IntangibleAssetsValidations.IntangibleAssetsPatentsAndLicenses);
            issue.ShouldNotBeNull();
            issue.ErrorCode.ShouldBe(IntangibleAssetsValidations.PatentsAndLicensesConfig.ErrorCode);
            issue.DisplayName.ShouldBe(IntangibleAssetsValidations.PatentsAndLicensesConfig.DisplayName);
            issue.ErrorCategory.ShouldBe(IntangibleAssetsValidations.PatentsAndLicensesConfig.ErrorCategory);
            issue.Target.ShouldBe(IntangibleAssetsValidations.PatentsAndLicensesConfig.Target);
            issue.Type.ShouldBe(IntangibleAssetsValidations.PatentsAndLicensesConfig.Type);
            issue.Name.ShouldBe(IntangibleAssetsValidations.PatentsAndLicensesConfig.Name);
            issue.Description.ShouldBe(IntangibleAssetsValidations.PatentsAndLicensesConfig.Description);
            issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Intangible Assets > Patents And Licenses");
        }

        [Fact]
        public void Should_not_trigger_PatentsAndLicenses_validation_when_found_account_503_and_PatentsAndLicenses_has_value()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 503,
                    Amount = 100
                }
            };
            _accountsBuilder.AccountingPolicies = new AccountingPolicies
            {
                CurrentPeriodAccountingPolicies = new AccountingPoliciesData
                {
                    IntangibleAssets = new IntangibleAssets
                    {
                        PatentsAndLicenses = new AssetsAdjustment
                        {
                            AlternativeBasis = "text",
                            ReducingBalanceBasis = 1,
                            StraightLineBasis = 2
                        }
                    }
                }
            };

            var issues = new IntangibleAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == IntangibleAssetsValidations.IntangibleAssetsPatentsAndLicenses);
            issue.ShouldBeNull();
        }

        [Fact]
        public void Should_trigger_DevelopmentCosts_validation_when_found_account_504()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 504,
                    Amount = 100
                }
            };

            var issues = new IntangibleAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == IntangibleAssetsValidations.IntangibleAssetsDevelopmentCosts);
            issue.ShouldNotBeNull();
            issue.ErrorCode.ShouldBe(IntangibleAssetsValidations.DevelopmentCostsConfig.ErrorCode);
            issue.DisplayName.ShouldBe(IntangibleAssetsValidations.DevelopmentCostsConfig.DisplayName);
            issue.ErrorCategory.ShouldBe(IntangibleAssetsValidations.DevelopmentCostsConfig.ErrorCategory);
            issue.Target.ShouldBe(IntangibleAssetsValidations.DevelopmentCostsConfig.Target);
            issue.Type.ShouldBe(IntangibleAssetsValidations.DevelopmentCostsConfig.Type);
            issue.Name.ShouldBe(IntangibleAssetsValidations.DevelopmentCostsConfig.Name);
            issue.Description.ShouldBe(IntangibleAssetsValidations.DevelopmentCostsConfig.Description);
            issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Intangible Assets > Development Costs");
        }

        [Fact]
        public void Should_not_trigger_DevelopmentCosts_validation_when_found_account_504_and_DevelopmentCosts_has_value()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 504,
                    Amount = 100
                }
            };
            _accountsBuilder.AccountingPolicies = new AccountingPolicies
            {
                CurrentPeriodAccountingPolicies = new AccountingPoliciesData
                {
                    IntangibleAssets = new IntangibleAssets
                    {
                        DevelopmentCosts = new AssetsAdjustment
                        {
                            AlternativeBasis = "text",
                            ReducingBalanceBasis = 1,
                            StraightLineBasis = 2
                        }
                    }
                }
            };

            var issues = new IntangibleAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == IntangibleAssetsValidations.IntangibleAssetsDevelopmentCosts);
            issue.ShouldBeNull();
        }

        [Fact]
        public void Should_trigger_ComputerSoftware_validation_when_found_account_505()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 505,
                    Amount = 100
                }
            };

            var issues = new IntangibleAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == IntangibleAssetsValidations.IntangibleAssetsComputerSoftware);
            issue.ShouldNotBeNull();
            issue.ErrorCode.ShouldBe(IntangibleAssetsValidations.ComputerSoftwareConfig.ErrorCode);
            issue.DisplayName.ShouldBe(IntangibleAssetsValidations.ComputerSoftwareConfig.DisplayName);
            issue.ErrorCategory.ShouldBe(IntangibleAssetsValidations.ComputerSoftwareConfig.ErrorCategory);
            issue.Target.ShouldBe(IntangibleAssetsValidations.ComputerSoftwareConfig.Target);
            issue.Type.ShouldBe(IntangibleAssetsValidations.ComputerSoftwareConfig.Type);
            issue.Name.ShouldBe(IntangibleAssetsValidations.ComputerSoftwareConfig.Name);
            issue.Description.ShouldBe(IntangibleAssetsValidations.ComputerSoftwareConfig.Description);
            issue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Accounting Policies > Intangible Assets > Computer Software");
        }

        [Fact]
        public void Should_not_trigger_ComputerSoftware_validation_when_found_account_505_and_ComputerSoftware_has_value()
        {
            _accountsBuilder.TrialBalance.TrialBalances = new List<PeriodTrialBalance>
            {
                new PeriodTrialBalance
                {
                    AccountCode = 505,
                    Amount = 100
                }
            };
            _accountsBuilder.AccountingPolicies = new AccountingPolicies
            {
                CurrentPeriodAccountingPolicies = new AccountingPoliciesData
                {
                    IntangibleAssets = new IntangibleAssets
                    {
                        ComputerSoftware = new AssetsAdjustment
                        {
                            AlternativeBasis = "text",
                            ReducingBalanceBasis = 1,
                            StraightLineBasis = 2
                        }
                    }
                }
            };

            var issues = new IntangibleAssetsValidationRunner(_repository.Object).Validate(_accountsBuilder);

            var issue = issues.SingleOrDefault(x => x.Name == IntangibleAssetsValidations.IntangibleAssetsComputerSoftware);
            issue.ShouldBeNull();
        }
    }
}
