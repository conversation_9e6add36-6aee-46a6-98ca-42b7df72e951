﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
    <AWSProjectType>Lambda</AWSProjectType>
	<ImplicitUsings>enable</ImplicitUsings>
	<Nullable>enable</Nullable>
    <!-- This property makes the build directory similar to a publish directory and helps the AWS .NET Lambda Mock Test Tool find project dependencies. -->
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
    <SonarQubeTestProject>false</SonarQubeTestProject>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Amazon.Lambda.Core" Version="2.5.0" />
    <PackageReference Include="Amazon.Lambda.Serialization.SystemTextJson" Version="2.4.4" />
    <PackageReference Include="Amazon.Lambda.SNSEvents" Version="2.1.0" />
    <PackageReference Include="Iris.AccountsProduction.AccountsBuilder.Messages" Version="1.0.0.278" />
    <PackageReference Include="Iris.AccountsProduction.Common.Toolkit" Version="2.0.0.259" />
    <PackageReference Include="Iris.Elements.Logging.Serilog.Lambda" Version="2.0.0.22" />
    <PackageReference Include="MediatR" Version="12.4.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AccountsProduction.AccountsBuilder.AggregatorFunc.Application\AccountsProduction.AccountsBuilder.AggregatorFunc.Application.csproj" />
  </ItemGroup>
</Project>