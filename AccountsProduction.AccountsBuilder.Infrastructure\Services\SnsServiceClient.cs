﻿using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using Amazon.SimpleNotificationService;
using Amazon.SimpleNotificationService.Model;
using Iris.AccountsProduction.Common.Toolkit.Exceptions;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Infrastructure.Services
{
    public class SnsServiceClient : ISnsServiceClient
    {
        private readonly IAmazonSimpleNotificationService _simpleNotificationService;
        private readonly ILogger<SnsServiceClient> _logger;

        public SnsServiceClient(IAmazonSimpleNotificationService simpleNotificationService, ILogger<SnsServiceClient> logger)
        {
            _simpleNotificationService = simpleNotificationService;
            _logger = logger;
        }

        public async Task PublishMessage(string topicArn, string message, string subject,
            CancellationToken cancellationToken, Dictionary<string, string>? messageAttributes)
        {
            if (string.IsNullOrEmpty(topicArn)) throw new ArgumentNullException(nameof(topicArn));
            try
            {
                var publishRequest = new PublishRequest
                {
                    TopicArn = topicArn,
                    Message = message,
                    Subject = subject
                };

                if (messageAttributes != null)
                {
                    publishRequest.MessageAttributes = new Dictionary<string, MessageAttributeValue>();

                    foreach (var attribute in messageAttributes)
                    {
                        publishRequest.MessageAttributes.Add(attribute.Key, new MessageAttributeValue
                        {
                            DataType = "String",
                            StringValue = attribute.Value
                        });
                    }
                }

                var publishResponse = await _simpleNotificationService.PublishAsync(publishRequest, cancellationToken);

                _logger.LogInformation("Published message to sns with MessageId {MessageId}", publishResponse?.MessageId);

            }
            catch (Exception e)
            {
                throw new ExternalException(e.Message);
            }
        }
    }
}
