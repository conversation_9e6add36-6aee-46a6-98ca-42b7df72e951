﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData;
using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Application.Common;
using AutoMapper;
using Microsoft.Extensions.Logging;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Application.IntegrationEvents;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents
{
    public interface IDataProcessingLayerEventFinishedEventSharedBase
    {
        public async Task StoreFinancialData(FinancialDataDto financialDataDto, Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder, ILogger _logger, IAccountsBuilderRepository _repository, IMapper _mapper, string name, Guid processId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Processing financial data from DataProcessingLayer.");

            var financialData = MapFinancialData(financialDataDto, accountsBuilder, _mapper);
            financialData.Retry = accountsBuilder.FinancialData.Retry;
            SetFinancialDataStatusDetail(financialData);

            accountsBuilder.UpdateFinancialData(financialData);

            await _repository.Save(accountsBuilder, cancellationToken);

            _logger.LogInformation("Processing financial data by status: {status}", accountsBuilder.FinancialData?.Status);
            _logger.LogInformation("FINISHED event type {eventType} for processId {processId} at eventTimestamp {eventTimestamp}.", name, processId, DateTime.UtcNow);
        }

        public async Task PublishDplRetry(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder, IDomainEventService domainEventService, IAccountsBuilderRepository _repository, IMapper _mapper, CancellationToken cancellationToken)
        {
            var newProcessId = Guid.NewGuid();
            var trialBalanceDto = _mapper.Map<TrialBalanceDto>(accountsBuilder.TrialBalance);
            accountsBuilder.SetProcessId(newProcessId);
            accountsBuilder.FinancialData.IncreaseRetry();
            await _repository.Save(accountsBuilder, cancellationToken);
            var notificationTrialBalanceChanged = new NotificationTrialBalanceChanged(accountsBuilder.ClientId, accountsBuilder.PeriodId, accountsBuilder.ProcessId, accountsBuilder.EntitySetup.ReportingStandard, trialBalanceDto);
            await domainEventService.Publish(notificationTrialBalanceChanged, cancellationToken);
        }

        public static FinancialData MapFinancialData(FinancialDataDto financialDataDto, Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder, IMapper mapper)
        {
            var financialData = mapper.Map<FinancialData>(financialDataDto);

            foreach (var reportingPeriod in accountsBuilder.TrialBalance.ReportingPeriods)
            {
                if (financialData.Financials.All(x => x.Period != reportingPeriod.EndDate))
                {
                    financialData.Financials.Add(new Financial
                    {
                        Period = reportingPeriod.EndDate
                    });
                }
            }

            return financialData;
        }

        public static void SetFinancialDataStatusDetail(FinancialData financialData)
        {
            switch (financialData?.Status)
            {
                case (int)FinancialDataStatusDto.Success:
                    if (!ValidateCalculationResult(financialData))
                    {
                        financialData.SetStatusFail();
                        financialData.UpdateErrorCode(Error.CalculationError);
                        return;
                    }
                    break;
                case (int)FinancialDataStatusDto.Error:
                    financialData.SetStatusFail();
                    financialData.UpdateErrorCode(Error.TechnicalError);
                    return;
            }

            financialData?.SetStatusSuccessful();
            financialData?.UpdateErrorCode(null);
        }

        private static bool ValidateCalculationResult(FinancialData financialData)
        {
            const string DplCalculationError = "Not Calculable/Not Applicable";
            var validCalculation = true;

            if (financialData.Financials is null) return false;

            foreach (var financial in financialData.Financials)
            {
                foreach (var category in financial.GetType().GetProperties())
                {
                    if (category.PropertyType != typeof(FinancialDataCategory)) continue;
                    var categoryName = category.Name;
                    var propertyInfo = financial.GetType().GetProperty(categoryName);
                    if (propertyInfo?.GetValue(financial) is FinancialDataCategory { Value: DplCalculationError })
                    {
                        validCalculation = false;
                    }
                }
            }

            return validCalculation;
        }

    }
}