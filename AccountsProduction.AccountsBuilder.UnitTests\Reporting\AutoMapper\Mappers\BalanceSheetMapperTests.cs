﻿using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper;
using AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.AutoMapper.Mappers
{
    public class BalanceSheetMapperTests
    {
        private readonly IMapper _mapper;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = TestHelpers.Guids.GuidTwo;
        private readonly int _defaultAmount = 0;
        private readonly decimal _calledUpShareCapitalNotPaid;
        private readonly decimal _fixedAssets;
        private readonly decimal _currentAssets;
        private readonly decimal _prepaymentsAndAccruedIncome;
        private readonly decimal _creditorsAmountsFallingDueWithinOneYear;
        private readonly decimal _netCurrentAssetsOrLiabilities;
        private readonly decimal _totalAssetsLessCurrentLiabilities;
        private readonly decimal _creditorsAmountsFallingAfterMoreThanOneYear;
        private readonly decimal _provisionsForLiabilities;
        private readonly decimal _accrualsAndDeferredIncome;
        private readonly decimal _netAssets;
        private readonly decimal _capitalAndReserves;
        private readonly decimal _capitalAccount;
        private readonly decimal _partnersCapitalAccounts;
        private readonly decimal _partnersCurrentAccounts;
        private readonly decimal _otherReserves;
        private readonly decimal _totalMembersInterests;
        private readonly decimal _membersOtherInterests;
        private readonly decimal _membersCapital;
        private readonly decimal _otherDebtsDueToMembers;
        private readonly decimal _loansAndOtherDebtsDueToMembers;
        private readonly decimal _nonControllingInterests;
        private readonly decimal _herdBasis;
        private const string Message = "This field name is stored in DB as it is, do not modify!";

        public BalanceSheetMapperTests()
        {
            var randomTool = new Random();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _calledUpShareCapitalNotPaid = Convert.ToDecimal(randomTool.Next(1, 10000));
            _fixedAssets = Convert.ToDecimal(randomTool.Next(1, 10000));
            _currentAssets = Convert.ToDecimal(randomTool.Next(1, 10000));
            _prepaymentsAndAccruedIncome = Convert.ToDecimal(randomTool.Next(1, 10000));
            _creditorsAmountsFallingDueWithinOneYear = Convert.ToDecimal(randomTool.Next(1, 10000));
            _netCurrentAssetsOrLiabilities = Convert.ToDecimal(randomTool.Next(1, 10000));
            _totalAssetsLessCurrentLiabilities = Convert.ToDecimal(randomTool.Next(1, 10000));
            _creditorsAmountsFallingAfterMoreThanOneYear = Convert.ToDecimal(randomTool.Next(1, 10000));
            _provisionsForLiabilities = Convert.ToDecimal(randomTool.Next(1, 10000));
            _accrualsAndDeferredIncome = Convert.ToDecimal(randomTool.Next(1, 10000));
            _netAssets = Convert.ToDecimal(randomTool.Next(1, 10000));
            _capitalAndReserves = Convert.ToDecimal(randomTool.Next(1, 10000));
            _capitalAccount = Convert.ToDecimal(randomTool.Next(1, 10000));
            _partnersCapitalAccounts = Convert.ToDecimal(randomTool.Next(1, 10000));
            _partnersCurrentAccounts = Convert.ToDecimal(randomTool.Next(1, 10000));
            _otherReserves = Convert.ToDecimal(randomTool.Next(1, 10000));
            _totalMembersInterests = Convert.ToDecimal(randomTool.Next(1, 10000));
            _membersOtherInterests = Convert.ToDecimal(randomTool.Next(1, 10000));
            _membersCapital = Convert.ToDecimal(randomTool.Next(1, 10000));
            _otherDebtsDueToMembers = Convert.ToDecimal(randomTool.Next(1, 10000));
            _loansAndOtherDebtsDueToMembers = Convert.ToDecimal(randomTool.Next(1, 10000));
            _nonControllingInterests = Convert.ToDecimal(randomTool.Next(1, 10000));
        }

        [Fact]
        public void Should_contain_correct_properties()
        {
            var balanceSheet = new BalanceSheetMessage();

            var periodIdProperty = balanceSheet.GetType().GetProperties().SingleOrDefault(s => s.Name == "PeriodId");
            var balanceSheetDtoProperties = balanceSheet
                .GetType().GetProperties()
                .Where(s => s.PropertyType == typeof(FinancialDataCategoryMessage))
                .Select(x => x).ToList();

            periodIdProperty.ShouldNotBeNull("This field is mandatory, do not modify!");
            balanceSheetDtoProperties.Count.ShouldBe(42);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CalledUpShareCapitalNotPaid", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "FixedAssets", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CurrentAssets", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "PrepaymentsAndAccruedIncome", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CreditorsAmountsFallingDueWithinOneYear", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "NetCurrentAssetsOrLiabilities", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "TotalAssetsLessCurrentLiabilities", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CreditorsAmountsFallingAfterMoreThanOneYear", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "ProvisionsForLiabilities", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "AccrualsAndDeferredIncome", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "NetAssets", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CapitalAndReserves", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "IntangibleAssets", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "TangibleFixedAssets", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "FixedAssetInvestments", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "InvestmentProperty", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "Stock", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CurrentAssetInvestments", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "Debtors", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CashAtBankAndInHand", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CapitalAccount", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "PartnersCapitalAccounts", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "PartnersCurrentAccounts", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "OtherReserves", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "PensionSchemeAssetsLiabilities", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "HealthcareObligatons", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CalledUpShareCapital", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "SharePremiumReserve", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "RevaluationReserve", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CapitalRedemptionReserve", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "OtherReserves1", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "OtherReserves2", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "FairValueReserve", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "ProfitAndLossReserve", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "Goodwill", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "TotalMembersInterests", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "MembersOtherInterests", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "MembersCapital", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "OtherDebtsDueToMembers", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "LoansAndOtherDebtsDueToMembers", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "NonControllingInterests", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "HerdBasis", Message);
        }

        [Fact]
        public void Should_map_profit_and_loss_for_general_balance_sheet()
        {
            var balanceSheet = new BalanceSheetGeneral
            {
                ClientId = _clientId,
                AccountPeriodId = _periodId
            };
            var balanceSheetDto = new BalanceSheetMessage
            {
                PeriodId = Guid.NewGuid(),
                CalledUpShareCapitalNotPaid = new FinancialDataCategoryMessage { Value = _calledUpShareCapitalNotPaid.ToString() },
                FixedAssets = new FinancialDataCategoryMessage { Value = _fixedAssets.ToString() },
                CurrentAssets = new FinancialDataCategoryMessage { Value = _currentAssets.ToString() },
                PrepaymentsAndAccruedIncome = new FinancialDataCategoryMessage { Value = _prepaymentsAndAccruedIncome.ToString() },
                CreditorsAmountsFallingDueWithinOneYear = new FinancialDataCategoryMessage { Value = _creditorsAmountsFallingDueWithinOneYear.ToString() },
                NetCurrentAssetsOrLiabilities = new FinancialDataCategoryMessage { Value = _netCurrentAssetsOrLiabilities.ToString() },
                TotalAssetsLessCurrentLiabilities = new FinancialDataCategoryMessage { Value = _totalAssetsLessCurrentLiabilities.ToString() },
                CreditorsAmountsFallingAfterMoreThanOneYear = new FinancialDataCategoryMessage { Value = _creditorsAmountsFallingAfterMoreThanOneYear.ToString() },
                ProvisionsForLiabilities = new FinancialDataCategoryMessage { Value = _provisionsForLiabilities.ToString() },
                AccrualsAndDeferredIncome = new FinancialDataCategoryMessage { Value = _accrualsAndDeferredIncome.ToString() },
                NetAssets = new FinancialDataCategoryMessage { Value = _netAssets.ToString() },
                CapitalAndReserves = new FinancialDataCategoryMessage { Value = _capitalAndReserves.ToString() },
                NonControllingInterests = new FinancialDataCategoryMessage { Value = _nonControllingInterests.ToString() }
            };

            _mapper.Map(balanceSheetDto, balanceSheet);

            balanceSheet.ClientId.ShouldBe(_clientId);
            balanceSheet.AccountPeriodId.ShouldBe(_periodId);
            balanceSheet.CalledUpShareCapitalNotPaid.ShouldBe(_calledUpShareCapitalNotPaid);
            balanceSheet.PrepaymentsAndAccruedIncome.ShouldBe(_prepaymentsAndAccruedIncome);
            balanceSheet.CreditorsAmountsFallingDueWithinOneYear.ShouldBe(_creditorsAmountsFallingDueWithinOneYear);
            balanceSheet.CreditorsAmountsFallingAfterMoreThanOneYear.ShouldBe(_creditorsAmountsFallingAfterMoreThanOneYear);
            balanceSheet.ProvisionsForLiabilities.ShouldBe(_provisionsForLiabilities);
            balanceSheet.AccrualsAndDeferredIncome.ShouldBe(_accrualsAndDeferredIncome);
            balanceSheet.NonControllingInterests.ShouldBe(_nonControllingInterests);
        }

        [Fact]
        public void Should_map_profit_and_loss_for_general_balance_sheet_when_all_lineItem_values_are_null()
        {
            var balanceSheet = new BalanceSheetGeneral
            {
                ClientId = _clientId,
                AccountPeriodId = _periodId
            };
            var balanceSheetDto = new BalanceSheetMessage
            {
                PeriodId = Guid.NewGuid()
            };

            _mapper.Map(balanceSheetDto, balanceSheet);

            balanceSheet.ClientId.ShouldBe(_clientId);
            balanceSheet.AccountPeriodId.ShouldBe(_periodId);
            balanceSheet.CalledUpShareCapitalNotPaid.ShouldBe(_defaultAmount);
            balanceSheet.PrepaymentsAndAccruedIncome.ShouldBe(_defaultAmount);
            balanceSheet.CreditorsAmountsFallingDueWithinOneYear.ShouldBe(_defaultAmount);
            balanceSheet.CreditorsAmountsFallingAfterMoreThanOneYear.ShouldBe(_defaultAmount);
            balanceSheet.ProvisionsForLiabilities.ShouldBe(_defaultAmount);
            balanceSheet.AccrualsAndDeferredIncome.ShouldBe(_defaultAmount);
        }

        [Fact]
        public void Should_contain_correct_properties_for_general_balance_sheet()
        {
            var balanceSheet = new BalanceSheetGeneral();

            var periodIdProperty = balanceSheet.GetType().GetProperties().SingleOrDefault(s => s.Name == "AccountPeriodId");
            var balanceSheetDtoProperties = balanceSheet
                .GetType().GetProperties()
                .Where(s => s.PropertyType == typeof(decimal))
                .Select(x => x).ToList();

            periodIdProperty.ShouldNotBeNull("This field is mandatory, do not modify!");
            balanceSheetDtoProperties.Count.ShouldBe(27);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CalledUpShareCapitalNotPaid", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "Goodwill", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "IntangibleAssets", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "TangibleFixedAssets", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "FixedAssetInvestments", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CurrentAssetInvestments", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "InvestmentProperty", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "Stock", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "Debtors", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CashAtBankAndInHand", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "PrepaymentsAndAccruedIncome", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CreditorsAmountsFallingDueWithinOneYear", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CreditorsAmountsFallingAfterMoreThanOneYear", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "ProvisionsForLiabilities", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "PensionSchemeAssetsLiabilities", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "HealthcareObligatons", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "AccrualsAndDeferredIncome", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CalledUpShareCapital", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "SharePremiumReserve", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "RevaluationReserve", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "CapitalRedemptionReserve", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "OtherReserves1", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "OtherReserves2", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "FairValueReserve", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "ProfitAndLossReserve", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "NonControllingInterests", Message);
            balanceSheetDtoProperties.ShouldContain(x => x.Name == "HerdBasis", Message);
        }

        [Fact]
        public void Should_map_profit_and_loss_for_balance_sheet_ch()
        {
            var balanceSheet = new BalanceSheetCH
            {
                ClientId = _clientId,
                AccountPeriodId = _periodId
            };

            var balanceSheetDto = new BalanceSheetMessage
            {
                PeriodId = Guid.NewGuid(),
                CalledUpShareCapitalNotPaid = new FinancialDataCategoryMessage { Value = _calledUpShareCapitalNotPaid.ToString() },
                FixedAssets = new FinancialDataCategoryMessage { Value = _fixedAssets.ToString() },
                CurrentAssets = new FinancialDataCategoryMessage { Value = _currentAssets.ToString() },
                PrepaymentsAndAccruedIncome = new FinancialDataCategoryMessage { Value = _prepaymentsAndAccruedIncome.ToString() },
                CreditorsAmountsFallingDueWithinOneYear = new FinancialDataCategoryMessage { Value = _creditorsAmountsFallingDueWithinOneYear.ToString() },
                NetCurrentAssetsOrLiabilities = new FinancialDataCategoryMessage { Value = _netCurrentAssetsOrLiabilities.ToString() },
                TotalAssetsLessCurrentLiabilities = new FinancialDataCategoryMessage { Value = _totalAssetsLessCurrentLiabilities.ToString() },
                CreditorsAmountsFallingAfterMoreThanOneYear = new FinancialDataCategoryMessage { Value = _creditorsAmountsFallingAfterMoreThanOneYear.ToString() },
                ProvisionsForLiabilities = new FinancialDataCategoryMessage { Value = _provisionsForLiabilities.ToString() },
                AccrualsAndDeferredIncome = new FinancialDataCategoryMessage { Value = _accrualsAndDeferredIncome.ToString() },
                NetAssets = new FinancialDataCategoryMessage { Value = _netAssets.ToString() },
                CapitalAndReserves = new FinancialDataCategoryMessage { Value = _capitalAndReserves.ToString() }
            };

            _mapper.Map(balanceSheetDto, balanceSheet);

            balanceSheet.ClientId.ShouldBe(_clientId);
            balanceSheet.AccountPeriodId.ShouldBe(_periodId);
        }

        [Fact]
        public void Should_map_profit_and_loss_for_balance_sheet_frs105()
        {
            var balanceSheet = new BalanceSheetFRS105
            {
                ClientId = _clientId,
                AccountPeriodId = _periodId
            };
            var balanceSheetDto = new BalanceSheetMessage
            {
                PeriodId = Guid.NewGuid(),
                CalledUpShareCapitalNotPaid = new FinancialDataCategoryMessage { Value = _calledUpShareCapitalNotPaid.ToString() },
                FixedAssets = new FinancialDataCategoryMessage { Value = _fixedAssets.ToString() },
                CurrentAssets = new FinancialDataCategoryMessage { Value = _currentAssets.ToString() },
                PrepaymentsAndAccruedIncome = new FinancialDataCategoryMessage { Value = _prepaymentsAndAccruedIncome.ToString() },
                CreditorsAmountsFallingDueWithinOneYear = new FinancialDataCategoryMessage { Value = _creditorsAmountsFallingDueWithinOneYear.ToString() },
                NetCurrentAssetsOrLiabilities = new FinancialDataCategoryMessage { Value = _netCurrentAssetsOrLiabilities.ToString() },
                TotalAssetsLessCurrentLiabilities = new FinancialDataCategoryMessage { Value = _totalAssetsLessCurrentLiabilities.ToString() },
                CreditorsAmountsFallingAfterMoreThanOneYear = new FinancialDataCategoryMessage { Value = _creditorsAmountsFallingAfterMoreThanOneYear.ToString() },
                ProvisionsForLiabilities = new FinancialDataCategoryMessage { Value = _provisionsForLiabilities.ToString() },
                AccrualsAndDeferredIncome = new FinancialDataCategoryMessage { Value = _accrualsAndDeferredIncome.ToString() },
                NetAssets = new FinancialDataCategoryMessage { Value = _netAssets.ToString() },
                CapitalAndReserves = new FinancialDataCategoryMessage { Value = _capitalAndReserves.ToString() }
            };

            _mapper.Map(balanceSheetDto, balanceSheet);

            balanceSheet.ClientId.ShouldBe(_clientId);
            balanceSheet.AccountPeriodId.ShouldBe(_periodId);
            balanceSheet.CalledUpShareCapitalNotPaid.ShouldBe(_calledUpShareCapitalNotPaid);
            balanceSheet.FixedAssets.ShouldBe(_fixedAssets);
            balanceSheet.CurrentAssets.ShouldBe(_currentAssets);
            balanceSheet.PrepaymentsAndAccruedIncome.ShouldBe(_prepaymentsAndAccruedIncome);
            balanceSheet.CreditorsAmountsFallingDueWithinOneYear.ShouldBe(_creditorsAmountsFallingDueWithinOneYear);
            balanceSheet.NetCurrentAssetsOrLiabilities.ShouldBe(_netCurrentAssetsOrLiabilities);
            balanceSheet.TotalAssetsLessCurrentLiabilities.ShouldBe(_totalAssetsLessCurrentLiabilities);
            balanceSheet.CreditorsAmountsFallingAfterMoreThanOneYear.ShouldBe(_creditorsAmountsFallingAfterMoreThanOneYear);
            balanceSheet.ProvisionsForLiabilities.ShouldBe(_provisionsForLiabilities);
            balanceSheet.AccrualsAndDeferredIncome.ShouldBe(_accrualsAndDeferredIncome);
            balanceSheet.NetAssets.ShouldBe(_netAssets);
            balanceSheet.CapitalAndReserves.ShouldBe(_capitalAndReserves);
        }

        [Fact]
        public void Should_map_profit_and_loss_for_balance_sheet_ifrs()
        {
            var balanceSheet = new BalanceSheetIFRS
            {
                ClientId = _clientId,
                AccountPeriodId = _periodId
            };
            var balanceSheetDto = new BalanceSheetMessage
            {
                PeriodId = Guid.NewGuid(),
                CalledUpShareCapitalNotPaid = new FinancialDataCategoryMessage { Value = _calledUpShareCapitalNotPaid.ToString() },
                FixedAssets = new FinancialDataCategoryMessage { Value = _fixedAssets.ToString() },
                CurrentAssets = new FinancialDataCategoryMessage { Value = _currentAssets.ToString() },
                PrepaymentsAndAccruedIncome = new FinancialDataCategoryMessage { Value = _prepaymentsAndAccruedIncome.ToString() },
                CreditorsAmountsFallingDueWithinOneYear = new FinancialDataCategoryMessage { Value = _creditorsAmountsFallingDueWithinOneYear.ToString() },
                NetCurrentAssetsOrLiabilities = new FinancialDataCategoryMessage { Value = _netCurrentAssetsOrLiabilities.ToString() },
                TotalAssetsLessCurrentLiabilities = new FinancialDataCategoryMessage { Value = _totalAssetsLessCurrentLiabilities.ToString() },
                CreditorsAmountsFallingAfterMoreThanOneYear = new FinancialDataCategoryMessage { Value = _creditorsAmountsFallingAfterMoreThanOneYear.ToString() },
                ProvisionsForLiabilities = new FinancialDataCategoryMessage { Value = _provisionsForLiabilities.ToString() },
                AccrualsAndDeferredIncome = new FinancialDataCategoryMessage { Value = _accrualsAndDeferredIncome.ToString() },
                NetAssets = new FinancialDataCategoryMessage { Value = _netAssets.ToString() },
                CapitalAndReserves = new FinancialDataCategoryMessage { Value = _capitalAndReserves.ToString() }
            };

            _mapper.Map(balanceSheetDto, balanceSheet);

            balanceSheet.ClientId.ShouldBe(_clientId);
            balanceSheet.AccountPeriodId.ShouldBe(_periodId);
        }


        [Fact]
        public void Should_map_profit_and_loss_for_balance_sheet_llp()
        {
            var balanceSheet = new BalanceSheetLLP
            {
                ClientId = _clientId,
                AccountPeriodId = _periodId
            };
            var balanceSheetDto = new BalanceSheetMessage
            {
                PeriodId = Guid.NewGuid(),
                CalledUpShareCapitalNotPaid = new FinancialDataCategoryMessage { Value = _calledUpShareCapitalNotPaid.ToString() },
                FixedAssets = new FinancialDataCategoryMessage { Value = _fixedAssets.ToString() },
                CurrentAssets = new FinancialDataCategoryMessage { Value = _currentAssets.ToString() },
                PrepaymentsAndAccruedIncome = new FinancialDataCategoryMessage { Value = _prepaymentsAndAccruedIncome.ToString() },
                CreditorsAmountsFallingDueWithinOneYear = new FinancialDataCategoryMessage { Value = _creditorsAmountsFallingDueWithinOneYear.ToString() },
                NetCurrentAssetsOrLiabilities = new FinancialDataCategoryMessage { Value = _netCurrentAssetsOrLiabilities.ToString() },
                TotalAssetsLessCurrentLiabilities = new FinancialDataCategoryMessage { Value = _totalAssetsLessCurrentLiabilities.ToString() },
                CreditorsAmountsFallingAfterMoreThanOneYear = new FinancialDataCategoryMessage { Value = _creditorsAmountsFallingAfterMoreThanOneYear.ToString() },
                ProvisionsForLiabilities = new FinancialDataCategoryMessage { Value = _provisionsForLiabilities.ToString() },
                AccrualsAndDeferredIncome = new FinancialDataCategoryMessage { Value = _accrualsAndDeferredIncome.ToString() },
                NetAssets = new FinancialDataCategoryMessage { Value = _netAssets.ToString() },
                CapitalAndReserves = new FinancialDataCategoryMessage { Value = _capitalAndReserves.ToString() }
            };

            _mapper.Map(balanceSheetDto, balanceSheet);

            balanceSheet.ClientId.ShouldBe(_clientId);
            balanceSheet.AccountPeriodId.ShouldBe(_periodId);
        }


        [Fact]
        public void Should_map_profit_and_loss_for_balance_sheet_noncorp()
        {
            var balanceSheet = new BalanceSheetNonCorp
            {
                ClientId = _clientId,
                AccountPeriodId = _periodId
            };
            var balanceSheetDto = new BalanceSheetMessage
            {
                PeriodId = Guid.NewGuid(),
                CalledUpShareCapitalNotPaid = new FinancialDataCategoryMessage { Value = _calledUpShareCapitalNotPaid.ToString() },
                FixedAssets = new FinancialDataCategoryMessage { Value = _fixedAssets.ToString() },
                CurrentAssets = new FinancialDataCategoryMessage { Value = _currentAssets.ToString() },
                PrepaymentsAndAccruedIncome = new FinancialDataCategoryMessage { Value = _prepaymentsAndAccruedIncome.ToString() },
                CreditorsAmountsFallingDueWithinOneYear = new FinancialDataCategoryMessage { Value = _creditorsAmountsFallingDueWithinOneYear.ToString() },
                NetCurrentAssetsOrLiabilities = new FinancialDataCategoryMessage { Value = _netCurrentAssetsOrLiabilities.ToString() },
                TotalAssetsLessCurrentLiabilities = new FinancialDataCategoryMessage { Value = _totalAssetsLessCurrentLiabilities.ToString() },
                CreditorsAmountsFallingAfterMoreThanOneYear = new FinancialDataCategoryMessage { Value = _creditorsAmountsFallingAfterMoreThanOneYear.ToString() },
                ProvisionsForLiabilities = new FinancialDataCategoryMessage { Value = _provisionsForLiabilities.ToString() },
                AccrualsAndDeferredIncome = new FinancialDataCategoryMessage { Value = _accrualsAndDeferredIncome.ToString() },
                NetAssets = new FinancialDataCategoryMessage { Value = _netAssets.ToString() }
            };

            _mapper.Map(balanceSheetDto, balanceSheet);

            balanceSheet.ClientId.ShouldBe(_clientId);
            balanceSheet.AccountPeriodId.ShouldBe(_periodId);
            balanceSheet.CapitalAccount.ShouldBe(_defaultAmount);
            balanceSheet.PartnersCapitalAccounts.ShouldBe(_defaultAmount);
            balanceSheet.PartnersCurrentAccounts.ShouldBe(_defaultAmount);
            balanceSheet.OtherReserves.ShouldBe(_defaultAmount);
        }
    }
}
