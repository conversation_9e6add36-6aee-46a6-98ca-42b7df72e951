﻿using AccountsProduction.AccountsBuilder.Application.Commands.GenerateReport;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder;
using AccountsProduction.AccountsBuilder.Application.Queries.AccountsBuilder;
using Iris.AccountsProduction.Common.Toolkit.Filters;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using static Iris.AccountsProduction.Common.Toolkit.Utils.Constants;

namespace AccountsProduction.AccountsBuilder.Api.Controllers
{
    [Route("clients/{clientId}/[controller]")]
    [ApiController]
    [Feature(FeatureToggle.AccountsProduction.Services)]
    [APLicensed]
	[ConfidentialClientAccess (ClientUuidParamName = "clientId")]
	public class ReportsController : ControllerBase
    {
        private readonly IMediator _mediator;

        public ReportsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// Get necessary report information for a client and a period
        /// </summary>
        /// <param name="clientId">Client uuid</param>
        /// <param name="periodId">Period uuid</param>
        /// <returns></returns>
        [HttpGet("{periodId}")]
        [ProducesResponseType(typeof(AccountsBuilderDto), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetByPeriodId([FromRoute] Guid clientId, [FromRoute] Guid periodId)
        {
            var response = await _mediator.Send(new GetReportQuery
            {
                ClientId = clientId,
                PeriodId = periodId
            });

            if (response == null)
            {
                return NotFound();
            }

            return Ok(response);
        }

        /// <summary>
        /// Create accounts builder resource for client and period
        /// </summary>
        /// <param name="clientId">Client uuid</param>
        /// <param name="command">Payload data</param>
        /// <returns>
        ///   <response code="201">Created with success</response>
        ///   <response code="400">Returns if the provided payload is not valid</response>
        ///   <response code="500">Returns if server error happened</response>
        /// </returns>
        [HttpPost]
        public async Task<IActionResult> Generate([FromRoute] Guid clientId, [FromBody] GenerateReportCommand command)
        {
            command.ClientId = clientId;
            await _mediator.Send(command);

            return Ok();
        }
    }
}
