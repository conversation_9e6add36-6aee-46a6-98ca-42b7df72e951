using Iris.Platform.Eventbus.Client.Dotnet.Client;
using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.EventBusClient;
using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.Message;
using Iris.Platform.Eventbus.Client.Dotnet.Services;
using Microsoft.Extensions.Logging;
using System.Net;

namespace AccountsProduction.AccountsBuilder.Infrastructure.Mocks
{
    /// <summary>
    /// Mock EventBus client for local development and testing
    /// </summary>
    public class MockEventBusClient : EventBusClient
    {
        private readonly ILogger<MockEventBusClient> _logger;

        public MockEventBusClient(
            ILogger<MockEventBusClient> logger,
            IRootAssembly rootAssembly) : base(null, null, rootAssembly)
        {
            _logger = logger;
        }

        public override Task<EventBusResponse<TResponse>> PublishWithResponse<TRequest, TResponse>(
            EventBusMessage<TRequest> message,
            int timeoutInMilliseconds = 30000)
        {
            _logger.LogInformation("MockEventBusClient: PublishWithResponse called for topic {Topic}", message.Topic);

            // Mock successful response for practice details
            if (typeof(TResponse).Name.Contains("PracticeDetail"))
            {
                var mockPracticeDetails = new List<object>(); // Empty practice details for testing
                var response = new EventBusResponse<TResponse>
                {
                    Status = HttpStatusCode.OK,
                    Error = null,
                    Payload = (TResponse)(object)mockPracticeDetails
                };
                return Task.FromResult(response);
            }

            // Default successful response
            var defaultResponse = new EventBusResponse<TResponse>
            {
                Status = HttpStatusCode.OK,
                Error = null,
                Payload = default(TResponse)
            };

            return Task.FromResult(defaultResponse);
        }

        public override Task<EventBusResponse<string>> Publish<TRequest>(
            EventBusMessage<TRequest> message,
            int timeoutInMilliseconds = 30000)
        {
            _logger.LogInformation("MockEventBusClient: Publish called for topic {Topic}", message.Topic);

            var response = new EventBusResponse<string>
            {
                Status = HttpStatusCode.OK,
                Error = null,
                Payload = "Mock publish successful"
            };

            return Task.FromResult(response);
        }

        public override Task<EventBusResponse<string>> PublishFireAndForget<TRequest>(EventBusMessage<TRequest> message)
        {
            _logger.LogInformation("MockEventBusClient: PublishFireAndForget called for topic {Topic}", message.Topic);

            var response = new EventBusResponse<string>
            {
                Status = HttpStatusCode.OK,
                Error = null,
                Payload = "Mock fire and forget successful"
            };

            return Task.FromResult(response);
        }

        protected override void Dispose(bool disposing)
        {
            // Mock disposal - nothing to dispose
            _logger.LogInformation("MockEventBusClient: Disposed");
        }
    }
}
