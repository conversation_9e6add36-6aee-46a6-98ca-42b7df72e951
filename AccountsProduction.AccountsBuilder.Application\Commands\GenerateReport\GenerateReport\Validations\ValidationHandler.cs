﻿using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations
{
    public class ValidationHandler : IValidationHandler
    {
        private readonly IValidationRunnerBuilder _validationRunnerBuilder;
        private readonly IGroupAccountSubAccountIntervalRepository _groupAccountSubAccountIntervalRepository;

        public ValidationHandler(IValidationRunnerBuilder validationRunnerBuilder, IGroupAccountSubAccountIntervalRepository groupAccountSubAccountIntervalRepository)
        {
            _validationRunnerBuilder = validationRunnerBuilder;
            _groupAccountSubAccountIntervalRepository = groupAccountSubAccountIntervalRepository;
        }

        public async Task<ValidationData> Validate(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder, string reportType)
        {
            await _groupAccountSubAccountIntervalRepository.LoadGroupAccountSubAccountIntervalListInCacheAsync(accountsBuilder.TrialBalance.AccountsChartIdentifier, accountsBuilder.TrialBalance.GroupStructureCode);

            var validationData = new ValidationData();
            var validationRunners = _validationRunnerBuilder.GetValidationRunners(accountsBuilder, reportType);

            foreach (var validationRunner in validationRunners)
            {
                validationData.ValidationIssues.AddRange(validationRunner.Validate(accountsBuilder));
            }

            return validationData;
        }
    }
}