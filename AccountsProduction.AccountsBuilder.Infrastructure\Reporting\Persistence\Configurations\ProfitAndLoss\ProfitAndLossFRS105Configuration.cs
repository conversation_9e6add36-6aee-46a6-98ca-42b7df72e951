﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.ProfitAndLoss;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.ProfitAndLoss
{
    public class ProfitAndLossFRS105Configuration : IEntityTypeConfiguration<ProfitAndLossFRS105>
    {
        public void Configure(EntityTypeBuilder<ProfitAndLossFRS105> builder)
        {
            builder.ToTable("ProfitAndLossFRS105", "public");

            builder.HasKey(e => new { e.ClientId, e.AccountPeriodId })
                .HasName("profitandlossfrs105_pk");

            builder.Property(e => e.Turnover).HasColumnType("numeric");

            builder.Property(e => e.OtherIncome).HasColumnType("numeric");

            builder.Property(e => e.CostOfRawMaterialsAndConsumables).HasColumnType("numeric");

            builder.Property(e => e.StaffCosts).HasColumnType("numeric");

            builder.Property(e => e.StaffCosts).HasColumnType("numeric");

            builder.Property(e => e.DepreciationAndOtherAmountsWrittenOffAssets).HasColumnType("numeric");

            builder.Property(e => e.OtherCharges).HasColumnType("numeric");

            builder.Property(e => e.Tax).HasColumnType("numeric");

            builder.Property(e => e.ProfitLossAvailableForDiscretionaryDivision).HasColumnType("numeric");

            builder.Property(e => e.MembersRemunerationAsExpense).HasColumnType("numeric");

            builder.HasOne(d => d.ReportingPeriod)
                .WithOne(p => p!.ProfitAndLossFRS105!)
                .HasForeignKey<ProfitAndLossFRS105>(d => new { d.ClientId, d.AccountPeriodId })
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("profitandlossfrs105_fk");
        }
    }
}
