﻿using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Dpl;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Application.IntegrationEvents;
using AccountsProduction.AccountsBuilder.Infrastructure.IntegrationEventHandler;
using AutoMapper;
using Iris.Platform.Eventbus.Client.Dotnet.Client;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Infrastructure.IntegrationEvents
{
    public class TrialBalanceChangeNotificationEventHandlerTests
    {
        private readonly Mock<ILogger<TrialBalanceChangeNotificationEventHandler>> _logger;
        private readonly Mock<ISnsServiceClient> _snsServiceClient;
        private readonly Mock<IEventBusClient> _eventBusClient;
        private readonly Mock<UserContext> _userContext;
        private readonly Mock<IEnvVariableProvider> _envVariableProvider;
        private readonly IMapper _mapper;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = TestHelpers.Guids.GuidTwo;
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;
        private readonly Mock<IAccountPeriodService> _accountPeriodService;

        public TrialBalanceChangeNotificationEventHandlerTests()
        {
            var mapperConfig = new MapperConfiguration(cfg => { cfg.AddProfile<TrialBalanceMapper>(); });
            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _logger = new Mock<ILogger<TrialBalanceChangeNotificationEventHandler>>();
            _snsServiceClient = new Mock<ISnsServiceClient>();
            _eventBusClient = new Mock<IEventBusClient>();
            _userContext = new Mock<UserContext>();
            _envVariableProvider = new Mock<IEnvVariableProvider>();
            _userContext.Setup(q => q.TenantId).Returns(_tenantId.ToString());

            _accountPeriodService = new Mock<IAccountPeriodService>();

            _accountPeriodService.Setup(x => x.GetRoundingOptionsAsync(It.Is<Guid>(g => g == _clientId), It.Is<Guid>(p => p == _periodId)))
                .ReturnsAsync(new AccountsBuilder.Application.Common.Dto.RoundingOptions.RoundingOptionsResponse()
                {
                    AccountPeriodId = _periodId,
                    ProfitLossRoundingAccount = 280,
                    ProfitLossRoundingAccountDescription = "Sundry expenses",
                    BalanceSheetRoundingAccount = 737,
                    BalanceSheetRoundingAccountDescription = "Trade creditors",
                    UseAdvancedRounding = true
                });
        }

        [Fact]
        public async Task Should_publish_dpl_event_via_sns()
        {
            _envVariableProvider.SetupGet(x => x.SendDplInboundViaEventbus).Returns(false);

            var notification = new NotificationTrialBalanceChanged(_clientId, _periodId, _processId, ReportStandardType.FRS102_1A, new AccountsBuilder.Application.Common.Dto.TrialBalance.TrialBalanceDto());
            var eventHandler = new TrialBalanceChangeNotificationEventHandler(_logger.Object, _mapper, _userContext.Object, _snsServiceClient.Object, _eventBusClient.Object, _envVariableProvider.Object, _accountPeriodService.Object);
            await eventHandler.Handle(notification, CancellationToken.None);

            _snsServiceClient.Verify(s =>
            s.PublishMessage(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                CancellationToken.None,
                It.Is<Dictionary<string, string>>(
                    dict => dict.ContainsKey("ClientId") && dict.ContainsKey("PeriodId") && dict.ContainsKey("ProcessId"))),
                    Times.Once);
        }

        [Fact]
        public async Task Should_publish_dpl_event_via_eventbus()
        {
            _envVariableProvider.SetupGet(x => x.SendDplInboundViaEventbus).Returns(true);

            var notification = new NotificationTrialBalanceChanged(_clientId, _periodId, _processId, ReportStandardType.FRS102_1A, new AccountsBuilder.Application.Common.Dto.TrialBalance.TrialBalanceDto());
            var eventHandler = new TrialBalanceChangeNotificationEventHandler(_logger.Object, _mapper, _userContext.Object, _snsServiceClient.Object, _eventBusClient.Object, _envVariableProvider.Object, _accountPeriodService.Object);
            await eventHandler.Handle(notification, CancellationToken.None);

            _eventBusClient.Verify(eb => eb.Publish(It.IsAny<string>(), It.IsAny<DplEventbusMessage>()), Times.Once);
        }
    }
}
