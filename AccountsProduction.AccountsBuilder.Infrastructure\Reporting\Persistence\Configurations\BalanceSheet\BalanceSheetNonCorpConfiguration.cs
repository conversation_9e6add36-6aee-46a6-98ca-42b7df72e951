﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.BalanceSheet
{
    public class BalanceSheetNonCorpConfiguration : IEntityTypeConfiguration<BalanceSheetNonCorp>
    {
        public void Configure(EntityTypeBuilder<BalanceSheetNonCorp> builder)
        {
            builder.ToTable("BalanceSheetNonCorp", "public");

            builder.HasKey(e => new { e.ClientId, e.AccountPeriodId })
                .HasName("balancesheetnoncorp_pk");

            builder.Property(e => e.CapitalAccount).HasColumnType("numeric");

            builder.Property(e => e.PartnersCapitalAccounts).HasColumnType("numeric");

            builder.Property(e => e.PartnersCurrentAccounts).HasColumnType("numeric");

            builder.Property(e => e.OtherReserves).HasColumnType("numeric");

            builder.HasOne(d => d.BalanceSheetGeneral)
                   .WithOne(p => p.BalanceSheetNonCorp)
                   .HasForeignKey<BalanceSheetNonCorp>(d => new { d.ClientId, d.AccountPeriodId })
                   .OnDelete(DeleteBehavior.Cascade)
                   .HasConstraintName("balancesheetnoncorp_fk");
        }
    }
}
