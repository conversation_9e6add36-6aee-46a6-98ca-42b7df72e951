﻿using Amazon.RDS.Util;
using Microsoft.Extensions.Configuration;
using Npgsql;
using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.Infrastructure.Persistence
{
    /// <summary>
    /// Database connection string factory
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class DbConnectionStringFactory
    {
        /// <summary>
        /// Generates a database connection string
        /// </summary>
        /// <param name="configuration">Service configuration</param>
        /// <returns>The connection string</returns>
        public static string ConnectionString(IConfiguration configuration)
        {
            if(configuration[ConfigurationKeys.DbConnection] != null)
                return configuration[ConfigurationKeys.DbConnection];

            var host = configuration[ConfigurationKeys.DbHost];
            var port = int.Parse(configuration[ConfigurationKeys.DbPort]);
            var username = configuration[ConfigurationKeys.DbUsername];
            var database = configuration[ConfigurationKeys.DbName];
            return $"Server={host};Port={port};User Id={username};Database={database};SSL Mode=Require;Trust Server Certificate=true;Pooling=true;";

        }

        /// <summary>
        /// Generate a password provider callback function that generates an RDS IAM authentication token
        /// </summary>
        /// <returns>A password provider callback function</returns>
        public static ProvidePasswordCallback PasswordCallback() => (host, port, database, username) =>
            RDSAuthTokenGenerator.GenerateAuthToken(host, port, username);
    }

    public static class ConfigurationKeys
    {
        public static string DbConnection => "DB_CONNECTION";
        public static string DbHost => "ACCOUNTS_PRODUCTION_DB_HOST";
        public static string DbPort => "ACCOUNTS_PRODUCTION_DB_PORT";
        public static string DbUsername => "ACCOUNTS_PRODUCTION_DB_USERNAME";
        public static string DbName => "ACCOUNTS_PRODUCTION_DB_DATABASE_NAME";
        public static string DbPassword => "ACCOUNTS_PRODUCTION_DB_PASSWORD";

    }
}