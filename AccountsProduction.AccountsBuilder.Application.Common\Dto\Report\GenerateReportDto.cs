﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Address;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.PracticeDetails;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.Report
{
    public class GenerateReportDto
    {
        public EntitySetupDto? EntitySetupDto { get; set; }
        public PracticeDetailMessage? PracticeDetailsDto { get; set; }
        public List<InvolvementDto> InvolvementDtos { get; set; } = null!;
        public ClientResponse ClientResponse { get; set; } = null!;
        public ClientAddressDto ClientAddressDto { get; set; } = null!;
        public TrialBalanceDto TrialBalanceDto { get; set; } = null!;
    }
}
