﻿using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain
{
    public class ReportingSignatureDto
    {
        public DateTime? AccountantSigningDate { get; set; }
        public List<ReportingSignatureDetailDto> Signatures { get; set; } = new();
        public bool? IncludeAccountantsReport { get; set; }
        public string? AccountantSigningName { get; set; }
    }

    public class ReportingSignatureDetailDto
    {
        public string SignatoryTitle { get; set; } = null!;
        public string SignatoryFirstName { get; set; } = null!;
        public string SignatorySurname { get; set; } = null!;
        public DateTime? SignatureDate { get; set; } = null!;
        public int DisplayOrder { get; set; }
        public SignatureType SignatureType { get; set; }
        public Guid InvolvementUUID { get; set; }
        public string InvolvementType { get; set; } = null!;
    }
}
