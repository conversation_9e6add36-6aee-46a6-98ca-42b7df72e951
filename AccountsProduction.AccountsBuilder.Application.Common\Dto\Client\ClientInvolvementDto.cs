﻿

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.Client
{
    public class ClientInvolvementDto
    {
        public long InvolvementId { get; set; }

        public Guid InvolvementClientGuid { get; set; }

        public string InvolvementType { get; set; } = null!;

        public string InvolvedClientType { get; set; } = null!;

        public string InvolvementClientName { get; set; } = null!;

        public string InvolvementFirstName { get; set; } = null!;

        public string InvolvementTitle { get; set; } = null!;

        public string InvolvementSurname { get; set; } = null!;

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public DateTime? InvolvedDateOfDeath { get; set; }

        public int PdoCode { get; set; }

        public bool IsDeleted { get; set; }
        public DateTime EntityModificationTime { get; set; }
    }
}
