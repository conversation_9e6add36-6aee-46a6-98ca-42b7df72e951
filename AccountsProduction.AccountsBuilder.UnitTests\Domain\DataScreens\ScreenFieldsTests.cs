﻿using AccountsProduction.AccountsBuilder.Domain.DataScreens;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Domain.DataScreens
{
    public class ScreenFieldsTests
    {
        [Fact]
        public void ScreenField_Name_ShouldBeSetCorrectly()
        {
            // Arrange
            var screenField = new ScreenField();

            // Act
            screenField.Name = "TestName";

            // Assert
            Assert.Equal("TestName", screenField.Name);
        }

        [Theory]
        [InlineData(123)]
        [InlineData(123.45)]
        [InlineData(true)]
        [InlineData("TestString")]
        public void ScreenField_Value_ShouldBeSetCorrectly(object value)
        {
            // Arrange
            var screenField = new ScreenField();

            // Act
            screenField.Value = value;

            // Assert
            Assert.Equal(value, screenField.Value);
        }

        [Fact]
        public void ScreenField_Value_ShouldHandleNull()
        {
            // Arrange
            var screenField = new ScreenField();

            // Act
            screenField.Value = null;

            // Assert
            Assert.Null(screenField.Value);
        }
    }
}
