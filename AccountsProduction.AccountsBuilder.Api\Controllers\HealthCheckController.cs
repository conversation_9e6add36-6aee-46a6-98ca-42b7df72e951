﻿using Microsoft.AspNetCore.Mvc;

namespace AccountsProduction.AccountsBuilder.Api.Controllers
{
    [Route("[controller]")]
    [ApiController]
	public class HealthCheckController : ControllerBase
    {

        private readonly IConfiguration _configuration;

        public HealthCheckController(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        [HttpGet]
		public IActionResult CheckHealth()
        {
            return Ok(new
            {
                build_number = _configuration["BUILD_NUMBER"]
            });
        }
    }
}
