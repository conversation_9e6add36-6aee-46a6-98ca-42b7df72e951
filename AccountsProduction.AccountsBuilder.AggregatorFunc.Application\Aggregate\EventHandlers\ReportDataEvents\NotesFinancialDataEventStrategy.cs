﻿using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents
{
    public class NotesFinancialDataEventStrategy : BaseAggregationStrategy
    {
        public override string Name => EventTypes.FinancialNotesDataEvent;

        private readonly ILogger<NotesFinancialDataEventStrategy> _logger;
        private readonly IMapper _mapper;
        private readonly IAccountsBuilderRepository _repository;
        private readonly UserContext _userContext;

        public NotesFinancialDataEventStrategy(ILogger<NotesFinancialDataEventStrategy> logger, IMapper mapper,
            IAccountsBuilderRepository repository, UserContext userContext)
        {
            _logger = logger;
            _mapper = mapper;
            _repository = repository;
            _userContext = userContext;
        }

        public override async Task Process(string requestMessage, Guid tenantId, Guid clientId, Guid periodId, Guid processId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("START event type {eventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, DateTime.UtcNow);

            var notesDto = Deserialize<NotesResponseMessage>(requestMessage);

            var accountsBuilder = await _repository.Get(clientId, periodId, cancellationToken);

            if (accountsBuilder == null || accountsBuilder.TenantId.ToString() != _userContext.TenantId)
            {
                _logger.LogWarning("No accounts builder entity found for clientId {clientId} and periodId {periodId}.", clientId, periodId);
                return;
            }

            var notes = _mapper.Map<Domain.NoteModels.Notes>(notesDto, opt => opt.Items["ReportType"] = accountsBuilder.ReportingStandard.Type);

            _logger.LogInformation($"Notes received for clientId {clientId} and periodId {periodId}.");

            accountsBuilder.AddNotes(notes);

            await _repository.Save(accountsBuilder, cancellationToken);

            _logger.LogInformation("FINISHED event type {eventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, DateTime.UtcNow);
        }
    }
}