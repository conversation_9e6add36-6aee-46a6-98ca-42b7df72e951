﻿using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class OtherMapper : Profile
    {
        public OtherMapper()
        {
            CreateMap<Financial, OtherMessage>()
                .ForMember(s => s.PeriodId, o => o.Ignore())
                .ForMember(d => d.WagesAndSalaries, opt => opt.MapFrom(s => s.WagesAndSalaries))
                .ForMember(d => d.SocialSecurityCosts, opt => opt.MapFrom(s => s.SocialSecurityCosts))
                .ForMember(d => d.OtherPensionCosts, opt => opt.MapFrom(s => s.OtherPensionCosts))
                .ForMember(d => d.DirectorsRemuneration, opt => opt.MapFrom(s => s.DirectorsRemuneration))
                .ForMember(d => d.DirectorsContributionsToDbSchemes, opt => opt.MapFrom(s => s.DirectorsContributionsToDbSchemes))
                .ForMember(d => d.DirectorsContributionsToDcSchemes, opt => opt.MapFrom(s => s.DirectorsContributionsToDcSchemes))
                .ForMember(d => d.DirectorsPensionsPaid, opt => opt.MapFrom(s => s.DirectorsPensionsPaid))
                .ForMember(d => d.DirectorsCompensationForLossOfOffice, opt => opt.MapFrom(s => s.DirectorsCompensationForLossOfOffice))
                .ForMember(d => d.ShareCapitalMovements, opt => opt.MapFrom(s => s.ShareCapitalMovements))
                .ReverseMap();
        }
    }
}
