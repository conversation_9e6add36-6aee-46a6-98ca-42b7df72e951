﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.AutoMapper.Mappers
{
    public class ReportingPeriodMapperTests
    {
        private readonly IMapper _mapper;

        public ReportingPeriodMapperTests()
        {
            var mapperConfig = new MapperConfiguration(cfg =>
                {
                    cfg.AddProfile<TrialBalanceMapper>();
                }
            );

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();
        }

        [Fact]
        public void Should_convert_to_non_financial_data_dto()
        {
            var period1 = Guid.NewGuid();
            var period2 = Guid.NewGuid();

            var periods = new List<ReportingPeriod>
            {
                new ReportingPeriod
                {
                    Id = period1, EndDate = new DateTime(2020, 1, 1), StartDate = new DateTime(2019,1,1)
                },
                new ReportingPeriod
                {
                    Id = period2, EndDate = new DateTime(2021, 1, 1)
                }
            };

            var reportingPeriodDtos = _mapper.Map<List<ReportingPeriod>, List<ReportingPeriodDto>>(periods);

            reportingPeriodDtos.ShouldNotBeNull();
            var actualPeriod1 = reportingPeriodDtos.First(x => x.Id == period1);
            var actualPeriod2 = reportingPeriodDtos.First(x => x.Id == period2);
            actualPeriod1.StartDate.ShouldBe(new DateTime(2019, 1, 1));
            actualPeriod1.EndDate.ShouldBe(new DateTime(2020, 1, 1));
            actualPeriod2.StartDate.ShouldBeNull();
            actualPeriod2.EndDate.ShouldBe(new DateTime(2021, 1, 1));

        }

        [Fact]
        public void Should_convert_to_reportingPeriod()
        {
            var period1 = Guid.NewGuid();

            var periods = new List<ReportingPeriodDto>
            {
                new ReportingPeriodDto
                {
                    Id = period1, EndDate = new DateTime(2020, 1, 1), StartDate = new DateTime(2019,1,1)
                }
            };

            var reportingPeriod = _mapper.Map<List<ReportingPeriodDto>, List<ReportingPeriod>>(periods);

            reportingPeriod.ShouldNotBeNull();
            var actualPeriod1 = reportingPeriod.First(x => x.Id == period1);
            actualPeriod1.StartDate.ShouldBe(new DateTime(2019, 1, 1));
            actualPeriod1.EndDate.ShouldBe(new DateTime(2020, 1, 1));

        }
    }
}
