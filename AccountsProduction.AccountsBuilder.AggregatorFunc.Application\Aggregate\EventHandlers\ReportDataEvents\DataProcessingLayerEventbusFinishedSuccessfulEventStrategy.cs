﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.Invocation;
using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.Message;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents
{
    public class DataProcessingLayerEventbusFinishedSuccessfulEventStrategy(IAccountsBuilderRepository _repository,
        ILogger<DataProcessingLayerEventbusFinishedSuccessfulEventStrategy> _logger, IMapper _mapper)
        : DataProcessingLayerEventbusFinishedEventbusBaseStrategy<FinancialDataEventbusDto>()
    {
        public override string TopicName => "dataprocessinglayer:successful-generation";

        public override async Task<ExecutionResult> ExecuteAsync(EventBusMessage<FinancialDataEventbusDto> message)
        {
            _logger.LogInformation("START event type {eventType} for processId {processId} at eventTimestamp {eventTimestamp}.", TopicName, message.Payload.ProcessId, DateTime.UtcNow);
            var accountsBuilder = await _repository.Get(message.Payload.ProcessId, CancellationToken.None);

            if (accountsBuilder is null)
            {
                _logger.LogWarning("No accounts builder entity found for processId {processId}.", message.Payload.ProcessId);

                return new ExecutionResult()
                {
                    Payload = null,
                    Status = System.Net.HttpStatusCode.NotFound
                };
            }

            var financialDataDto = _mapper.Map<FinancialDataDto>(message.Payload, opt =>
            {
                opt.Items[nameof(FinancialDataDto.ClientId)] = accountsBuilder.ClientId;
                opt.Items[nameof(FinancialDataDto.PeriodId)] = accountsBuilder.PeriodId;
            });

            await (this as IDataProcessingLayerEventFinishedEventSharedBase).StoreFinancialData(financialDataDto, accountsBuilder, _logger, _repository, _mapper, TopicName, message.Payload.ProcessId, CancellationToken.None);

            return new ExecutionResult()
            {
                Payload = null,
                Status = System.Net.HttpStatusCode.OK
            };
        }
    }
}