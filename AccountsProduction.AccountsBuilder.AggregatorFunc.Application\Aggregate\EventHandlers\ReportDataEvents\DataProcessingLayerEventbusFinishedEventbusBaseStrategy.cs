﻿using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.Invocation;
using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.Message;
using Iris.Platform.Eventbus.Client.Dotnet.Services;
using System.Text.Json;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents
{
    public abstract class DataProcessingLayerEventbusFinishedEventbusBaseStrategy<PAYLOAD>()
        : <PERSON>opic<PERSON><PERSON><PERSON>, IDataProcessingLayerEventFinishedEventSharedBase
    {
        public abstract string TopicName { get; }

        private readonly static JsonSerializerOptions JsonSerializerOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        public Task<ExecutionResult> Execute(EventBusMessage<string> message)
        {
            var eventBusMessage = new EventBusMessage<PAYLOAD>()
            {
                ContractVersion = message.ContractVersion,
                CorrelationId = message.CorrelationId,
                Qos = message.Qos,
                Receiver = message.Receiver,
                Sender = message.Sender,
                Status = message.Status,
                TenantId = message.TenantId,
                Topic = message.Topic,
                Trace = message.Trace,
                UserId = message.UserId,
                Payload = JsonSerializer.Deserialize<PAYLOAD>(message.Payload, JsonSerializerOptions)!
            };

            return ExecuteAsync(eventBusMessage);
        }

        public abstract Task<ExecutionResult> ExecuteAsync(EventBusMessage<PAYLOAD> message);
    }
}