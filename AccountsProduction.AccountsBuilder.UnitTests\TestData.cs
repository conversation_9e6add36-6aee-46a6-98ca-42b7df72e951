﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NonFinancialRunner;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels;
using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels.TangibleFixedAsset;
using AccountsProduction.AccountsBuilder.Domain.DataScreens;
using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AccountsProduction.AccountsBuilder.Domain.NoteModels;
using AccountsProduction.AccountsBuilder.Reporting.Application.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using Iris.AccountsProduction.AccountsBuilder.Messages.DataScreenValue;
using System.Text.Json;

namespace AccountsProduction.AccountsBuilder.UnitTests
{
    public static class TestData
    {
        public static string CurrentChangesInAccountingPolicies = "current changes in accounting policies";
        public static string CurrentFinancialInstrumentsAccountingPolicy = "current financial instruments";
        public static string CurrentGovernmentGrantsAccountingPolicy = "current government grants";
        public static string CurrentMembersTransactionsWithTheLlpText = "current members transactions";
        public static string CurrentExemptionsParentAddress = "current parent address";
        public static string CurrentExemptionsParentName = "current parent name";
        public static string CurrentPlantAndMachineryAlternativeBasis = "CurrentPlantAndMachineryAlternativeBasis";
        public static string CurrentPlantAndMachineryCategoryDescription = "CurrentPlantAndMachineryCategoryDescription";
        public static string CurrentComputerEquipmentAlternativeBasis = "CurrentComputerEquipmentAlternativeBasis";
        public static string CurrentComputerEquipmentCategoryDescription = "CurrentComputerEquipmentCategoryDescription";
        public static string CurrentFixturesAndFittingsAlternativeBasis = "CurrentFixturesAndFittingsAlternativeBasis";
        public static string CurrentFixturesAndFittingsCategoryDescription = "CurrentFixturesAndFittingsCategoryDescription";
        public static string CurrentImprovementsToPropertyAlternativeBasis = "CurrentImprovementsToPropertyAlternativeBasis";
        public static string CurrentImprovementsToPropertyCategoryDescription = "CurrentImprovementsToPropertyCategoryDescription";
        public static string CurrentMotorVehiclesAlternativeBasis = "CurrentMotorVehiclesAlternativeBasis";
        public static string CurrentMotorVehiclesCategoryDescription = "CurrentMotorVehiclesCategoryDescription";
        public static string CurrentGoodwillCategoryDescription = "CurrentGoodwillCategoryDescription";
        public static string CurrentGoodwillAlternativeBasis = "CurrentGoodwillAlternativeBasis";
        public static string CurrentPatentsAndLicensesCategoryDescription = "CurrentPatentsAndLicensesCategoryDescription";
        public static string CurrentPatentsAndLicensesAlternativeBasis = "CurrentPatentsAndLicensesAlternativeBasis";
        public static string CurrentDevelopmentCostsCategoryDescription = "CurrentDevelopmentCostsCategoryDescription";
        public static string CurrentDevelopmentCostsAlternativeBasis = "CurrentDevelopmentCostsAlternativeBasis";
        public static string CurrentComputerSoftwareCategoryDescription = "CurrentComputerSoftwareCategoryDescription";
        public static string CurrentComputerSoftwareAlternativeBasis = "CurrentComputerSoftwareAlternativeBasis";
        public static string CurrentFreeholdPropertyAlternativeBasis = "CurrentFreeholdPropertyAlternativeBasis";
        public static string CurrentFreeholdPropertyCategoryDescription = "CurrentFreeholdPropertyCategoryDescription";
        public static string CurrentShortLeaseholdPropertyAlternativeBasis = "CurrentShortLeaseholdPropertyAlternativeBasis";
        public static string CurrentShortLeaseholdPropertyCategoryDescription = "CurrentShortLeaseholdPropertyCategoryDescription";
        public static string CurrentLongLeaseholdPropertyAlternativeBasis = "CurrentLongLeaseholdPropertyAlternativeBasis";
        public static string CurrentLongLeaseholdPropertyCategoryDescription = "CurrentLongLeaseholdPropertyCategoryDescription";
        public static string PreviousExemptionsParentAddress = "previous parent address";
        public static string PreviousExemptionsParentName = "previous parent name";
        public static string PreviousChangesInAccountingPolicies = "previous changes in accounting policies";
        public static string PreviousFinancialInstrumentsAccountingPolicy = "previous financial instruments";
        public static string PreviousGovernmentGrantsAccountingPolicy = "previous government grants";
        public static string PreviousMembersTransactionsWithTheLlpText = "previous members transactions";
        public static string PreviousPlantAndMachineryAlternativeBasis = "PreviousPlantAndMachineryAlternativeBasis";
        public static string PreviousPlantAndMachineryCategoryDescription = "PreviousPlantAndMachineryCategoryDescription";
        public static string PreviousComputerEquipmentAlternativeBasis = "PreviousComputerEquipmentAlternativeBasis";
        public static string PreviousComputerEquipmentCategoryDescription = "PreviousComputerEquipmentCategoryDescription";
        public static string PreviousFixturesAndFittingsAlternativeBasis = "PreviousFixturesAndFittingsAlternativeBasis";
        public static string PreviousFixturesAndFittingsCategoryDescription = "PreviousFixturesAndFittingsCategoryDescription";
        public static string PreviousImprovementsToPropertyAlternativeBasis = "PreviousImprovementsToPropertyAlternativeBasis";
        public static string PreviousImprovementsToPropertyCategoryDescription = "PreviousImprovementsToPropertyCategoryDescription";
        public static string PreviousMotorVehiclesAlternativeBasis = "PreviousMotorVehiclesAlternativeBasis";
        public static string PreviousMotorVehiclesCategoryDescription = "PreviousMotorVehiclesCategoryDescription";
        public static string PreviousGoodwillCategoryDescription = "PreviousGoodwillCategoryDescription";
        public static string PreviousGoodwillAlternativeBasis = "PreviousGoodwillAlternativeBasis";
        public static string PreviousPatentsAndLicensesCategoryDescription = "PreviousPatentsAndLicensesCategoryDescription";
        public static string PreviousPatentsAndLicensesAlternativeBasis = "PreviousPatentsAndLicensesAlternativeBasis";
        public static string PreviousDevelopmentCostsCategoryDescription = "PreviousDevelopmentCostsCategoryDescription";
        public static string PreviousDevelopmentCostsAlternativeBasis = "PreviousDevelopmentCostsAlternativeBasis";
        public static string PreviousComputerSoftwareCategoryDescription = "PreviousComputerSoftwareCategoryDescription";
        public static string PreviousComputerSoftwareAlternativeBasis = "PreviousComputerSoftwareAlternativeBasis";
        public static string PreviousFreeholdPropertyAlternativeBasis = "PreviousFreeholdPropertyAlternativeBasis";
        public static string PreviousFreeholdPropertyCategoryDescription = "PreviousFreeholdPropertyCategoryDescription";
        public static string PreviousShortLeaseholdPropertyAlternativeBasis = "PreviousShortLeaseholdPropertyAlternativeBasis";
        public static string PreviousShortLeaseholdPropertyCategoryDescription = "PreviousShortLeaseholdPropertyCategoryDescription";
        public static string PreviousLongLeaseholdPropertyAlternativeBasis = "PreviousLongLeaseholdPropertyAlternativeBasis";
        public static string PreviousLongLeaseholdPropertyCategoryDescription = "PreviousLongLeaseholdPropertyCategoryDescription";

        public static AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder AbFRS105Completed =>
            JsonSerializer.Deserialize<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(JsonSerializer.Serialize(_abFRS105Completed));

        public static AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder AbFRS1021ACompleted =>
            JsonSerializer.Deserialize<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(JsonSerializer.Serialize(_abFRS1021ACompleted));
        public static AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder AbFRS102Completed =>
            JsonSerializer.Deserialize<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(JsonSerializer.Serialize(_abFRS102Completed));
        public static AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder UnincorporatedCompleted =>
            JsonSerializer.Deserialize<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(JsonSerializer.Serialize(_unincorporatedCompleted));


        private static AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder _abFRS1021ACompleted = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
        {
            ReportingStandard = new ReportingStandard
            {
                Type = "FRS102_1A"
            },
            LicenseData = new LicenseData(),
            NonFinancialData = new NonFinancialData { IsDataCompleted = true },
            InvolvementsData = new InvolvementsData { IsDataCompleted = true },
            DataScreenValue = GetDataScreenValues(),
            ClientId = TestHelpers.Guids.GuidOne,
            FinancialData = new FinancialData
            {
                Status = (int)FinancialDataStatusDto.Success,
                Financials = new List<Financial>
                {
                    new Financial()
                }
            },
            Signatory = new Signatory
            {
                IsSuccessful = true
            },
            AccountingPolicies = new AccountingPolicies
            {
                ClientId = TestHelpers.Guids.GuidOne,
                CorrelationId = TestHelpers.Guids.GuidFour,
                CurrentPeriodAccountingPolicies = GetAccountingPoliciesDataForCurrentPeriod(),
                PreviousPeriodAccountingPolicies = GetAccountingPoliciesDataForPreviousPeriod(),
                IsSuccessful = true
            },
            Notes = new Notes
            {
                IsSuccessful = true
            }
        };

        private static AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder _abFRS102Completed = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
        {
            ReportingStandard = new ReportingStandard
            {
                Type = "FRS102"
            },
            LicenseData = new LicenseData(),
            NonFinancialData = new NonFinancialData { IsDataCompleted = true },
            InvolvementsData = new InvolvementsData { IsDataCompleted = true },
            DataScreenValue = GetDataScreenValues(),
            ClientId = TestHelpers.Guids.GuidOne,
            FinancialData = new FinancialData
            {
                Status = (int)FinancialDataStatusDto.Success,
                Financials = new List<Financial>
                {
                    new Financial()
                }
            },
            Signatory = new Signatory
            {
                IsSuccessful = true
            },
            AccountingPolicies = new AccountingPolicies
            {
                ClientId = TestHelpers.Guids.GuidOne,
                CorrelationId = TestHelpers.Guids.GuidFour,
                CurrentPeriodAccountingPolicies = GetAccountingPoliciesDataForCurrentPeriod(),
                PreviousPeriodAccountingPolicies = GetAccountingPoliciesDataForPreviousPeriod(),
                IsSuccessful = true
            },
            Notes = new Notes
            {
                IsSuccessful = true
            }
        };

        public static DataScreenValue GetDataScreenValues()
        {
            return new DataScreenValue
            {
                ClientId = TestHelpers.Guids.GuidOne,
                PeriodId = TestHelpers.Guids.GuidTwo,
                PreviousPeriodId = TestHelpers.Guids.GuidThree,
                TenantId = TestHelpers.Guids.GuidFour,
                CurrentPeriod = new List<PeriodScreenValue>
                {
                    new ()
                    {
                        ReportMappingTable = "NoteOther",
                        ScreenId = "ScreenId1",
                        ScreenFields = new List<ScreenField>
                        {
                            new() {
                                Name = "Name number",
                                Value = 4321
                            },
                            new() {
                                Name = "Name null",
                                Value = null
                            },
                            new() {
                                Name = "Name True",
                                Value = true
                            },
                            new() {
                                Name = "Name False",
                                Value = false
                            }
                        }
                    },
                    new ()
                    {
                        ReportMappingTable = "NoteAcctgPols",
                        ScreenId = "ScreenId2",
                        ScreenFields = new List<ScreenField>
                        {
                            new() {
                                Name = "Name number",
                                Value = 4321
                            },
                            new() {
                                Name = "Name null",
                                Value = null
                            },
                            new() {
                                Name = "Name True",
                                Value = true
                            },
                            new() {
                                Name = "Name False",
                                Value = false
                            }
                        }
                    }

                },
                PreviousPeriod = new List<PeriodScreenValue>
                {
                    new ()
                    {
                        ReportMappingTable = "NoteProfitAndLoss",
                        ScreenId = "ScreenId3",
                        ScreenFields = new List<ScreenField>
                        {
                            new() {
                                Name = "Name number",
                                Value = 1234
                            },
                            new() {
                                Name = "Name null",
                                Value = null
                            },
                            new() {
                                Name = "Name True",
                                Value = true
                            },
                            new() {
                                Name = "Name False",
                                Value = false
                            }
                        }
                    },
                    new ()
                    {
                        ReportMappingTable = "NoteBalanceSheet",
                        ScreenId = "ScreenId4",
                        ScreenFields = new List<ScreenField>
                        {
                            new() {
                                Name = "Name number",
                                Value = 1234
                            },
                            new() {
                                Name = "Name null",
                                Value = null
                            },
                            new() {
                                Name = "Name True",
                                Value = true
                            },
                            new() {
                                Name = "Name False",
                                Value = false
                            }
                        }
                    }
                }
            };
        }


        public static DataScreenValueMessage GetDataScreenValuesMessage()
        {
            return new DataScreenValueMessage
            {
                ClientId = TestHelpers.Guids.GuidOne,
                PeriodId = TestHelpers.Guids.GuidTwo,
                PreviousPeriodId = TestHelpers.Guids.GuidThree,
                TenantId = TestHelpers.Guids.GuidFour,
                CurrentPeriod = new List<PeriodScreenValueMessage>
                {
                    new ()
                    {
                        ReportMappingTable = "NoteOther",
                        ScreenId = "ScreenId1",
                        ScreenFields = new List<ScreenFieldMessage>
                        {
                            new() {
                                Name = "Name number",
                                Value = 4321
                            },
                            new() {
                                Name = "Name null",
                                Value = null
                            },
                            new() {
                                Name = "Name True",
                                Value = true
                            },
                            new() {
                                Name = "Name False",
                                Value = false
                            }
                        }
                    },
                    new ()
                    {
                        ReportMappingTable = "NoteAcctgPols",
                        ScreenId = "ScreenId2",
                        ScreenFields = new List<ScreenFieldMessage>
                        {
                            new() {
                                Name = "Name number",
                                Value = 4321
                            },
                            new() {
                                Name = "Name null",
                                Value = null
                            },
                            new() {
                                Name = "Name True",
                                Value = true
                            },
                            new() {
                                Name = "Name False",
                                Value = false
                            }
                        }
                    },

                    new() {
                        ReportMappingTable = "NoteProfitAndLoss",
                        ScreenId = "ScreenId3",
                        ScreenFields = new List<ScreenFieldMessage>
                        {
                            new() {
                                Name = "Name number",
                                Value = 4321
                            },
                            new() {
                                Name = "Name null",
                                Value = null
                            },
                            new() {
                                Name = "Name True",
                                Value = true
                            },
                            new() {
                                Name = "Name False",
                                Value = false
                            },
                        }
                    },
                    new() {
                        ReportMappingTable = "NoteBalanceSheet",
                        ScreenId = "ScreenId4",
                        ScreenFields = new List<ScreenFieldMessage>
                        {
                            new() {
                                Name = "Name number",
                                Value = 4321
                            },
                            new() {
                                Name = "Name null",
                                Value = null
                            },
                            new() {
                                Name = "Name True",
                                Value = true
                            },
                            new() {
                                Name = "Name False",
                                Value = false
                            },
                        }
                    },
                    new() {
                        ReportMappingTable = "Reports",
                        ScreenId = "ScreenId5",
                        ScreenFields = new List<ScreenFieldMessage>
                        {
                            new() {
                                Name = "Name number",
                                Value = 4321
                            },
                            new() {
                                Name = "Name null",
                                Value = null
                            },
                            new() {
                                Name = "Name True",
                                Value = true
                            },
                            new() {
                                Name = "Name False",
                                Value = false
                            },
                        }
                    }
                }
            };
        }

        public static SignatureDto GetSignatureWithAuditAccountantName()
        {
            return new SignatureDto
            {
                AccountantSigningName = "Test Name",
                AccountantSigningDate = DateTime.UtcNow
            };
        }

        public static SignatureDto GetSignatureWithNoAuditAccountantName()
        {
            return new SignatureDto
            {
                AccountantSigningDate = DateTime.UtcNow
            };
        }

        public static List<NoteOther> GetNotesOtherSet()
        {
            return new List<NoteOther>()
            {
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidTwo,
                    ScreenId = "ScreenId1",
                    NoteType = "Name number",
                    NoteValue = 4321
                },
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidTwo,
                    ScreenId = "ScreenId1",
                    NoteType = "Name null",
                    NoteText = null
                },
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidTwo,
                    ScreenId = "ScreenId1",
                    NoteType = "Name True",
                    NoteValue = 1
                },
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidTwo,
                    ScreenId = "ScreenId1",
                    NoteType = "Name False",
                    NoteValue = 0
                },
            };
        }

        public static List<NoteProfitAndLoss> GetNotesProfitAndLossSet()
        {
            return new List<NoteProfitAndLoss>()
            {
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidThree,
                    ScreenId = "ScreenId3",
                    NoteType = "Name number",
                    NoteValue = 1234
                },
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidThree,
                    ScreenId = "ScreenId3",
                    NoteType = "Name null",
                    NoteText = "abc"
                },
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidThree,
                    ScreenId = "ScreenId3",
                    NoteType = "Name True",
                    NoteValue = 123
                },
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidThree,
                    ScreenId = "ScreenId3",
                    NoteType = "Name False",
                    NoteValue = 321
                },
            };
        }

        public static List<NoteAccountingPolicies> GetNotesAcctgPolsSet()
        {
            return new List<NoteAccountingPolicies>()
            {
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidTwo,
                    ScreenId = "ScreenId2",
                    NoteType = "Name number",
                    NoteValue = 1234
                },
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidTwo,
                    ScreenId = "ScreenId2",
                    NoteType = "Name null",
                    NoteText = "abc"
                },
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidTwo,
                    ScreenId = "ScreenId2",
                    NoteType = "Name True",
                    NoteValue = 123
                },
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidTwo,
                    ScreenId = "ScreenId2",
                    NoteType = "Name False",
                    NoteValue = 321
                },
            };
        }

        public static List<NoteBalanceSheet> GetNotesBalanceSheetSet()
        {
            return new List<NoteBalanceSheet>()
            {
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidThree,
                    ScreenId = "ScreenId4",
                    NoteType = "Name number",
                    NoteValue = 1234
                },
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidThree,
                    ScreenId = "ScreenId4",
                    NoteType = "Name null",
                    NoteText = "abc"
                },
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidThree,
                    ScreenId = "ScreenId4",
                    NoteType = "Name True",
                    NoteValue = 123
                },
                new ()
                {
                    ClientId = TestHelpers.Guids.GuidOne,
                    AccountPeriodId = TestHelpers.Guids.GuidThree,
                    ScreenId = "ScreenId4",
                    NoteType = "Name False",
                    NoteValue = 321
                },
            };
        }


        public static AccountingPoliciesData GetAccountingPoliciesDataForCurrentPeriod()
        {
            return new AccountingPoliciesData
            {
                ExemptionsFinancialStatements = new ExemptionsFinancialStatements
                {
                    ParentAddress = CurrentExemptionsParentAddress,
                    ParentName = CurrentExemptionsParentName,
                    Section = ExemptionsSectionEnum.Section_401
                },
                ForeignCurrencies = true,
                PresentationCurrency = true,
                ResearchAndDevelopment = true,
                FinancialInstrumentsAccountingPolicy = CurrentChangesInAccountingPolicies,
                GovernmentGrantsAccountingPolicy = CurrentGovernmentGrantsAccountingPolicy,
                MembersTransactionsWithTheLlpText = CurrentMembersTransactionsWithTheLlpText,
                ChangesInAccountingPolicies = CurrentChangesInAccountingPolicies,
                GoodwillMaterial = true,
                TangibleFixedAssets = new TangibleFixedAssets
                {
                    PlantAndMachinery = new PlantAndMachineries
                    {
                        ClassNameCustomization = "Plant and Machineries",
                        PlantAndMachinery = new AssetsAdjustment
                        {
                            AlternativeBasis = CurrentPlantAndMachineryAlternativeBasis,
                            CategoryDescription = CurrentPlantAndMachineryCategoryDescription,
                            ReducingBalanceBasis = 1,
                            StraightLineBasis = 1
                        },
                        ComputerEquipment = new AssetsAdjustment
                        {
                            AlternativeBasis = CurrentComputerEquipmentAlternativeBasis,
                            CategoryDescription = CurrentComputerEquipmentCategoryDescription,
                            ReducingBalanceBasis = 2,
                            StraightLineBasis = 2
                        },
                        FixturesAndFittings = new AssetsAdjustment
                        {
                            AlternativeBasis = CurrentFixturesAndFittingsAlternativeBasis,
                            CategoryDescription = CurrentFixturesAndFittingsCategoryDescription,
                            ReducingBalanceBasis = 3,
                            StraightLineBasis = 3
                        },
                        ImprovementsToProperty = new AssetsAdjustment
                        {
                            AlternativeBasis = CurrentImprovementsToPropertyAlternativeBasis,
                            CategoryDescription = CurrentImprovementsToPropertyCategoryDescription,
                            ReducingBalanceBasis = 4,
                            StraightLineBasis = 4
                        },
                        MotorVehicles = new AssetsAdjustment
                        {
                            AlternativeBasis = CurrentMotorVehiclesAlternativeBasis,
                            CategoryDescription = CurrentMotorVehiclesCategoryDescription,
                            ReducingBalanceBasis = 5,
                            StraightLineBasis = 5
                        }
                    },
                    LandAndBuildings = new LandAndBuildings
                    {
                        ClassNameCustomization = "Land and Buildings",
                        FreeholdProperty = new AssetsAdjustment
                        {
                            AlternativeBasis = CurrentFreeholdPropertyAlternativeBasis,
                            CategoryDescription = CurrentFreeholdPropertyCategoryDescription,
                            ReducingBalanceBasis = 6,
                            StraightLineBasis = 6
                        },
                        ShortLeaseholdProperty = new AssetsAdjustment
                        {
                            AlternativeBasis = CurrentShortLeaseholdPropertyAlternativeBasis,
                            CategoryDescription = CurrentShortLeaseholdPropertyCategoryDescription,
                            ReducingBalanceBasis = 7,
                            StraightLineBasis = 7
                        },
                        LongLeaseholdProperty = new AssetsAdjustment
                        {
                            AlternativeBasis = CurrentLongLeaseholdPropertyAlternativeBasis,
                            CategoryDescription = CurrentLongLeaseholdPropertyCategoryDescription,
                            ReducingBalanceBasis = 8,
                            StraightLineBasis = 8
                        }
                    }
                },
                IntangibleAssets = new IntangibleAssets
                {
                    Goodwill = new AssetsAdjustment
                    {
                        CategoryDescription = CurrentGoodwillCategoryDescription,
                        AlternativeBasis = CurrentGoodwillAlternativeBasis,
                        ReducingBalanceBasis = 1,
                        StraightLineBasis = 1
                    },
                    PatentsAndLicenses = new AssetsAdjustment
                    {
                        CategoryDescription = CurrentPatentsAndLicensesCategoryDescription,
                        AlternativeBasis = CurrentPatentsAndLicensesAlternativeBasis,
                        ReducingBalanceBasis = 2,
                        StraightLineBasis = 2
                    },
                    DevelopmentCosts = new AssetsAdjustment
                    {
                        CategoryDescription = CurrentDevelopmentCostsCategoryDescription,
                        AlternativeBasis = CurrentDevelopmentCostsAlternativeBasis,
                        ReducingBalanceBasis = 3,
                        StraightLineBasis = 3
                    },
                    ComputerSoftware = new AssetsAdjustment
                    {
                        CategoryDescription = CurrentComputerSoftwareCategoryDescription,
                        AlternativeBasis = CurrentComputerSoftwareAlternativeBasis,
                        ReducingBalanceBasis = 4,
                        StraightLineBasis = 4
                    }
                }
            };
        }

        public static AccountingPoliciesData GetAccountingPoliciesDataForPreviousPeriod()
        {
            return new AccountingPoliciesData
            {
                ExemptionsFinancialStatements = new ExemptionsFinancialStatements
                {
                    ParentAddress = PreviousExemptionsParentAddress,
                    ParentName = PreviousExemptionsParentName,
                    Section = ExemptionsSectionEnum.Section_401
                },
                ForeignCurrencies = false,
                PresentationCurrency = false,
                ResearchAndDevelopment = false,
                FinancialInstrumentsAccountingPolicy = PreviousChangesInAccountingPolicies,
                GovernmentGrantsAccountingPolicy = PreviousGovernmentGrantsAccountingPolicy,
                MembersTransactionsWithTheLlpText = PreviousMembersTransactionsWithTheLlpText,
                ChangesInAccountingPolicies = PreviousChangesInAccountingPolicies,
                GoodwillMaterial = false,
                TangibleFixedAssets = new TangibleFixedAssets
                {
                    PlantAndMachinery = new PlantAndMachineries
                    {
                        ClassNameCustomization = "Previous Plant and machinery",
                        PlantAndMachinery = new AssetsAdjustment
                        {
                            AlternativeBasis = PreviousPlantAndMachineryAlternativeBasis,
                            CategoryDescription = PreviousPlantAndMachineryCategoryDescription,
                            ReducingBalanceBasis = 11,
                            StraightLineBasis = 11
                        },
                        ComputerEquipment = new AssetsAdjustment
                        {
                            AlternativeBasis = PreviousComputerEquipmentAlternativeBasis,
                            CategoryDescription = PreviousComputerEquipmentCategoryDescription,
                            ReducingBalanceBasis = 22,
                            StraightLineBasis = 22
                        },
                        FixturesAndFittings = new AssetsAdjustment
                        {
                            AlternativeBasis = PreviousFixturesAndFittingsAlternativeBasis,
                            CategoryDescription = PreviousFixturesAndFittingsCategoryDescription,
                            ReducingBalanceBasis = 33,
                            StraightLineBasis = 33
                        },
                        ImprovementsToProperty = new AssetsAdjustment
                        {
                            AlternativeBasis = PreviousImprovementsToPropertyAlternativeBasis,
                            CategoryDescription = PreviousImprovementsToPropertyCategoryDescription,
                            ReducingBalanceBasis = 44,
                            StraightLineBasis = 44
                        },
                        MotorVehicles = new AssetsAdjustment
                        {
                            AlternativeBasis = PreviousMotorVehiclesAlternativeBasis,
                            CategoryDescription = PreviousMotorVehiclesCategoryDescription,
                            ReducingBalanceBasis = 55,
                            StraightLineBasis = 55
                        }
                    },
                    LandAndBuildings = new LandAndBuildings
                    {
                        ClassNameCustomization = "Previous Land and Buildings",
                        FreeholdProperty = new AssetsAdjustment
                        {
                            AlternativeBasis = PreviousFreeholdPropertyAlternativeBasis,
                            CategoryDescription = PreviousFreeholdPropertyCategoryDescription,
                            ReducingBalanceBasis = 66,
                            StraightLineBasis = 66
                        },
                        ShortLeaseholdProperty = new AssetsAdjustment
                        {
                            AlternativeBasis = PreviousShortLeaseholdPropertyAlternativeBasis,
                            CategoryDescription = PreviousShortLeaseholdPropertyCategoryDescription,
                            ReducingBalanceBasis = 77,
                            StraightLineBasis = 77
                        },
                        LongLeaseholdProperty = new AssetsAdjustment
                        {
                            AlternativeBasis = PreviousLongLeaseholdPropertyAlternativeBasis,
                            CategoryDescription = PreviousLongLeaseholdPropertyCategoryDescription,
                            ReducingBalanceBasis = 88,
                            StraightLineBasis = 88
                        }
                    }
                },
                IntangibleAssets = new IntangibleAssets
                {
                    Goodwill = new AssetsAdjustment
                    {
                        CategoryDescription = PreviousGoodwillCategoryDescription,
                        AlternativeBasis = PreviousGoodwillAlternativeBasis,
                        ReducingBalanceBasis = 11,
                        StraightLineBasis = 11
                    },
                    PatentsAndLicenses = new AssetsAdjustment
                    {
                        CategoryDescription = PreviousPatentsAndLicensesCategoryDescription,
                        AlternativeBasis = PreviousPatentsAndLicensesAlternativeBasis,
                        ReducingBalanceBasis = 22,
                        StraightLineBasis = 22
                    },
                    DevelopmentCosts = new AssetsAdjustment
                    {
                        CategoryDescription = PreviousDevelopmentCostsCategoryDescription,
                        AlternativeBasis = PreviousDevelopmentCostsAlternativeBasis,
                        ReducingBalanceBasis = 33,
                        StraightLineBasis = 33
                    },
                    ComputerSoftware = new AssetsAdjustment
                    {
                        CategoryDescription = PreviousComputerSoftwareCategoryDescription,
                        AlternativeBasis = PreviousComputerSoftwareAlternativeBasis,
                        ReducingBalanceBasis = 44,
                        StraightLineBasis = 44
                    }
                }
            };
        }

        public static ValidationIssue ValidationIssue => new ValidationIssue
        {
            ErrorCode = ValidationCodes.CompanyNumber,
            Type = ValidationRuleType.Missing,
            Target = Target.IssueLog,
            ErrorCategory = ErrorCategoryType.Mandatory,
            DisplayName = "Company number",
            Breadcrumb = ClientManagementBreadcrumbs.InformationTab.ToString(),
            Name = NonFinancialValidations.CompanyNumber,
            Description = "Some description"
        };

        private static AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder _abFRS105Completed = new()
        {
            ReportingStandard = new ReportingStandard
            {
                Type = "FRS105"
            },
            LicenseData = new LicenseData(),
            NonFinancialData = new NonFinancialData { IsDataCompleted = true },
            InvolvementsData = new InvolvementsData { IsDataCompleted = true },
            ClientId = TestHelpers.Guids.GuidOne,
            FinancialData = new FinancialData
            {
                Status = (int)FinancialDataStatusDto.Success,
                Financials = new List<Financial>
                    {
                        new Financial()
                    }
            },
            Signatory = new Signatory
            {
                IsSuccessful = true
            },
            Notes = new Notes
            {
                CurrentPeriod = new NotesData
                {
                    AverageNumberOfEmployees = new AverageNumberOfEmployees
                    {
                        PreviousPeriod = 1,
                        CurrentPeriod = 2
                    },
                    OffBalanceSheetArrangements = "text1",
                    AdvancesCreditAndGuaranteesGrantedToDirectors = "text2",
                    GuaranteesAndOtherFinancialCommitments = "text3",
                    RelatedPartyTransactions = "text4",
                    LoansAndOtherDebtsDueToMembers = "text44",
                    AdditionalNote1 = new AdditionalNote { NoteTitle = "text5", NoteText = "text6" },
                    AdditionalNote2 = new AdditionalNote { NoteTitle = "text7", NoteText = "text8" },
                    ControllingPartyNote = "text9",
                    IntangibleAssetsRevaluation = "text10",
                    OperatingProfitLoss = new OperatingProfitLoss
                    {
                        IsEnabled = true,
                        Items = new List<OperatingProfitLossItem> {
                            new OperatingProfitLossItem { Index = 1, Description = "Test1", Value = 201 },
                            new OperatingProfitLossItem { Index = 2, Description = "Test2", Value = 202.34M },
                            new OperatingProfitLossItem { Index = 3, Description = "Test3", Value = -203.67M },
                    }
                    }
                },
                IsSuccessful = true
            },
            PracticeDetails = new PracticeDetails
            {
                ReferredType = 1,
                SupervisingBody = 2,
                Name = "name",
                AddressLine1 = "AddressLine1",
                AddressLine2 = "AddressLine2",
                AddressLine3 = "AddressLine3",
                AddressTown = "AddressTown",
                AddressCounty = "AddressCounty",
                AddressPostcode = "AddressPostcode",
                AddressCountry = "AddressCountry"
            },
            TenantId = TestHelpers.Guids.GuidThree
        };

        private static AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder _unincorporatedCompleted = new()
        {
            ReportingStandard = new ReportingStandard
            {
                Type = ReportStandardType.UNINCORPORATED
            },
            InvolvementsData = new InvolvementsData { IsDataCompleted = true },
            NonFinancialData = new NonFinancialData { IsDataCompleted = true, BusinessType = "Partnership" },

            ClientId = TestHelpers.Guids.GuidOne,
            FinancialData = new FinancialData
            {
                Status = (int)FinancialDataStatusDto.Success,
                Financials = new List<Financial>
                    {
                        new Financial()
                    }
            },
            Signatory = new Signatory
            {
                IsSuccessful = true
            },
            AccountingPolicies = new AccountingPolicies
            {
                ClientId = TestHelpers.Guids.GuidOne,
                CorrelationId = TestHelpers.Guids.GuidFour,
                CurrentPeriodAccountingPolicies = new AccountingPoliciesData
                {
                    ExemptionsFinancialStatements = new ExemptionsFinancialStatements
                    {
                        ParentAddress = CurrentExemptionsParentAddress,
                        ParentName = CurrentExemptionsParentName,
                        Section = ExemptionsSectionEnum.Section_401
                    },
                    ForeignCurrencies = true,
                    PresentationCurrency = true,
                    ResearchAndDevelopment = true,
                    FinancialInstrumentsAccountingPolicy = CurrentChangesInAccountingPolicies,
                    GovernmentGrantsAccountingPolicy = CurrentGovernmentGrantsAccountingPolicy,
                    MembersTransactionsWithTheLlpText = CurrentMembersTransactionsWithTheLlpText,
                    ChangesInAccountingPolicies = CurrentChangesInAccountingPolicies
                },
                IsSuccessful = true
            },
            Notes = new Notes
            {
                IsSuccessful = true
            },
            ProfitShareData = new ProfitShareData
            {
                IsSuccessful = true
            }
        };
    }
}
