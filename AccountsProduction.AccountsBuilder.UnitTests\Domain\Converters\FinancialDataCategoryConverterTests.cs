﻿using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.Converters;
using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Domain
{
    public class FinancialCategoryConverterTests
    {
        private const string accountCodeKey = "AccountCode";
        private const string amountKey = "Amount";
        private const string subaccountCodeKey = "SubaccountCode";
        private const string descriptionKey = "Description";
        private const string directorCodeKey = "DirectorCode";
        private const string valueKey = "Value";
        private const string drilldownKey = "DrilldownData";
        private const string sectorIdKey = "SectorId";
        private const string sectorCreatedDateKey = "SectorCreatedDate";

        [Fact]
        public void ToEntry_Should_throw_exception_when_value_is_null()
        {
            IPropertyConverter converter = new FinancialDataCategoryConverter();
            Should.Throw<ArgumentOutOfRangeException>(() => converter.ToEntry(null));
        }


        [Fact]
        public void ToEntry_SectorIdNotNull_AddsSectorIdToDrilldownDocument()
        {
            IPropertyConverter converter = new FinancialDataCategoryConverter();
            var financialDataCategory = new FinancialDataCategory
            {
                Value = "TestValue",
                DrilldownData = new List<FinancialDataDrilldown>
                {
                    new FinancialDataDrilldown
                    {
                        AccountCode = 123,
                        Amount = 456.78m,
                        Description = "TestDescription",
                        SectorId = Guid.NewGuid() // Non-null SectorId
                    }
                }
            };

            var entry = converter.ToEntry(financialDataCategory) as Document;
            var drilldownList = entry["DrilldownData"] as DynamoDBList;
            var drilldownDocument = drilldownList.Entries[0] as Document;

            Assert.NotNull(drilldownDocument);
            Assert.True(drilldownDocument.ContainsKey("SectorId"));
            Assert.NotNull(drilldownDocument["SectorId"]);
        }

        [Fact]
        public void ToEntry_SectorCreatedDateNotNull_AddsSectorCreatedDateToDrilldownDocument()
        {
            IPropertyConverter converter = new FinancialDataCategoryConverter();
            var financialDataCategory = new FinancialDataCategory
            {
                Value = "TestValue",
                DrilldownData = new List<FinancialDataDrilldown>
                {
                    new FinancialDataDrilldown
                    {
                        AccountCode = 123,
                        Amount = 456.78m,
                        Description = "TestDescription",
                        SectorCreatedDate = DateTime.UtcNow // Non-null SectorCreatedDate
                    }
                }
            };

            var entry = converter.ToEntry(financialDataCategory) as Document;
            var drilldownList = entry["DrilldownData"] as DynamoDBList;
            var drilldownDocument = drilldownList.Entries[0] as Document;

            Assert.NotNull(drilldownDocument);
            Assert.True(drilldownDocument.ContainsKey("SectorCreatedDate"));
            Assert.NotNull(drilldownDocument["SectorCreatedDate"]);
        }

        [Fact]
        public void Should_convert_to_dbentry()
        {
            var valueToConvert = new FinancialDataCategory
            {
                Value = "100",
                DrilldownData = new List<FinancialDataDrilldown>
                {
                    new FinancialDataDrilldown
                    {
                        AccountCode = 1,
                        Amount = 200,
                        Description = "test",
                        SubAccountCode = null,
                        DirectorCode = 1
                    },
                    new FinancialDataDrilldown
                    {
                        AccountCode = 1,
                        Amount = 200,
                        Description = "test",
                        SubAccountCode = 20,
                        DirectorCode = 2
                    },
                    new FinancialDataDrilldown
                    {
                        AccountCode = 1,
                        Amount = 200,
                        Description = "test",
                        SubAccountCode = 20,
                        DirectorCode = null
                    }
                }
            };

            IPropertyConverter converter = new FinancialDataCategoryConverter();
            var result = converter.ToEntry(valueToConvert);

            result.ShouldBeOfType(typeof(Document));
            result.ShouldNotBeNull();
            var document = result as Document;
            document?.Keys.ShouldContain(valueKey);
            ((string)document?[valueKey]).ShouldBe(valueToConvert.Value);
            document?.Keys.ShouldContain(drilldownKey);
            document?[drilldownKey].ShouldBeOfType(typeof(DynamoDBList));
            (document?[drilldownKey] as DynamoDBList)?.Entries.Count.ShouldBe(valueToConvert.DrilldownData.Count);
        }

        [Fact]
        public void Should_convert_from_string_entry_to_financial_category()
        {
            var dbentry = new Primitive("100");
            dbentry.Type = DynamoDBEntryType.String;

            IPropertyConverter converter = new FinancialDataCategoryConverter();
            var result = converter.FromEntry(dbentry);

            result.ShouldNotBeNull();
            result.ShouldBeOfType(typeof(FinancialDataCategory));
            ((FinancialDataCategory)result).Value.ShouldBe(dbentry.Value);
        }

        [Fact]
        public void Should_convert_from_complex_entry_to_financial_category()
        {
            var account = 2;
            decimal amount = 300;
            var description = "test";
            var directorCode = 1;
            var sectorId = TestHelpers.Guids.GuidOne;
            var sectorCreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var dbentry = new Document();
            dbentry.Add(new KeyValuePair<string, DynamoDBEntry>(valueKey, "200"));
            var drilldownDocument = new Document
            {
                new KeyValuePair<string, DynamoDBEntry>(accountCodeKey, account),
                new KeyValuePair<string, DynamoDBEntry>(amountKey, amount),
                new KeyValuePair<string, DynamoDBEntry>(descriptionKey, description),
                new KeyValuePair<string, DynamoDBEntry>(directorCodeKey, directorCode),
                new KeyValuePair<string, DynamoDBEntry>(sectorIdKey, sectorId),
                new KeyValuePair<string, DynamoDBEntry>(sectorCreatedDateKey, sectorCreatedDate)

            };
            var list = new DynamoDBList();
            list.Add(drilldownDocument);
            dbentry.Add(new KeyValuePair<string, DynamoDBEntry>(drilldownKey, list));

            IPropertyConverter converter = new FinancialDataCategoryConverter();
            var result = converter.FromEntry(dbentry);

            result.ShouldNotBeNull();
            result.ShouldBeOfType(typeof(FinancialDataCategory));
            ((FinancialDataCategory)result).Value.ShouldBe(dbentry[valueKey]);
            ((FinancialDataCategory)result).DrilldownData.Count.ShouldBe(1);
            ((FinancialDataCategory)result).DrilldownData.First().AccountCode.ShouldBe(account);
            ((FinancialDataCategory)result).DrilldownData.First().Amount.ShouldBe(amount);
            ((FinancialDataCategory)result).DrilldownData.First().Description.ShouldBe(description);
            ((FinancialDataCategory)result).DrilldownData.First().DirectorCode.ShouldBe(directorCode);
            ((FinancialDataCategory)result).DrilldownData.First().SectorId.ShouldBe(sectorId);
            ((FinancialDataCategory)result).DrilldownData.First().SectorCreatedDate.ShouldBe(sectorCreatedDate);
        }
    }
}
