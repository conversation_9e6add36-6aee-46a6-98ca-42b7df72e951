{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:13022/", "sslPort": 44317}}, "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "AccountsProduction.AccountsBuilder.Api": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "Logging__LogLevel__Default": "Debug", "AWS__Region": "eu-west-2", "AWS_DEFAULT_REGION": "eu-west-2", "AWS_REGION": "eu-west-2", "AWS_ACCOUNT_ID": "************", "AWS_ACCESS_KEY_ID": "********************", "AWS_SECRET_ACCESS_KEY": "xz6hiCpkzEfvXVglzj9N3cTk55ykpHHgQu0rrAeS", "AWS_SESSION_TOKEN": "IQoJb3JpZ2luX2VjEMb//////////wEaCWV1LXdlc3QtMiJHMEUCIQCzvmA0uesQaGWgNcW2d3QqjqKSPEa57l64HpzCOydslQIgfZ2SETh70WIUzD/ps7R3Hv5X668OAUTVj1Ffr6fKNaIqowIIj///////////ARAEGgwyNDU2MzM5MzQ4MTIiDEsD/J4Po5Pi/moUPCr3AbyW9x3iDb6Kp7iId36gXi+jXxY7QIWwee57kPU1IotxV2l5j6RhEkJWINx+DDk8o0cCdeH0hSzWjISaf/ghn4uVXQFq9JrYn83P/FiBr8Opq1HRdFqvjmHi8/WLTsqnE/RbJW7+Su9E8KgNrLYsE3MbZAdMlDRWmpNkFTWWjYGosPfgQDEFqSvXr4wMM3vhIC0mHuInXeSNKz542SB7RTbj+xxz0Iaa/YrkK7CfoQkxNdjnGgFgMyJ4PgsNahkbmBocu7coF0KJFGcF/F1mMD60z6KH7RrgBiRF9Y0ka4naD1ZpZkvqwqvQgoUl4Wfmpz50f4SeWi8wtsLhwQY6nQGDoMpoQ++cUiJMzXjEiKm2MQSeWJvb5ojekydZVuYOA8bXlDGpSQayK3/fXV7ukeNwKRw4WrivbVXXeCSxjnVAL/IR4LM3OVkwIMsDWbeLv6hNWz+Gq/vSWylLFF/KQIe9IUKKqN07p9gUILiTNebYt3PPPUAWkghllzdl8cR9YDA3tJhpbjvrYegWQHqfGe/KogZDn1r3XaCW2J+K", "AWS_SVC_ROLE_ARN": "arn:aws:sts::************:assumed-role/Developer/aniketsession", "AWS_Profile": "Development", "AWS__Profile": "Development", "ACCOUNT_PERIOD_API_HOST": "vpce-0243baf16b4d45d9b-orv6s6vv.execute-api.eu-west-2.vpce.amazonaws.com/v1", "ACCOUNT_PERIOD_API_ID": "2fjj3yrota", "ACCOUNT_PERIOD_API_KEY": "7LfTggYWY72nuOdBBrIRrgc1UhwqMxT1Gqy7groh", "ACCOUNT_PERIOD_API_SCHEME": "https", "DB_CONNECTION": "Server=localhost;Port=5432;Database=hubdb;User Id=********;Password=********;", "REPORTINGCORE_DB_CONNECTION": "Server=localhost;Port=5432;Database=coredb;User Id=********;Password=********;", "ACCOUNTS_PRODUCTION_DB_DATABASE_NAME": "accountsproduction_hub_db", "ACCOUNTS_PRODUCTION_DB_HOST": "accountsproduction-hub-dbs-instance-development.cluster-ckvo9ttwlplu.eu-west-2.rds.amazonaws.com", "ACCOUNTS_PRODUCTION_DB_PORT": "5432", "ACCOUNTS_PRODUCTION_DB_USERNAME": "accountsproduction_hub_db_user", "ACCOUNTSBUILDER_API_HOST": "vpce-0243baf16b4d45d9b-orv6s6vv.execute-api.eu-west-2.vpce.amazonaws.com/v1", "ACCOUNTSBUILDER_API_ID": "bnc83xp1ji", "ACCOUNTSBUILDER_API_KEY": "aG6VrQzlAm3cS7Z8Y3kOXa9Mn25GSTwq6ABCvOPE", "ACCOUNTSBUILDER_API_SCHEME": "https", "ACCOUNTSBUILDER_REPORTING_QUEUE": "https://sqs.eu-west-2.amazonaws.com/************/accountsproduction-accountsbuilder-reporting-sqs", "ACCOUNTSPRODUCTION_ACCOUNTPERIOD_REQUEST_TOPIC": "arn:aws:sns:eu-west-2:************:accountsproduction-accountperiod-request-topic", "ACCOUNTSPRODUCTION_ACCOUNTSBUILDER_INPUT_TOPIC": "arn:aws:sns:eu-west-2:************:accountsproduction-accountsbuilder-input-topic", "ACCOUNTSPRODUCTION_API_HOST": "vpce-0243baf16b4d45d9b-orv6s6vv.execute-api.eu-west-2.vpce.amazonaws.com/v1", "ACCOUNTSPRODUCTION_API_ID": "x2i0zl16zk", "ACCOUNTSPRODUCTION_API_KEY": "kU0tUSxSwU5z0Zu5ujNZb6dsH0e1efC455kQoTc5", "ACCOUNTSPRODUCTION_API_SCHEME": "https", "BUILD_NUMBER": "%build.number%", "CLIENT_ADDRESS_API_URL": "address/client/", "CLIENT_API_HOST": "vpce-0243baf16b4d45d9b-orv6s6vv.execute-api.eu-west-2.vpce.amazonaws.com/v1", "CLIENT_API_ID": "jsv7iud33f", "CLIENT_API_KEY": "SyAnWvAoQn3YALnLVuRUj1ZBfCtjNfZI1KxYEz7N", "CLIENT_API_SCHEME": "https", "CLIENT_API_URL": "client/", "CLIENT_INVOLVEMENT_API_URL": "involvement/client/", "CONCURRENCY_RETRY_COUNT": "3", "ENTITY_INVOLVEMENT_API_URL": "involvement/entity/", "REPORTING_TRIALBALANCE_REQUEST_TOPIC": "arn:aws:sns:eu-west-2:************:reporting-trialbalance-processing-request-topic", "TRIAL_BALANCE_API_HOST": "vpce-0243baf16b4d45d9b-orv6s6vv.execute-api.eu-west-2.vpce.amazonaws.com/v1", "TRIAL_BALANCE_API_ID": "2y1qwvx271", "TRIAL_BALANCE_API_KEY": "WxKJXO31oC1iS26QV9oee4ETfKPACF8W33GdEXMp", "TRIAL_BALANCE_API_SCHEME": "https", "EVENTBUS_AWS_ACCOUNT_ID": "************", "EVENT_BUS_MAIN_QUEUE_URL": "https://sqs.eu-west-2.amazonaws.com/************/platform-eventbus-main-queue", "EVENTBUS_LARGE_PAYLOADS_S3_BUCKET_NAME": "platform-eventbus-large-payloads"}, "applicationUrl": "https://localhost:5001;http://localhost:5000"}, "Mock Lambda Test Tool": {"commandName": "Executable", "executablePath": "%USERPROFILE%\\.dotnet\\tools\\dotnet-lambda-test-tool-8.0.exe", "commandLineArgs": "--port 5050", "workingDirectory": ".\\bin\\$(Configuration)\\net8.0"}}}