﻿namespace AccountsProduction.AccountsBuilder.Domain
{
    public class ReportingStandard
    {
        public ReportingStandard()
        {

        }

        public ReportingStandard(string id, string name, string type, ReportingStandardVersion version)
        {
            Id = id;
            Name = name;
            Type = type;
            Version = version;
        }

        public string Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public ReportingStandardVersion Version { get; set; }
    }
}
