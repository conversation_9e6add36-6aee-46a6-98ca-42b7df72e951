﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Infrastructure.Services;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using Shouldly;
using System.Net;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Infrastructure.Services
{
    public class AccountsProductionServiceTests
    {
        private readonly Mock<IEnvVariableProvider> _envVariableProvider;
        private readonly Mock<UserContext> _userContext;
        private readonly Mock<ILogger<AccountsProductionService>> _logger;

        public AccountsProductionServiceTests()
        {
            _envVariableProvider = new Mock<IEnvVariableProvider>();
            _envVariableProvider.Setup(obj => obj.AccountsProductionApiScheme).Returns("https");
            _envVariableProvider.Setup(obj => obj.AccountsProductionApiHost).Returns("api.elements-development.iris.co.uk/accountsproduction-accountperiod/v1");
            _envVariableProvider.Setup(obj => obj.AccountsProductionApiKey).Returns("ApiKey");
            _envVariableProvider.Setup(obj => obj.AwsRegion).Returns("eu-west-2");
            _envVariableProvider.Setup(obj => obj.AwsAccessKey).Returns("AccessKey");
            _envVariableProvider.Setup(obj => obj.AwsSecretKey).Returns("SecretKey");
            _envVariableProvider.Setup(obj => obj.AwsSessionToken).Returns("SessionToken");

            _userContext = new Mock<UserContext>();
            _userContext.Setup(x => x.TenantId).Returns(TestHelpers.Guids.GuidOne.ToString());
            _userContext.Setup(x => x.CorrelationId).Returns(Guid.NewGuid().ToString());

            _logger = new Mock<ILogger<AccountsProductionService>>();
        }

        [Fact]
        public async Task Should_return_clientresponse()
        {
            var clientId = Guid.NewGuid();

            var expectedResponse = new List<InvolvementDto>()
            {
                new InvolvementDto
                {
                    AccountingOrder = 1,
                    Id = 1,
                    CurrentClientGuid = Guid.NewGuid()
                },
                new InvolvementDto
                {
                    AccountingOrder = 2,
                    Id = 2,
                    CurrentClientGuid = Guid.NewGuid()
                }
            };

            var responseMessage = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedResponse, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase }))
            };

            var accountsProductionClientService = new AccountsProductionService(_envVariableProvider.Object, GetMockHttpClient(responseMessage), _userContext.Object, _logger.Object);

            var result = await accountsProductionClientService.GetInvolvements(clientId);

            Assert.NotNull(result);
            expectedResponse.Count.ShouldBe(2);
            Assert.Equal(expectedResponse.First().Id, expectedResponse.First().Id);
            Assert.Equal(expectedResponse.Last().Id, expectedResponse.Last().Id);
        }


        private static HttpClient GetMockHttpClient(HttpResponseMessage responseMessage)
        {
            var mockFactory = new Mock<IHttpClientFactory>();
            var mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(responseMessage);
            var httpClient = new HttpClient(mockHttpMessageHandler.Object);
            mockFactory.Setup(_ => _.CreateClient(It.IsAny<string>())).Returns(httpClient);

            return httpClient;
        }
    }
}
