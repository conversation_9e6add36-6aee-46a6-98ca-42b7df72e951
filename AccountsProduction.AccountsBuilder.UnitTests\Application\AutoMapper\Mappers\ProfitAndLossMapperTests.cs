﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AutoMapper;
using DeepEqual.Syntax;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.AutoMapper.Mappers
{
    public class ProfitAndLossMapperTests
    {
        private readonly IMapper _mapper;

        public ProfitAndLossMapperTests()
        {
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
        }

        [Fact]
        public void Should_convert_to_profitandloss()
        {
            var finacialData = new Financial
            {
                Period = new DateTime(2021, 1, 1),
                Turnover = ReportingDomainMapperTestData.GetFinancialDataCategory("Turnover"),
                CalledUpShareCapitalNotPaid = ReportingDomainMapperTestData.GetFinancialDataCategory("CalledUpShareCapitalNotPaid"),
                DistributionExpenses = ReportingDomainMapperTestData.GetFinancialDataCategory("DistributionExpenses"),
                AccrualsAndDeferredIncome = ReportingDomainMapperTestData.GetFinancialDataCategory("AccrualsAndDeferredIncome"),
                AdministrativeExpenses = ReportingDomainMapperTestData.GetFinancialDataCategory("AdministrativeExpenses"),
                AmountsWrittenOffInvestments = ReportingDomainMapperTestData.GetFinancialDataCategory("AmountsWrittenOffInvestments"),
                CalledUpShareCapital = ReportingDomainMapperTestData.GetFinancialDataCategory("CalledUpShareCapital"),
                CapitalAccount = ReportingDomainMapperTestData.GetFinancialDataCategory("CapitalAccount"),
                CapitalAndReserves = ReportingDomainMapperTestData.GetFinancialDataCategory("CapitalAndReserves"),
                CapitalRedemptionReserve = ReportingDomainMapperTestData.GetFinancialDataCategory("CapitalRedemptionReserve"),
                CashAtBankAndInHand = ReportingDomainMapperTestData.GetFinancialDataCategory("CashAtBankAndInHand"),
                CostOfRawMaterialsAndConsumables = ReportingDomainMapperTestData.GetFinancialDataCategory("CostOfRawMaterialsAndConsumables"),
                CostOfSales = ReportingDomainMapperTestData.GetFinancialDataCategory("CostOfSales"),
                CreditorsAmountsFallingAfterMoreThanOneYear = ReportingDomainMapperTestData.GetFinancialDataCategory("CreditorsAmountsFallingAfterMoreThanOneYear"),
                CreditorsAmountsFallingDueWithinOneYear = ReportingDomainMapperTestData.GetFinancialDataCategory("CreditorsAmountsFallingDueWithinOneYear"),
                CrossCheck = ReportingDomainMapperTestData.GetFinancialDataCategory("CrossCheck"),
                CurrentAssetInvestments = ReportingDomainMapperTestData.GetFinancialDataCategory("CurrentAssetInvestments"),
                CurrentAssets = ReportingDomainMapperTestData.GetFinancialDataCategory("CurrentAssets"),
                Debtors = ReportingDomainMapperTestData.GetFinancialDataCategory("Debtors"),
                Depreciation = ReportingDomainMapperTestData.GetFinancialDataCategory("Depreciation"),
                DepreciationAndOtherAmountsWrittenOffAssets = ReportingDomainMapperTestData.GetFinancialDataCategory("DepreciationAndOtherAmountsWrittenOffAssets"),
                ExceptionalItems = ReportingDomainMapperTestData.GetFinancialDataCategory("ExceptionalItems"),
                Expenses = ReportingDomainMapperTestData.GetFinancialDataCategory("Expenses"),
                FairValueReserve = ReportingDomainMapperTestData.GetFinancialDataCategory("FairValueReserve"),
                FinanceCosts = ReportingDomainMapperTestData.GetFinancialDataCategory("FinanceCosts"),
                FixedAssetInvestments = ReportingDomainMapperTestData.GetFinancialDataCategory("FixedAssetInvestments"),
                FixedAssets = ReportingDomainMapperTestData.GetFinancialDataCategory("FixedAssets"),
                GainLossG282OnRevaluation = ReportingDomainMapperTestData.GetFinancialDataCategory("GainLossG282OnRevaluation"),
                GainLossG50OnRevaluation = ReportingDomainMapperTestData.GetFinancialDataCategory("GainLossG50OnRevaluation"),
                Goodwill = ReportingDomainMapperTestData.GetFinancialDataCategory("Goodwill"),
                GrossProfitLoss = ReportingDomainMapperTestData.GetFinancialDataCategory("GrossProfitLoss"),
                HealthcareObligations = ReportingDomainMapperTestData.GetFinancialDataCategory("HealthcareObligations"),
                IncomeFromFixedAssetInvestments = ReportingDomainMapperTestData.GetFinancialDataCategory("IncomeFromFixedAssetInvestments"),
                IncomeFromInterestInAssociatedUndertakings = ReportingDomainMapperTestData.GetFinancialDataCategory("IncomeFromInterestInAssociatedUndertakings"),
                IncomeFromOtherParticipatingInterests = ReportingDomainMapperTestData.GetFinancialDataCategory("IncomeFromOtherParticipatingInterests"),
                IncomeFromSharesInGroupUndertakings = ReportingDomainMapperTestData.GetFinancialDataCategory("IncomeFromSharesInGroupUndertakings"),
                IntangibleAssets = ReportingDomainMapperTestData.GetFinancialDataCategory("IntangibleAssets"),
                InterestPayableAndSimilarExpenses = ReportingDomainMapperTestData.GetFinancialDataCategory("InterestPayableAndSimilarExpenses"),
                InterestReceivableAndSimilarIncome = ReportingDomainMapperTestData.GetFinancialDataCategory("InterestReceivableAndSimilarIncome"),
                InvestmentProperty = ReportingDomainMapperTestData.GetFinancialDataCategory("InvestmentProperty"),
                MembersRemunerationAsExpense = ReportingDomainMapperTestData.GetFinancialDataCategory("MembersRemunerationAsExpense"),
                NetAssets = ReportingDomainMapperTestData.GetFinancialDataCategory("NetAssets"),
                NetCurrentAssetsOrLiabilities = ReportingDomainMapperTestData.GetFinancialDataCategory("OperatingProfitLoss"),
                NonControllingInterestsPL = ReportingDomainMapperTestData.GetFinancialDataCategory("NonControllingInterests"),
                OperatingProfitLoss = ReportingDomainMapperTestData.GetFinancialDataCategory(""),
                OtherCharges = ReportingDomainMapperTestData.GetFinancialDataCategory("OtherCharges"),
                OtherFinanceCosts = ReportingDomainMapperTestData.GetFinancialDataCategory("OtherFinanceCosts"),
                OtherFinanceIncome = ReportingDomainMapperTestData.GetFinancialDataCategory("OtherFinanceIncome"),
                OtherIncome = ReportingDomainMapperTestData.GetFinancialDataCategory("OtherIncome"),
                OtherOperatingIncome = ReportingDomainMapperTestData.GetFinancialDataCategory("OtherOperatingIncome"),
                OtherReserves = ReportingDomainMapperTestData.GetFinancialDataCategory("OtherReserves"),
                OtherReserves1 = ReportingDomainMapperTestData.GetFinancialDataCategory("OtherReserves1"),
                OtherReserves2 = ReportingDomainMapperTestData.GetFinancialDataCategory("OtherReserves2"),
                PartnerAppropriations = ReportingDomainMapperTestData.GetFinancialDataCategory("PartnerAppropriations"),
                PartnersCapitalAccounts = ReportingDomainMapperTestData.GetFinancialDataCategory("PartnersCapitalAccounts"),
                PartnersCurrentAccounts = ReportingDomainMapperTestData.GetFinancialDataCategory("PartnersCurrentAccounts"),
                PensionSchemeAssetsLiabilities = ReportingDomainMapperTestData.GetFinancialDataCategory("PensionSchemeAssetsLiabilities"),
                PrepaymentsAndAccruedIncome = ReportingDomainMapperTestData.GetFinancialDataCategory("PrepaymentsAndAccruedIncome"),
                ProfitLossAvailableForDiscretionaryDivision = ReportingDomainMapperTestData.GetFinancialDataCategory("ProfitLossAvailableForDiscretionaryDivision"),
                ProfitLossForTheFinancialYear = ReportingDomainMapperTestData.GetFinancialDataCategory("ProfitLossForTheFinancialYear"),
                ProfitLossOnOrdinaryActivitiesBeforeTaxation = ReportingDomainMapperTestData.GetFinancialDataCategory("ProfitLossOnOrdinaryActivitiesBeforeTaxation"),
                ProfitLossReserve = ReportingDomainMapperTestData.GetFinancialDataCategory("ProfitLossReserve"),
                ProvisionsForLiabilities = ReportingDomainMapperTestData.GetFinancialDataCategory("ProvisionsForLiabilities"),
                RevaluationReserve = ReportingDomainMapperTestData.GetFinancialDataCategory("RevaluationReserve"),
                Sales = ReportingDomainMapperTestData.GetFinancialDataCategory("Sales"),
                SharePremiumReserve = ReportingDomainMapperTestData.GetFinancialDataCategory("SharePremiumReserve"),
                StaffCosts = ReportingDomainMapperTestData.GetFinancialDataCategory("StaffCosts"),
                Stock = ReportingDomainMapperTestData.GetFinancialDataCategory("Stock"),
                TangibleFixedAssets = ReportingDomainMapperTestData.GetFinancialDataCategory(""),
                Tax = ReportingDomainMapperTestData.GetFinancialDataCategory("tax"),
                Taxation = ReportingDomainMapperTestData.GetFinancialDataCategory("Taxation"),
                TotalAssetsLessCurrentLiabilities = ReportingDomainMapperTestData.GetFinancialDataCategory("TotalAssetsLessCurrentLiabilities")
            };

            var result = _mapper.Map<Financial, ProfitAndLossMessage>(finacialData);

            result.Period.ShouldBe(finacialData.Period);
            result.Turnover.ShouldDeepEqual(finacialData.Turnover);
            result.OtherIncome.ShouldDeepEqual(finacialData.OtherIncome);
            result.CostOfRawMaterialsAndConsumables.ShouldDeepEqual(finacialData.CostOfRawMaterialsAndConsumables);
            result.StaffCosts.ShouldDeepEqual(finacialData.StaffCosts);
            result.DepreciationAndOtherAmountsWrittenOffAssets.ShouldDeepEqual(finacialData.DepreciationAndOtherAmountsWrittenOffAssets);
            result.OtherCharges.ShouldDeepEqual(finacialData.OtherCharges);
            result.Tax.ShouldDeepEqual(finacialData.Tax);
            result.Sales.ShouldDeepEqual(finacialData.Sales);
            result.CostOfSales.ShouldDeepEqual(finacialData.CostOfSales);
            result.Expenses.ShouldDeepEqual(finacialData.Expenses);
            result.FinanceCosts.ShouldDeepEqual(finacialData.FinanceCosts);
            result.PartnerAppropriations.ShouldDeepEqual(finacialData.PartnerAppropriations);
            result.Depreciation.ShouldDeepEqual(finacialData.Depreciation);
            result.GrossProfitLoss.ShouldDeepEqual(finacialData.GrossProfitLoss);
            result.DistributionExpenses.ShouldDeepEqual(finacialData.DistributionExpenses);
            result.AdministrativeExpenses.ShouldDeepEqual(finacialData.AdministrativeExpenses);
            result.OtherOperatingIncome.ShouldDeepEqual(finacialData.OtherOperatingIncome);
            result.GainLossOnRevaluation1.ShouldDeepEqual(finacialData.GainLossG50OnRevaluation);
            result.OperatingProfitLoss.ShouldDeepEqual(finacialData.OperatingProfitLoss);
            result.ExceptionalItems.ShouldDeepEqual(finacialData.ExceptionalItems);
            result.IncomeFromSharesInGroupUndertakings.ShouldDeepEqual(finacialData.IncomeFromSharesInGroupUndertakings);
            result.IncomeFromInterestInAssociatedUndertakings.ShouldDeepEqual(finacialData.IncomeFromInterestInAssociatedUndertakings);
            result.IncomeFromOtherParticipatingInterests.ShouldDeepEqual(finacialData.IncomeFromOtherParticipatingInterests);
            result.IncomeFromFixedAssetInvestments.ShouldDeepEqual(finacialData.IncomeFromFixedAssetInvestments);
            result.InterestReceivableAndSimilarIncome.ShouldDeepEqual(finacialData.InterestReceivableAndSimilarIncome);
            result.OtherFinanceIncome.ShouldDeepEqual(finacialData.OtherFinanceIncome);
            result.AmountsWrittenOffInvestments.ShouldDeepEqual(finacialData.AmountsWrittenOffInvestments);
            result.GainLossOnRevaluation2.ShouldDeepEqual(finacialData.GainLossG282OnRevaluation);
            result.InterestPayableAndSimilarExpenses.ShouldDeepEqual(finacialData.InterestPayableAndSimilarExpenses);
            result.OtherFinanceCosts.ShouldDeepEqual(finacialData.OtherFinanceCosts);
            result.ProfitLossOnOrdinaryActivitiesBeforeTaxation.ShouldDeepEqual(finacialData.ProfitLossOnOrdinaryActivitiesBeforeTaxation);
            result.Taxation.ShouldDeepEqual(finacialData.Taxation);
            result.ProfitLossForTheFinancialYear.ShouldDeepEqual(finacialData.ProfitLossForTheFinancialYear);
            result.ProfitLossAvailableForDiscretionaryDivision.ShouldDeepEqual(finacialData.ProfitLossAvailableForDiscretionaryDivision);
            result.NonControllingInterests.ShouldDeepEqual(finacialData.NonControllingInterestsPL);
            result.MembersRemunerationAsExpense.ShouldDeepEqual(finacialData.MembersRemunerationAsExpense);
        }
    }
}
