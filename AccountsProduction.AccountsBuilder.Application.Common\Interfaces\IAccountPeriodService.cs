﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.RoundingOptions;

namespace AccountsProduction.AccountsBuilder.Application.Common.Interfaces
{
    public interface IAccountPeriodService
    {
        Task<EntitySetupDto> GetEntitySetupAsync(Guid clientId, Guid accountPeriodId);
        Task<bool> IsClientOnboarded(Guid clientId);
        Task<ClientResponse> GetClientAsync(Guid clientId);
        Task<RoundingOptionsResponse> GetRoundingOptionsAsync(Guid clientId, Guid accountPeriodId);
    }
}