﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.ProfitShare;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.AggregationStrategies
{
    public class PartnershipProfitShareDataEventStrategyTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<ILogger<PartnershipProfitShareDataEventStrategy>> _logger;
        private readonly IMapper _mapper;
        private readonly Mock<UserContext> _userContext;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = TestHelpers.Guids.GuidTwo;
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;

        public PartnershipProfitShareDataEventStrategyTests()
        {
            _repository = new Mock<IAccountsBuilderRepository>();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _logger = new Mock<ILogger<PartnershipProfitShareDataEventStrategy>>();
            _userContext = new Mock<UserContext>();
            _userContext.Setup(q => q.TenantId).Returns(_tenantId.ToString());
        }

        [Fact]
        public async Task Should_update_profit_share_and_publish_to_reporting_domain()
        {
            var reportingStandardDetail = new ReportingStandard
            {
                Id = "1",
                Name = ReportStandardType.UNINCORPORATED,
                Version = ReportingStandardVersion.Full,
                Type = ReportStandardType.UNINCORPORATED
            };

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId, null, null, reportingStandardDetail));

            var strategy = new PartnershipProfitShareDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Once);
        }

        [Fact]
        public async Task Should_update_in_error_state()
        {
            var reportingStandardDetail = new ReportingStandard
            {
                Id = "1",
                Name = ReportStandardType.UNINCORPORATED,
                Version = ReportingStandardVersion.Full,
                Type = ReportStandardType.UNINCORPORATED
            };

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId, null, null, reportingStandardDetail));

            var strategy = new PartnershipProfitShareDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetFailedRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Once);
        }

        [Fact]
        public async Task Should_not_update_when_invalid_tenant_process_found()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId));
            _userContext.Setup(q => q.TenantId).Returns(TestHelpers.Guids.GuidFive.ToString());
            var strategy = new PartnershipProfitShareDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_not_update_when_no_accounts_builder_found()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(() => null);
            var strategy = new PartnershipProfitShareDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_not_update_when_invalid_tenant_accounts_builder_found()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(TestHelpers.Guids.GuidSix.ToString(), _clientId, _periodId));
            _userContext.Setup(q => q.TenantId).Returns(TestHelpers.Guids.GuidFive.ToString());
            var strategy = new PartnershipProfitShareDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_throw_on_exception()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId));
            _repository.Setup(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None)).Throws<Exception>();
            var strategy = new PartnershipProfitShareDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await Should.ThrowAsync<Exception>(async () => { await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None); });
        }

        private string GetSuccessRequestMessage()
        {
            var message = new ProfitShareDataMessage
            {
                IsSuccessful = true,
                ProfitShares = new List<ProfitShareMessage>
                {
                    new()
                    {
                        InvolvementId = 0,
                        CumulativeAmount = 10000,
                        AccountPeriodId = _periodId
                    }
                }
            };

            return JsonSerializer.Serialize(message, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
        }

        private static string GetFailedRequestMessage()
        {
            var message = new ProfitShareDataMessage { IsSuccessful = false, Error = "error" };

            return JsonSerializer.Serialize(message, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
        }
    }
}