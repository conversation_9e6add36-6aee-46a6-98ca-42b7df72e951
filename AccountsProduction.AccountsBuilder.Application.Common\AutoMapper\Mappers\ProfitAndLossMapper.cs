﻿using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers
{
    public class ProfitAndLossMapper : Profile
    {
        public ProfitAndLossMapper()
        {
            CreateMap<Financial, ProfitAndLossMessage>()
                .ForMember(d => d.GainLossOnRevaluation1, opt => opt.MapFrom(s => s.GainLossG50OnRevaluation))
                .ForMember(d => d.GainLossOnRevaluation2, opt => opt.MapFrom(s => s.GainLossG282OnRevaluation))
                .ForMember(d => d.NonControllingInterests, opt => opt.MapFrom(s => s.NonControllingInterestsPL))
                .ForMember(s => s.PeriodId, o => o.Ignore())
                .ReverseMap();
        }
    }
}
