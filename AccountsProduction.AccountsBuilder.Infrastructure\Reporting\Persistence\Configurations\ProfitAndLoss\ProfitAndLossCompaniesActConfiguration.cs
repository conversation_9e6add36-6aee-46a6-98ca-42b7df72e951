﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.ProfitAndLoss;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.ProfitAndLoss
{
    public class ProfitAndLossCompaniesActConfiguration : IEntityTypeConfiguration<ProfitAndLossCompaniesAct>
    {
        public void Configure(EntityTypeBuilder<ProfitAndLossCompaniesAct> builder)
        {
            builder.ToTable("ProfitAndLossCompaniesAct", "public");

            builder.HasKey(e => new { e.ClientId, e.AccountPeriodId })
                .HasName("profitandlosscompaniesact_pk");

            builder.Property(e => e.Turnover).HasColumnType("numeric");

            builder.Property(e => e.CostOfSales).HasColumnType("numeric");

            builder.Property(e => e.GrossProfitLoss).HasColumnType("numeric");

            builder.Property(e => e.DistributionExpenses).HasColumnType("numeric");

            builder.Property(e => e.AdministrativeExpenses).HasColumnType("numeric");

            builder.Property(e => e.OtherOperatingIncome).HasColumnType("numeric");

            builder.Property(e => e.GainLossOnRevaluation1).HasColumnType("numeric");

            builder.Property(e => e.OperatingProfitLoss).HasColumnType("numeric");

            builder.Property(e => e.ExceptionalItems).HasColumnType("numeric");

            builder.Property(e => e.IncomeFromSharesInGroupUndertakings).HasColumnType("numeric");

            builder.Property(e => e.IncomeFromInterestInAssociatedUndertakings).HasColumnType("numeric");

            builder.Property(e => e.IncomeFromOtherParticipatingInterests).HasColumnType("numeric");

            builder.Property(e => e.IncomeFromFixedAssetInvestments).HasColumnType("numeric");

            builder.Property(e => e.InterestReceivableAndSimilarIncome).HasColumnType("numeric");

            builder.Property(e => e.OtherFinanceIncome).HasColumnType("numeric");

            builder.Property(e => e.AmountsWrittenOffInvestments).HasColumnType("numeric");

            builder.Property(e => e.GainLossOnRevaluation2).HasColumnType("numeric");

            builder.Property(e => e.InterestPayableAndSimilarExpenses).HasColumnType("numeric");

            builder.Property(e => e.OtherFinanceCosts).HasColumnType("numeric");

            builder.Property(e => e.ProfitLossOnOrdinaryActivitiesBeforeTaxation).HasColumnType("numeric");

            builder.Property(e => e.Taxation).HasColumnType("numeric");

            builder.Property(e => e.ProfitLossForTheFinancialYear).HasColumnType("numeric");

            builder.Property(e => e.ProfitLossAvailableForDiscretionaryDivision).HasColumnType("numeric");

            builder.Property(e => e.NonControllingInterests).HasColumnType("numeric");

            builder.Property(e => e.MembersRemunerationAsExpense).HasColumnType("numeric");

            builder.HasOne(d => d.ReportingPeriod)
                .WithOne(p => p!.ProfitAndLossCompaniesAct!)
                .HasForeignKey<ProfitAndLossCompaniesAct>(d => new { d.ClientId, d.AccountPeriodId })
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("profitandlosscompaniesact_fk");
        }
    }
}
