﻿using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.
    PartnershipPartnersRunner;

public class PartnershipPartnerValidationRunner : ValidationRunner
{
    private const string PartnerInvolvementTpe = "Proprietor/Partner";

    protected override Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>>
        ValidationRules =>
        new()
        {
            {
                PartnershipPartnerValidations.Partner, accountsBuilder =>
                {
                    var partners =
                        accountsBuilder.InvolvementsData.Involvements.Where(rel =>
                            rel.InvolvementType == PartnerInvolvementTpe);

                    if (partners.Count() < 2)
                    {
                        return PartnershipPartnerValidations.PartnersRuleConfig.MapToValidationIssue();
                    }

                    return null;
                }
            },
            {
                PartnershipPartnerValidations.ActivePartner, accountsBuilder =>
                {
                    var partners = accountsBuilder.InvolvementsData.Involvements.Where(rel =>
                        Validator.IsInvolvementActive(rel, PartnerInvolvementTpe,
                            accountsBuilder.EntityModificationTime.Date));
                    if (partners.Count() < 2)
                    {
                        return PartnershipPartnerValidations.ActivePartnersRuleConfig.MapToValidationIssue();
                    }

                    return null;
                }
            },
            {
                PartnershipPartnerValidations.UnallocatedProfit, accountsBuilder =>
                {
                    var absUnallocatedAmount = Math.Abs(accountsBuilder.ProfitShareData.UnallocatedAmount);
                    if (absUnallocatedAmount >= 0.5m)
                    {
                        return PartnershipPartnerValidations.UnallocatedProfitLossSevereRuleConfig.MapToValidationIssue();
                    }
                    else if (absUnallocatedAmount > 0.0m)
                    {
                        return PartnershipPartnerValidations.UnallocatedProfitLossRuleConfig.MapToValidationIssue();
                    }

                    return null;
                }
            }
        };
}