﻿namespace AccountsProduction.AccountsBuilder.Domain.FinancialDataModels
{
    public interface IFRS105Financial : IBaseFinancial
    {

        public FinancialDataCategory Turnover { get; set; }

        public FinancialDataCategory OtherIncome { get; set; }

        public FinancialDataCategory CostOfRawMaterialsAndConsumables { get; set; }

        public FinancialDataCategory StaffCosts { get; set; }

        public FinancialDataCategory DepreciationAndOtherAmountsWrittenOffAssets { get; set; }

        public FinancialDataCategory OtherCharges { get; set; }

        public FinancialDataCategory Tax { get; set; }

        public FinancialDataCategory CrossCheck { get; set; }

        public FinancialDataCategory CalledUpShareCapitalNotPaid { get; set; }

        public FinancialDataCategory FixedAssets { get; set; }

        public FinancialDataCategory CurrentAssets { get; set; }

        public FinancialDataCategory PrepaymentsAndAccruedIncome { get; set; }

        public FinancialDataCategory CreditorsAmountsFallingDueWithinOneYear { get; set; }

        public FinancialDataCategory NetCurrentAssetsOrLiabilities { get; set; }

        public FinancialDataCategory TotalAssetsLessCurrentLiabilities { get; set; }

        public FinancialDataCategory CreditorsAmountsFallingAfterMoreThanOneYear { get; set; }

        public FinancialDataCategory ProvisionsForLiabilities { get; set; }

        public FinancialDataCategory AccrualsAndDeferredIncome { get; set; }

        public FinancialDataCategory NetAssets { get; set; }

        public FinancialDataCategory CapitalAndReserves { get; set; }

        public FinancialDataCategory ProfitLossAvailableForDiscretionaryDivision { get; set; }

        public FinancialDataCategory MembersRemunerationAsExpense { get; set; }

        public FinancialDataCategory LoansAndOtherDebtsDueToMembers { get; set; }

        public FinancialDataCategory MembersOtherInterests { get; set; }

        public FinancialDataCategory TotalMembersInterests { get; set; }

        public FinancialDataCategory HerdBasis { get; set; }
    }
}