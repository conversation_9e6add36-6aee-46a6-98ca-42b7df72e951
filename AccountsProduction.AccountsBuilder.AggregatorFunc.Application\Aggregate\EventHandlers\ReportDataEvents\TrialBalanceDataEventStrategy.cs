﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Application.IntegrationEvents;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents
{
    public class TrialBalanceDataEventStrategy : BaseAggregationStrategy
    {
        public override string Name => EventTypes.TrialBalanceDataEvent;

        protected readonly ILogger<TrialBalanceDataEventStrategy> _logger;
        private readonly IAccountsBuilderRepository _repository;
        protected readonly IMapper _mapper;
        protected readonly UserContext _userContext;
        protected readonly ISnsServiceClient _snsServiceClient;
        protected readonly IEnvVariableProvider _envVariableProvider;
        private readonly IDomainEventService _domainEventService;

        public TrialBalanceDataEventStrategy(IAccountsBuilderRepository repository,
            ILogger<TrialBalanceDataEventStrategy> logger, IMapper mapper, UserContext userContext,
            ISnsServiceClient snsServiceClient, IEnvVariableProvider envVariableProvider, IDomainEventService domainEventService)
        {
            _repository = repository;
            _logger = logger;
            _mapper = mapper;
            _userContext = userContext;
            _snsServiceClient = snsServiceClient;
            _envVariableProvider = envVariableProvider;
            _domainEventService = domainEventService;
        }

        public override async Task Process(string requestMessage, Guid tenantId, Guid clientId, Guid periodId, Guid processId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("START event type {eventType} for clientId {clientId}, periodId {periodId} and processId {processId} at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, processId, DateTime.UtcNow);

            var trialBalanceDto = Deserialize<TrialBalanceDto>(requestMessage);


            var accountsBuilder = await _repository.Get(clientId, periodId, cancellationToken);

            if (accountsBuilder?.EntitySetup is null)
            {
                _logger.LogWarning("No accounts builder entity found for clientId {clientId} and periodId {periodId}.", clientId, periodId);
                return;
            }

            var trialBalanceData = _mapper.Map<TrialBalance>(trialBalanceDto);

            accountsBuilder.SetProcessId(processId);
            accountsBuilder.FinancialData.ResetRetry();

            accountsBuilder.AddTrialBalance(trialBalanceData);

            await _repository.Save(accountsBuilder, cancellationToken);

            _logger.LogWarning("Updated accounts builder with new trial balance data for clientId {clientId} and periodId {periodId}.", clientId, periodId);

            var notificationTrialBalanceChanged = new NotificationTrialBalanceChanged(accountsBuilder.ClientId, accountsBuilder.PeriodId, accountsBuilder.ProcessId, accountsBuilder.EntitySetup.ReportingStandard, trialBalanceDto);
            await _domainEventService.Publish(notificationTrialBalanceChanged, cancellationToken);

            _logger.LogInformation("FINISHED event type {eventType} for clientId {clientId}, periodId {periodId} and processId {processId}  at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, processId, DateTime.UtcNow);
        }
    }
}