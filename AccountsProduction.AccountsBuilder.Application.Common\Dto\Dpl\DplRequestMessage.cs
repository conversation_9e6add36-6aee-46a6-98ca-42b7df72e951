﻿using System.Text.Json.Serialization;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.Dpl
{
    public class DplRequestMessage
    {
        [JsonPropertyName("clientId")]
        public Guid ClientId { get; set; }

        [JsonPropertyName("data")]
        public List<DplRequestDataDto> Data { get; set; } = new();

        [JsonPropertyName("roundingOptions")]
        public List<DplRequestDataRoundingOptionsDto> RoundingOptions { get; set; } = new();

        [JsonPropertyName("accountChartIdentifier")]
        public string AccountChartIdentifier { get; set; } = null!;

        [JsonPropertyName("groupStructureIdentifier")]
        public int GroupStructureIdentifier { get; set; }
    }
}
