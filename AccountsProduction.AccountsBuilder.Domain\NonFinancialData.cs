﻿using Newtonsoft.Json.Linq;

namespace AccountsProduction.AccountsBuilder.Domain
{
    public class NonFinancialData
    {
        public string CompanyName { get; set; }

        public string BusinessType { get; set; }

        public string BusinessSubType { get; set; }

        public string LimitedCompanyType { get; set; }

        public string RegisteredNo { get; set; }

        public ClientAddress ClientAddresses { get; set; }

        public bool IsDataCompleted { get; set; }

        public DateTime EntityModificationTime { get; set; }

        public void UpdateModificationTime()
        {
            EntityModificationTime = DateTime.UtcNow;
        }

        public void UpdateRecord(JObject payload)
        {
            if (payload.ContainsKey(ClientAttributiveName.Name))
            {
                CompanyName = payload.GetValue(ClientAttributiveName.Name)?.ToObject<string>();
            }

            if (payload.ContainsKey(ClientAttributiveName.LimitedCompanyType))
            {
                LimitedCompanyType = payload.GetValue(ClientAttributiveName.LimitedCompanyType)?.ToObject<string>();
            }

            if (payload.ContainsKey(ClientAttributiveName.BusinessType))
            {
                BusinessType = payload.GetValue(ClientAttributiveName.BusinessType)?.ToObject<string>();
            }

            if (payload.ContainsKey(ClientAttributiveName.BusinessSubType))
            {
                BusinessSubType = payload.GetValue(ClientAttributiveName.BusinessSubType)?.ToObject<string>();
            }

            if (payload.ContainsKey(ClientAttributiveName.RegisteredNo))
            {
                RegisteredNo = payload.GetValue(ClientAttributiveName.RegisteredNo)?.ToObject<string>();
            }

            if (payload.ContainsKey(ClientAttributiveName.MainAddress) || payload.ContainsKey(ClientAttributiveName.RegisteredAddress))
            {
                if (ClientAddresses is null)
                {
                    ClientAddresses = new ClientAddress();
                }

                ClientAddresses.UpdateRecord(payload);
            }

        }

    }


    internal static class ClientAttributiveName
    {
        public static readonly string Id = "id";
        public static readonly string Name = "name";
        public static readonly string LimitedCompanyType = "limitedCompanyType";
        public static readonly string BusinessType = "businessType";
        public static readonly string BusinessSubType = "businessSubType";
        public static readonly string RegisteredNo = "companyNumber";
        public static readonly string MainAddress = "mainAddress";
        public static readonly string RegisteredAddress = "registeredAddress";
    }
}
