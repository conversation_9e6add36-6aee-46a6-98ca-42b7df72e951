﻿namespace AccountsProduction.AccountsBuilder.Domain.NoteModels
{
    public class AdvancesCreditAndGuaranteesGrantedToDirectors
    {
        public List<AdvancesCreditAndGuaranteesGrantedToDirectorItem> Items { get; set; }
        public string Guarantees { get; set; }
    }

    public class AdvancesCreditAndGuaranteesGrantedToDirectorItem
    {
        public int Index { get; set; }
        public Guid InvolvementClientGuid { get; set; }
        public string DirectorName { get; set; }
        public decimal? BalanceOutstandingAtStartOfYear { get; set; }
        public decimal? AmountsAdvanced { get; set; }
        public decimal? AmountsRepaid { get; set; }
        public decimal? AmountsWrittenOff { get; set; }
        public decimal? AmountsWaived { get; set; }
        public decimal? BalanceOutstandingAtEndOfYear { get; set; }
        public string AdvanceCreditConditions { get; set; }
    }
}
