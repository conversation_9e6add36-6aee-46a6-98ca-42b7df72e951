﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.AutoMapper.Mappers
{
    public class BalanceSheetMapperTests
    {
        private readonly IMapper _mapper;
        public BalanceSheetMapperTests()
        {
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
        }

        [Fact]
        public void Should_convert_to_balancesheet()
        {
            var finacialData = new Financial
            {
                Period = new DateTime(2021, 1, 1),
                CalledUpShareCapitalNotPaid = new FinancialDataCategory { Value = "900" },
                FixedAssets = new FinancialDataCategory { Value = "20" },
                CurrentAssets = new FinancialDataCategory { Value = "30" },
                PrepaymentsAndAccruedIncome = new FinancialDataCategory { Value = "50" },
                CreditorsAmountsFallingDueWithinOneYear = new FinancialDataCategory { Value = "60" },
                NetCurrentAssetsOrLiabilities = new FinancialDataCategory { Value = "80" },
                TotalAssetsLessCurrentLiabilities = new FinancialDataCategory { Value = "90" },
                CreditorsAmountsFallingAfterMoreThanOneYear = new FinancialDataCategory { Value = "76" },
                ProvisionsForLiabilities = new FinancialDataCategory { Value = "0" },
                AccrualsAndDeferredIncome = new FinancialDataCategory { Value = "100" },
                NetAssets = new FinancialDataCategory { Value = "1000" },
                CapitalAndReserves = new FinancialDataCategory { Value = "350" },
                IntangibleAssets = new FinancialDataCategory { Value = "400" },
                TangibleFixedAssets = new FinancialDataCategory { Value = "450" },
                FixedAssetInvestments = new FinancialDataCategory { Value = "500" },
                InvestmentProperty = new FinancialDataCategory { Value = "550" },
                Stock = new FinancialDataCategory { Value = "600" },
                CurrentAssetInvestments = new FinancialDataCategory { Value = "650" },
                Debtors = new FinancialDataCategory { Value = "700" },
                CashAtBankAndInHand = new FinancialDataCategory { Value = "750" },
                CapitalAccount = new FinancialDataCategory { Value = "800" },
                PartnersCapitalAccounts = new FinancialDataCategory { Value = "850" },
                PartnersCurrentAccounts = new FinancialDataCategory { Value = "900" },
                OtherReserves = new FinancialDataCategory { Value = "950" },
                PensionSchemeAssetsLiabilities = new FinancialDataCategory { Value = "877" },
                HealthcareObligations = new FinancialDataCategory { Value = "543" },
                CalledUpShareCapital = new FinancialDataCategory { Value = "6453" },
                SharePremiumReserve = new FinancialDataCategory { Value = "654" },
                RevaluationReserve = new FinancialDataCategory { Value = "654" },
                CapitalRedemptionReserve = new FinancialDataCategory { Value = "87" },
                OtherReserves1 = new FinancialDataCategory { Value = "654" },
                OtherReserves2 = new FinancialDataCategory { Value = "321" },
                FairValueReserve = new FinancialDataCategory { Value = "654" },
                ProfitLossReserve = new FinancialDataCategory { Value = "900" },
                Goodwill = new FinancialDataCategory { Value = "700" },
                NonControllingInterestsBS = new FinancialDataCategory { Value = "111" },
                TotalMembersInterests = new FinancialDataCategory { Value = "112" },
                MembersOtherInterests = new FinancialDataCategory { Value = "113" },
                MembersCapital = new FinancialDataCategory { Value = "114" },
                OtherDebtsDueToMembers = new FinancialDataCategory { Value = "115" },
                LoansAndOtherDebtsDueToMembers = new FinancialDataCategory { Value = "116" },
                HerdBasis = new FinancialDataCategory { Value = "117" }
            };

            var result = _mapper.Map<Financial, BalanceSheetMessage>(finacialData);

            result.Period.ShouldBe(finacialData.Period);
            result.CalledUpShareCapitalNotPaid.Value.ShouldBe(finacialData.CalledUpShareCapitalNotPaid.Value);
            result.FixedAssets.Value.ShouldBe(finacialData.FixedAssets.Value);
            result.CurrentAssets.Value.ShouldBe(finacialData.CurrentAssets.Value);
            result.PrepaymentsAndAccruedIncome.Value.ShouldBe(finacialData.PrepaymentsAndAccruedIncome.Value);
            result.CreditorsAmountsFallingDueWithinOneYear.Value.ShouldBe(finacialData.CreditorsAmountsFallingDueWithinOneYear.Value);
            result.NetCurrentAssetsOrLiabilities.Value.ShouldBe(finacialData.NetCurrentAssetsOrLiabilities.Value);
            result.TotalAssetsLessCurrentLiabilities.Value.ShouldBe(finacialData.TotalAssetsLessCurrentLiabilities.Value);
            result.CreditorsAmountsFallingAfterMoreThanOneYear.Value.ShouldBe(finacialData.CreditorsAmountsFallingAfterMoreThanOneYear.Value);
            result.ProvisionsForLiabilities.Value.ShouldBe(finacialData.ProvisionsForLiabilities.Value);
            result.AccrualsAndDeferredIncome.Value.ShouldBe(finacialData.AccrualsAndDeferredIncome.Value);
            result.NetAssets.Value.ShouldBe(finacialData.NetAssets.Value);
            result.CapitalAndReserves.Value.ShouldBe(finacialData.CapitalAndReserves.Value);
            result.IntangibleAssets.Value.ShouldBe(finacialData.IntangibleAssets.Value);
            result.TangibleFixedAssets.Value.ShouldBe(finacialData.TangibleFixedAssets.Value);
            result.FixedAssetInvestments.Value.ShouldBe(finacialData.FixedAssetInvestments.Value);
            result.InvestmentProperty.Value.ShouldBe(finacialData.InvestmentProperty.Value);
            result.Stock.Value.ShouldBe(finacialData.Stock.Value);
            result.CurrentAssetInvestments.Value.ShouldBe(finacialData.CurrentAssetInvestments.Value);
            result.Debtors.Value.ShouldBe(finacialData.Debtors.Value);
            result.CashAtBankAndInHand.Value.ShouldBe(finacialData.CashAtBankAndInHand.Value);
            result.CapitalAccount.Value.ShouldBe(finacialData.CapitalAccount.Value);
            result.PartnersCapitalAccounts.Value.ShouldBe(finacialData.PartnersCapitalAccounts.Value);
            result.PartnersCurrentAccounts.Value.ShouldBe(finacialData.PartnersCurrentAccounts.Value);
            result.OtherReserves.Value.ShouldBe(finacialData.OtherReserves.Value);
            result.PensionSchemeAssetsLiabilities.Value.ShouldBe(finacialData.PensionSchemeAssetsLiabilities.Value);
            result.HealthcareObligatons.Value.ShouldBe(finacialData.HealthcareObligations.Value);
            result.CalledUpShareCapital.Value.ShouldBe(finacialData.CalledUpShareCapital.Value);
            result.SharePremiumReserve.Value.ShouldBe(finacialData.SharePremiumReserve.Value);
            result.RevaluationReserve.Value.ShouldBe(finacialData.RevaluationReserve.Value);
            result.CapitalRedemptionReserve.Value.ShouldBe(finacialData.CapitalRedemptionReserve.Value);
            result.OtherReserves2.Value.ShouldBe(finacialData.OtherReserves2.Value);
            result.FairValueReserve.Value.ShouldBe(finacialData.FairValueReserve.Value);
            result.ProfitAndLossReserve.Value.ShouldBe(finacialData.ProfitLossReserve.Value);
            result.Goodwill.Value.ShouldBe(finacialData.Goodwill.Value);
            result.NonControllingInterests.Value.ShouldBe(finacialData.NonControllingInterestsBS.Value);
            result.TotalMembersInterests.Value.ShouldBe(finacialData.TotalMembersInterests.Value);
            result.MembersOtherInterests.Value.ShouldBe(finacialData.MembersOtherInterests.Value);
            result.MembersCapital.Value.ShouldBe(finacialData.MembersCapital.Value);
            result.OtherDebtsDueToMembers.Value.ShouldBe(finacialData.OtherDebtsDueToMembers.Value);
            result.LoansAndOtherDebtsDueToMembers.Value.ShouldBe(finacialData.LoansAndOtherDebtsDueToMembers.Value);
            result.HerdBasis.Value.ShouldBe(finacialData.HerdBasis.Value);
        }
    }
}