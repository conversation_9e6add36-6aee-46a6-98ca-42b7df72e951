﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.AccountPeriod;
using Iris.AccountsProduction.AccountsBuilder.Messages.DataScreenValue;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain
{
    public class BaseReportingDomainMapper
    {
        private readonly IMapper _mapper;

        public BaseReportingDomainMapper(IMapper mapper)
        {
            _mapper = mapper;
        }

        protected List<AccountsBuilder.Application.Common.Dto.ReportingPeriodDto> MapReportingPeriods(List<ReportingPeriod> reportingPeriods)
        {
            return _mapper.Map<List<ReportingPeriod>, List<AccountsBuilder.Application.Common.Dto.ReportingPeriodDto>>(reportingPeriods);
        }

        protected ReportingSignatureDto MapReportingSignatory(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var reportingSignatureDto = _mapper.Map<SignatureDetail, ReportingSignatureDto>(accountsBuilder.Signatory?.Signature);

            if (reportingSignatureDto is not null)
            {
                foreach (var signature in reportingSignatureDto.Signatures)
                {
                    var nonFinancialDataDto = accountsBuilder.InvolvementsData?.Involvements?.FirstOrDefault(c => c.InvolvementClientGuid == signature.InvolvementUUID);

                    if (nonFinancialDataDto != null)
                    {
                        signature.SignatoryTitle = nonFinancialDataDto.InvolvementTitle;
                        signature.SignatoryFirstName = nonFinancialDataDto.InvolvementFirstName;
                        signature.SignatorySurname = nonFinancialDataDto.InvolvementSurname;
                        signature.InvolvementUUID = nonFinancialDataDto.InvolvementClientGuid;
                    }
                }

            }

            return reportingSignatureDto;
        }

        protected (List<ProfitAndLossMessage> ProfitAndLossData, List<BalanceSheetMessage> BalanceSheetData, List<OtherMessage> OtherData) MapFinancialData(List<Financial> financials, List<ReportingPeriod> reportingPeriods)
        {
            var profitAndLossData = new List<ProfitAndLossMessage>();
            var balanceSheetData = new List<BalanceSheetMessage>();
            var otherData = new List<OtherMessage>();

            foreach (var financial in financials)
            {
                var profitAndLossItem = _mapper.Map<ProfitAndLossMessage>(financial);
                var balanceSheetItem = _mapper.Map<BalanceSheetMessage>(financial);
                var otherItem = _mapper.Map<OtherMessage>(financial);

                var period = reportingPeriods.FirstOrDefault(tb => tb.EndDate == financial.Period);

                if (period != null)
                {
                    profitAndLossItem.PeriodId = period.Id;
                    balanceSheetItem.PeriodId = period.Id;
                    balanceSheetItem.PeriodId = period.Id;
                }

                profitAndLossData.Add(profitAndLossItem);
                balanceSheetData.Add(balanceSheetItem);
                otherData.Add(otherItem);
            }
            return (profitAndLossData, balanceSheetData, otherData);
        }

        protected void AssignSharedProperties(
            ref FRS1021AAndFRS102SharedReportingMessage baseReportingMessage,
            Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder,
            List<ProfitAndLossMessage> profitAndLossData,
            List<BalanceSheetMessage> balanceSheetData,
            List<OtherMessage> otherData)
        {
            baseReportingMessage.Notes = _mapper.Map<NotesResponseDataMessage>(accountsBuilder.Notes);
            baseReportingMessage.DataScreenValue = _mapper.Map<DataScreenValueMessage>(accountsBuilder.DataScreenValue);
            baseReportingMessage.PracticeDetails = _mapper.Map<AccountsBuilder.Application.Common.Dto.PracticeDetailsDto>(accountsBuilder.PracticeDetails);
            baseReportingMessage.EntitySetup = _mapper.Map<AccountsBuilder.Application.Common.Dto.EntitySetup.EntitySetupDto>(accountsBuilder.EntitySetup);
            baseReportingMessage.ReportVersion = accountsBuilder.ReportingStandard?.Version;
            baseReportingMessage.ProfitShareData = _mapper.Map<ProfitShareDataDto>(accountsBuilder.ProfitShareData);
            baseReportingMessage.AccountPeriod = _mapper.Map<AccountPeriodMessage>(accountsBuilder.AccountPeriod);
            baseReportingMessage.NoteAccountingPolicies = _mapper.Map<AccountingPoliciesResponseDataMessage>(accountsBuilder.AccountingPolicies);
            baseReportingMessage.ClientId = accountsBuilder.ClientId;
            baseReportingMessage.PeriodId = accountsBuilder.PeriodId;
            baseReportingMessage.TenantId = accountsBuilder.TenantId;
            baseReportingMessage.WatermarkText = accountsBuilder.LicenseData.WatermarkText;
            baseReportingMessage.ProfitAndLossData = profitAndLossData;
            baseReportingMessage.Signatures = MapReportingSignatory(accountsBuilder);
            baseReportingMessage.ClientData = _mapper.Map<NonFinancialData, ClientDocumentMessageDto>(accountsBuilder.NonFinancialData);
            baseReportingMessage.Involvements = _mapper.Map<List<ClientInvolvementDto>>(accountsBuilder.InvolvementsData?.Involvements);
            baseReportingMessage.ReportingPeriods = MapReportingPeriods(accountsBuilder.TrialBalance?.ReportingPeriods ?? []);
            baseReportingMessage.BalanceSheetData = balanceSheetData;
            baseReportingMessage.FinancialData = _mapper.Map<AccountsBuilder.Application.Common.Dto.FinancialData.FinancialDataDto>(accountsBuilder.FinancialData);
            baseReportingMessage.OtherData = otherData;
            baseReportingMessage.TrialBalanceData = accountsBuilder.TrialBalance!.TrialBalances;
        }
    }
}
