﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.PracticeDetails;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.AutoMapper.Mappers
{
    public class PracticeDetailsMapperTests
    {
        private readonly IMapper _mapper;

        public PracticeDetailsMapperTests()
        {
            var mapperConfig = new MapperConfiguration(cfg => { cfg.AddProfile<PracticeDetailsMapper>(); });

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();
        }

        [Fact]
        public void Should_map_domainPracticeDetails_to_practiceDetailsDto()
        {
            var practiceDetails = new PracticeDetails
            {
                ReferredType = 1,
                SupervisingBody = 2,
                Name = "name",
                AddressLine1 = "AddressLine1",
                AddressLine2 = "AddressLine2",
                AddressLine3 = "AddressLine3",
                AddressTown = "AddressTown",
                AddressCounty = "AddressCounty",
                AddressPostcode = "AddressPostcode",
                AddressCountry = "AddressCountry"
            };

            var result = _mapper.Map<PracticeDetailsDto>(practiceDetails);

            result.ReferredType.ShouldBe(practiceDetails.ReferredType);
            result.SupervisingBody.ShouldBe(practiceDetails.SupervisingBody);
            result.Name.ShouldBe(practiceDetails.Name);
            result.AddressLine1.ShouldBe(practiceDetails.AddressLine1);
            result.AddressLine2.ShouldBe(practiceDetails.AddressLine2);
            result.AddressLine3.ShouldBe(practiceDetails.AddressLine3);
            result.AddressTown.ShouldBe(practiceDetails.AddressTown);
            result.AddressCounty.ShouldBe(practiceDetails.AddressCounty);
            result.AddressPostcode.ShouldBe(practiceDetails.AddressPostcode);
            result.AddressCountry.ShouldBe(practiceDetails.AddressCountry);
        }

        [Fact]
        public void Should_map_from_practicedetails_message_to_practicedetails()
        {
            var practiceDetails = new PracticeDetailMessage
            {
                ActingNames = new ActingNameMessage
                {
                    ReferredType = Referred.Singular,
                    SupervisoryBody = SupervisoryBody.CAI
                },
                Name = "name",
                Address = new AddressMessage
                {
                    Address1 = "AddressLine1",
                    Address2 = "AddressLine2",
                    Address3 = "AddressLine3",
                    City = "AddressTown",
                    County = "AddressCounty",
                    Postcode = "AddressPostcode",
                    Country = "AddressCountry"
                }
            };

            var result = _mapper.Map<PracticeDetails>(practiceDetails);

            result.ReferredType.ShouldBe(1);
            result.SupervisingBody.ShouldBe(4);
            result.Name.ShouldBe(practiceDetails.Name);
            result.AddressLine1.ShouldBe(practiceDetails.Address.Address1);
            result.AddressLine2.ShouldBe(practiceDetails.Address.Address2);
            result.AddressLine3.ShouldBe(practiceDetails.Address.Address3);
            result.AddressTown.ShouldBe(practiceDetails.Address.City);
            result.AddressCounty.ShouldBe(practiceDetails.Address.County);
            result.AddressPostcode.ShouldBe(practiceDetails.Address.Postcode);
            result.AddressCountry.ShouldBe(practiceDetails.Address.Country);
        }
    }
}
