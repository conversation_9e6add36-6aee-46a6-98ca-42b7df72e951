﻿using AccountsProduction.AccountsBuilder.Reporting.Application;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence;
using AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Services;
using Amazon.SimpleNotificationService;
using Iris.AccountsProduction.Common.Toolkit.ExtensionMethods;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure
{
    [ExcludeFromCodeCoverage]
    public static class DependencyInjection
    {
        public static IServiceCollection AddReportingInfrastructure(this IServiceCollection services,
            IConfiguration configuration)
        {
            services.AddScoped(provider =>
            {
                var optionsBuilder = new DbContextOptionsBuilder<AccountsProductionReportingDbContext>();
                optionsBuilder.UseNpgsql(DbConnectionStringFactory.ConnectionString(configuration),
                    pgOptions =>
                    {
                        pgOptions.EnableRetryOnFailure();
                        pgOptions.ProvidePasswordCallback(DbConnectionStringFactory.PasswordCallback(configuration));
                    });

#if DEBUG
                optionsBuilder.EnableSensitiveDataLogging().EnableDetailedErrors();
#endif

                return new AccountsProductionReportingDbContext(optionsBuilder.Options);
            });

            services.AddScoped<IAccountsProductionReportingDbContext>(provider => provider.GetService<AccountsProductionReportingDbContext>()!);

            services.AddFeatureServiceClient();

            var awsOptions = configuration.GetAWSOptions();
            services.AddDefaultAWSOptions(awsOptions);
            services.AddAWSService<IAmazonSimpleNotificationService>();

            services.AddTransient<IConfigurationSettings, ConfigurationSettings>();
            services.AddScoped<UserContext>();

            return services;
        }
    }
}
