﻿using System.Text.Json.Serialization;

namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.NonFinancialData
{
    public class NonFinancialDataRequestMessage
    {
        [JsonPropertyName("clientIdentifier")]
        public Guid ClientIdentifier { get; set; }

        [JsonPropertyName("clientId")]
        public Guid ClientId { get; set; }

        [JsonPropertyName("periodId")]
        public Guid PeriodId { get; set; }
    }
}
