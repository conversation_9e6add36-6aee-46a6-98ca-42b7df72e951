﻿using AccountsProduction.AccountsBuilder.Domain.Reporting.Other;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using System.Reflection;

namespace AccountsProduction.AccountsBuilder.Application.Reporting.AutoMapper.Mappers
{
    public class DplSummaryCalcsMapper : Profile
    {
        public DplSummaryCalcsMapper()
        {
            CreateMap<List<OtherMessage>, List<DplSummaryCalcs>>()
                .ConvertUsing<OtherMessageListToListOfDplSummaryCalcsConverters>();
        }

        private sealed class OtherMessageListToListOfDplSummaryCalcsConverters : ITypeConverter<List<OtherMessage>, List<DplSummaryCalcs>>
        {
            public List<DplSummaryCalcs> Convert(List<OtherMessage> source, List<DplSummaryCalcs> destination,
                ResolutionContext context)
            {
                return source.Extract();
            }
        }
    }

    public static class DplSummaryCalcsExtractor
    {
        public static List<DplSummaryCalcs> Extract<T>(this List<T> source)
        {
            if (!source.IsPopulated())
            {
                return new List<DplSummaryCalcs>();
            }

            var firstItem = source[0];

            var financialDataCategoryProperties = firstItem?.GetType().GetProperties()
                .Where(s => s.PropertyType == typeof(FinancialDataCategoryMessage))!.ToList();

            var periodIdPropertyInfo = firstItem?.GetType().GetProperties().Single(s => s.Name == "PeriodId");

            return BuildResult(source, financialDataCategoryProperties, periodIdPropertyInfo);
        }

        private static List<DplSummaryCalcs> BuildResult<T>(List<T> source, List<PropertyInfo>? financialDataCategoryProperties, PropertyInfo? periodIdPropertyInfo)
        {
            var result = new List<DplSummaryCalcs>();

            foreach (var item in source)
            {
                Guid periodIdValue;
                var periodIdValueObject = periodIdPropertyInfo?.GetValue(item, null);
                if (periodIdValueObject is Guid guidValue)
                {
                    periodIdValue = guidValue;
                }
                else
                {
                    throw new InvalidOperationException($"Expected PeriodId to be of type Guid, but got {periodIdValueObject?.GetType().Name}");
                }

                result.AddRange(BuildDplSummaryCalcsFromItem(item, financialDataCategoryProperties, periodIdValue));
            }

            return result;
        }

        private static List<DplSummaryCalcs> BuildDplSummaryCalcsFromItem<T>(T item, List<PropertyInfo>? financialDataCategoryProperties, Guid periodIdValue)
        {
            // Ensure financialDataCategoryProperties is not null before iterating
            if (financialDataCategoryProperties == null)
            {
                return new List<DplSummaryCalcs>();
            }

            var dplSummaryCalcs = financialDataCategoryProperties
                .Select(property => item?.GetType().GetProperty(property.Name))
                .Where(categoryPropertyInfo => categoryPropertyInfo != null)
                .Select(categoryPropertyInfo => new
                {
                    propertyName = categoryPropertyInfo?.Name,
                    propertyValue = categoryPropertyInfo?.GetValue(item, null) as FinancialDataCategoryMessage,
                })
                .Where(data => data.propertyValue != null && data.propertyValue.DrilldownData.IsPopulated())
                .Select(data => new DplSummaryCalcs
                {
                    AccountPeriodId = periodIdValue,
                    DPLCalcType = data.propertyName,
                    DPLValue = Convert.ToDecimal(data.propertyValue.Value)
                })
                .ToList();

            return dplSummaryCalcs;
        }
    }
}
