﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AutoMapper;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.ReportingDomain.Charity;

public class CharityReportingDomainMapper : BaseReportingDomainMapper, IMap
{
    public string ReportStandardType => AccountsBuilder.Application.Common.ReportStandardType.CHARITY;

    public CharityReportingDomainMapper(UserContext userContext, IMapper mapper) : base(mapper)
    {
    }

    public BaseReportingMessage Map(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
    {
        var (profitAndLossData, balanceSheetData, otherData) = MapFinancialData(accountsBuilder.FinancialData?.Financials ?? new(), accountsBuilder.TrialBalance?.ReportingPeriods ?? new());

        var requestMessage = new FRS1021AAndFRS102SharedReportingMessage
        {
            ReportType = AccountsBuilder.Application.Common.ReportStandardType.CHARITY
        };

        AssignSharedProperties(ref requestMessage, accountsBuilder, profitAndLossData, balanceSheetData, otherData);

        return requestMessage;
    }
}