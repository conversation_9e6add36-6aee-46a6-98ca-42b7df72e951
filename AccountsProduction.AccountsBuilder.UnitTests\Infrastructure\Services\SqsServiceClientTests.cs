﻿using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Infrastructure.Services;
using Amazon.SQS;
using Amazon.SQS.Model;
using Iris.AccountsProduction.Common.Toolkit.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Infrastructure.Services
{
    public class SqsServiceClientTests
    {
        private readonly Mock<IAmazonSQS> _awsClient;
        private readonly ISqsServiceClient _sqsServiceMoq;

        private Dictionary<string, string> MessageAttributes =>
            new Dictionary<string, string> { { "clientId", Guid.NewGuid().ToString() }, { "periodId", Guid.NewGuid().ToString() } };

        public SqsServiceClientTests()
        {
            _awsClient = new Mock<IAmazonSQS>();
            var logger = Mock.Of<ILogger<SqsServiceClient>>();
            _sqsServiceMoq = new SqsServiceClient(_awsClient.Object, logger);
        }

        [Fact]
        public async Task Should_throw_exception_for_empty_queue_url()
        {
            await Should.ThrowAsync<ArgumentNullException>(async () =>
            {
                await _sqsServiceMoq.SendMessage("", "test message", CancellationToken.None);
            });

            await Should.ThrowAsync<ArgumentNullException>(async () =>
            {
                await _sqsServiceMoq.SendMessage("", "test message", null, CancellationToken.None);
            });

            await Should.ThrowAsync<ArgumentNullException>(async () =>
            {
                await _sqsServiceMoq.SendMessage("", "test message", null, 60, CancellationToken.None);
            });
        }

        [Fact]
        public async Task Should_send_message()
        {
            _awsClient.Setup(x => x.SendMessageAsync(It.IsAny<SendMessageRequest>(), CancellationToken.None))
                .ReturnsAsync(new SendMessageResponse());

            await _sqsServiceMoq.SendMessage("queueUrl", "test message", CancellationToken.None);

            _awsClient.Verify(sqs => sqs.SendMessageAsync(It.Is<SendMessageRequest>(request =>
                request.QueueUrl == "queueUrl" &&
                request.MessageAttributes.Count == 0 &&
                request.DelaySeconds == 0), CancellationToken.None), Times.Once);
        }

        [Fact]
        public async Task Should_send_message_with_attributes()
        {
            _awsClient.Setup(x => x.SendMessageAsync(It.IsAny<SendMessageRequest>(), CancellationToken.None))
                .ReturnsAsync(new SendMessageResponse());

            var clientId = Guid.NewGuid().ToString();
            var periodId = Guid.NewGuid().ToString();
            var messageAttributes = new Dictionary<string, string> { { "clientId", clientId }, { "periodId", periodId } };
            await _sqsServiceMoq.SendMessage("queueUrl", "test message", messageAttributes, CancellationToken.None);

            _awsClient.Verify(sqs => sqs.SendMessageAsync(It.Is<SendMessageRequest>(request =>
                request.QueueUrl == "queueUrl" &&
                request.MessageAttributes.ContainsKey("clientId") &&
                request.MessageAttributes.ContainsKey("periodId") &&
                request.MessageAttributes["clientId"].StringValue == clientId &&
                request.MessageAttributes["periodId"].StringValue == periodId &&
                request.DelaySeconds == 0), CancellationToken.None), Times.Once);
        }

        [Fact]
        public async Task Should_send_message_with_delay()
        {
            _awsClient.Setup(x => x.SendMessageAsync(It.IsAny<SendMessageRequest>(), CancellationToken.None))
                .ReturnsAsync(new SendMessageResponse());

            var clientId = Guid.NewGuid().ToString();
            var periodId = Guid.NewGuid().ToString();
            var messageAttributes = new Dictionary<string, string> { { "clientId", clientId }, { "periodId", periodId } };

            await _sqsServiceMoq.SendMessage("queueUrl", "test message delay", messageAttributes, 60, CancellationToken.None);

            _awsClient.Verify(sqs => sqs.SendMessageAsync(It.Is<SendMessageRequest>(request =>
                request.QueueUrl == "queueUrl" &&
                request.MessageAttributes.ContainsKey("clientId") &&
                request.MessageAttributes.ContainsKey("periodId") &&
                request.MessageAttributes["clientId"].StringValue == clientId &&
                request.MessageAttributes["periodId"].StringValue == periodId &&
                request.DelaySeconds == 60), CancellationToken.None), Times.Once);
        }

        [Fact]
        public async Task Should_throw_sqs_custom_exception()
        {
            _awsClient.Setup(x => x.SendMessageAsync(It.IsAny<SendMessageRequest>(), CancellationToken.None))
                .Throws(new Exception("sqs exception"));

            await Should.ThrowAsync<ExternalException>(async () =>
            {
                await _sqsServiceMoq.SendMessage("queueUrl", "test message",
                    CancellationToken.None);
            });

            await Should.ThrowAsync<ExternalException>(async () =>
            {
                await _sqsServiceMoq.SendMessage("queueUrl", "test message",
                    MessageAttributes, CancellationToken.None);
            });

            await Should.ThrowAsync<ExternalException>(async () =>
            {
                await _sqsServiceMoq.SendMessage("queueUrl", "test message",
                    MessageAttributes, 60, CancellationToken.None);
            });
        }
    }
}
