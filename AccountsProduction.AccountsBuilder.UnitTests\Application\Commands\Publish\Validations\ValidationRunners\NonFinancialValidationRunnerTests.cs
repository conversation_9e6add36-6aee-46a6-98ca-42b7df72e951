﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NonFinancialRunner;
using AccountsProduction.AccountsBuilder.Domain;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations.ValidationRunners
{
    public class NonFinancialValidationRunnerTests
    {
        public class When_validating_full_object : NonFinancialValidationRunnerTests
        {
            [Fact]
            public void Should_return_validation_issues_non_financial_data()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    NonFinancialData = new NonFinancialData(),
                    ClientId = Guid.NewGuid(),
                    TenantId = Guid.NewGuid()
                };

                var nonFinancialValidationRunner = new NonFinancialValidationRunner();

                var issues = nonFinancialValidationRunner.Validate(accountsBuilder);

                issues.Count.ShouldBe(5);
                issues[0].Name.ShouldBe(NonFinancialValidations.BusinessName);
                issues[1].Name.ShouldBe(NonFinancialValidations.BusinessType);
                issues[2].Name.ShouldBe(NonFinancialValidations.AddressLineOne);
                issues[3].Name.ShouldBe(NonFinancialValidations.CityTown);
                issues[4].Name.ShouldBe(NonFinancialValidations.CompanyNumber);
            }

            [Fact]
            public void Should_not_return_validate_non_financial_data()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    NonFinancialData = new NonFinancialData
                    {
                        CompanyName = "Name",
                        RegisteredNo = "123456",
                        BusinessType = "Limited",
                        ClientAddresses = new ClientAddress
                        {
                            RegisteredAddress = new Address
                            {
                                Line1 = "address line 1",
                                PostCode = "123456",
                                Town = "Town"
                            }
                        }
                    },
                    ClientId = Guid.NewGuid(),
                    TenantId = Guid.NewGuid()
                };

                var nonFinancialValidationRunner = new NonFinancialValidationRunner();

                var issues = nonFinancialValidationRunner.Validate(accountsBuilder);

                issues.ShouldBeEmpty();
            }
        }

        public class When_validating_business_type : NonFinancialValidationRunnerTests
        {
            [Fact]
            public void Should_return_validation_issue()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    NonFinancialData = new NonFinancialData
                    {
                        CompanyName = "Name",
                        RegisteredNo = "123456",
                        ClientAddresses = new ClientAddress
                        {
                            RegisteredAddress = new Address
                            {
                                Line1 = "address line 1",
                                PostCode = "123456",
                                Town = "Town"
                            }
                        }
                    }
                };

                var nonFinancialValidationRunner = new NonFinancialValidationRunner();

                var issues = nonFinancialValidationRunner.Validate(accountsBuilder);

                issues.Count.ShouldBe(1);
                issues[0].ErrorCode.ShouldBe(ValidationCodes.BusinessType);
                issues[0].Name.ShouldBe(NonFinancialValidations.BusinessType);
                issues[0].Type.ShouldBe(ValidationRuleType.Missing);
                issues[0].Target.ShouldBe(Target.IssueLog);
                issues[0].ErrorCategory.ShouldBe(ErrorCategoryType.Mandatory);
                issues[0].DisplayName.ShouldBe("Business Type");
                issues[0].Breadcrumb.ShouldBe("Client management > Information tab");
                issues[0].Description.ShouldBe("A Company Type has not been entered.");
            }
        }

        public class When_validating_company_number : NonFinancialValidationRunnerTests
        {
            [Fact]
            public void Should_return_validation_issue()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    NonFinancialData = new NonFinancialData
                    {
                        BusinessType = "llp",
                        CompanyName = "123456",
                        ClientAddresses = new ClientAddress
                        {
                            RegisteredAddress = new Address
                            {
                                Line1 = "address line 1",
                                PostCode = "123456",
                                Town = "Town"
                            }
                        }
                    }
                };

                var nonFinancialValidationRunner = new NonFinancialValidationRunner();

                var issues = nonFinancialValidationRunner.Validate(accountsBuilder);

                issues.Count.ShouldBe(1);
                issues[0].ErrorCode.ShouldBe(ValidationCodes.CompanyNumber);
                issues[0].Name.ShouldBe(NonFinancialValidations.CompanyNumber);
                issues[0].DisplayName.ShouldBe("Company number");
                issues[0].Breadcrumb.ShouldBe("Client management > Information tab");
                issues[0].Description
                    .ShouldBe(
                        "A valid registered number has not been entered. Companies House requires a valid registered number in the valid format (usually 8 characters).");
                issues[0].Type.ShouldBe(ValidationRuleType.Missing);
                issues[0].Target.ShouldBe(Target.IssueLog);
                issues[0].ErrorCategory.ShouldBe(ErrorCategoryType.Mandatory);
            }

            [Theory]
            [InlineData(BusinessTypes.PartnershipBusinessType)]
            [InlineData(BusinessTypes.SoleTraderBusinessType)]
            public void Should_not_return_validation_issue_for_partnership_soletrader(string businessType)
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    NonFinancialData = new NonFinancialData
                    {
                        BusinessType = businessType,
                        CompanyName = "123456",
                        ClientAddresses = new ClientAddress
                        {
                            MainAddress = new Address
                            {
                                Line1 = "address line 1",
                                PostCode = "123456",
                                Town = "Town"
                            }
                        }
                    }
                };

                var baseNonFinancialValidationRunner = new BaseNonFinancialValidationRunner();

                var issues = baseNonFinancialValidationRunner.Validate(accountsBuilder);

                issues.Count.ShouldBe(0);
            }

            [Theory]
            [InlineData(BusinessTypes.PartnershipBusinessType)]
            [InlineData(BusinessTypes.SoleTraderBusinessType)]
            public void Should_return_validation_issue_for_partnership_soletrader_when_address_missing(string businessType)
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    NonFinancialData = new NonFinancialData
                    {
                        BusinessType = businessType,
                        CompanyName = "123456"
                    }
                };

                var baseNonFinancialValidationRunner = new BaseNonFinancialValidationRunner();

                var issues = baseNonFinancialValidationRunner.Validate(accountsBuilder);

                issues.Count.ShouldBe(2);
            }
        }

        public class When_validating_business_name : NonFinancialValidationRunnerTests
        {
            [Fact]
            public void Should_return_validation_issue()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    NonFinancialData = new NonFinancialData
                    {
                        BusinessType = "llp",
                        RegisteredNo = "123456",
                        ClientAddresses = new ClientAddress
                        {
                            RegisteredAddress = new Address
                            {
                                Line1 = "address line 1",
                                PostCode = "123456",
                                Town = "Town"
                            }
                        }
                    }
                };

                var nonFinancialValidationRunner = new NonFinancialValidationRunner();

                var issues = nonFinancialValidationRunner.Validate(accountsBuilder);

                issues.Count.ShouldBe(1);
                issues[0].ErrorCode.ShouldBe(ValidationCodes.BusinessName);
                issues[0].Name.ShouldBe(NonFinancialValidations.BusinessName);
                issues[0].DisplayName.ShouldBe("Business name");
                issues[0].Breadcrumb.ShouldBe("Client management > Information tab");
                issues[0].Description.ShouldBe("A Company Name has not been entered.");
                issues[0].Type.ShouldBe(ValidationRuleType.Missing);
                issues[0].Target.ShouldBe(Target.IssueLog);
                issues[0].ErrorCategory.ShouldBe(ErrorCategoryType.Mandatory);
            }
        }

        public class When_validating_address_line_one : NonFinancialValidationRunnerTests
        {
            [Fact]
            public void Should_return_validation_issue()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    NonFinancialData = new NonFinancialData
                    {
                        BusinessType = "llp",
                        CompanyName = "test",
                        RegisteredNo = "123456",
                        ClientAddresses = new ClientAddress
                        {
                            RegisteredAddress = new Address
                            {
                                PostCode = "123456",
                                Town = "Town"
                            }
                        }
                    }
                };

                var nonFinancialValidationRunner = new NonFinancialValidationRunner();

                var issues = nonFinancialValidationRunner.Validate(accountsBuilder);

                issues.Count.ShouldBe(1);
                issues[0].ErrorCode.ShouldBe(ValidationCodes.AddressLine1);
                issues[0].Name.ShouldBe(NonFinancialValidations.AddressLineOne);
                issues[0].DisplayName.ShouldBe("Address line 1");
                issues[0].Breadcrumb.ShouldBe("Client management > Information tab");
                issues[0].Description
                    .ShouldBe("A valid registered office address has not been entered.");
                issues[0].Type.ShouldBe(ValidationRuleType.Missing);
                issues[0].Target.ShouldBe(Target.IssueLog);
                issues[0].ErrorCategory.ShouldBe(ErrorCategoryType.Mandatory);
            }
        }

        public class When_validating_city : NonFinancialValidationRunnerTests
        {
            [Fact]
            public void Should_return_validation_issue()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    NonFinancialData = new NonFinancialData
                    {
                        BusinessType = "llp",
                        CompanyName = "Test",
                        RegisteredNo = "123456",
                        ClientAddresses = new ClientAddress
                        {
                            RegisteredAddress = new Address
                            {
                                Line1 = "address line 1",
                                PostCode = "123456",
                            }
                        }
                    }
                };

                var nonFinancialValidationRunner = new NonFinancialValidationRunner();

                var issues = nonFinancialValidationRunner.Validate(accountsBuilder);

                issues.Count.ShouldBe(1);
                issues[0].ErrorCode.ShouldBe(ValidationCodes.CityTown);
                issues[0].Name.ShouldBe(NonFinancialValidations.CityTown);
                issues[0].DisplayName.ShouldBe("City/Town");
                issues[0].Breadcrumb.ShouldBe("Client management > Information tab");
                issues[0].Description.ShouldBe("A valid city/town has not been entered.");
                issues[0].Type.ShouldBe(ValidationRuleType.Missing);
                issues[0].Target.ShouldBe(Target.IssueLog);
                issues[0].ErrorCategory.ShouldBe(ErrorCategoryType.Mandatory);
            }
        }
    }
}