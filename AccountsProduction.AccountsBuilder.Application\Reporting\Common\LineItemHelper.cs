﻿using AccountsProduction.AccountsBuilder.Reporting.Application.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Domain;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.Common;

public static class LineItemHelper
{
    public static List<LineItem> MergeItems(List<LineItem> lineItems, List<PeriodDto> periods)
    {
        if (periods.Count == 0)
        {
            return new List<LineItem>();
        }
        var finalResult = new List<LineItem>();
        var currentPeriodId = periods.MaxBy(x => x.EndDate)!.Id;
        var previousPeriodId = periods.MinBy(x => x.EndDate)!.Id;

        foreach (var lineItem in lineItems)
        {
            var found = lineItems.Where(HasMatchFor(lineItem)).ToList();
            if (found.Count == 2)
            {
                if (!finalResult.Any(HasMatchFor(lineItem)))
                {
                    var itemToBeAdded = found.First(x => x.AccountPeriodId == currentPeriodId);
                    itemToBeAdded.PreviousValue = found.FirstOrDefault(x => x.AccountPeriodId == previousPeriodId)?.CurrentValue ?? 0;
                    finalResult.Add(itemToBeAdded);
                }
                continue;
            }

            if (found.Count == 1)
            {
                if (found.First().AccountPeriodId == currentPeriodId)
                {
                    finalResult.Add(lineItem);
                    continue;
                }

                if (found.First().AccountPeriodId == previousPeriodId)
                {
                    lineItem.PreviousValue = found.First().CurrentValue;
                    lineItem.CurrentValue = 0;
                    lineItem.AccountPeriodId = currentPeriodId;
                    finalResult.Add(lineItem);
                }
            }
        }

        return finalResult;
    }

    private static Func<LineItem, bool> HasMatchFor(LineItem lineItem)
    {
        return x => x.Category == lineItem.Category &&
                    x.AccountCode == lineItem.AccountCode &&
                    x.SubAccountCode == lineItem.SubAccountCode &&
                    x.AccountDescription == lineItem.AccountDescription &&
                    x.InvolvementId == lineItem.InvolvementId &&
                    x.SectorId == lineItem.SectorId;
    }
}
