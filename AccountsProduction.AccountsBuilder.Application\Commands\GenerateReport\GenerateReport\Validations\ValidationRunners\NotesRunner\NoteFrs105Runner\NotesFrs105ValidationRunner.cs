﻿using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NotesRunner.NoteFrs105Runner
{
    public class NotesFrs105ValidationRunner : NotesValidationRunner
    {
        private const int Group631 = 631;
        private const int Group641 = 641;
        private readonly IGroupAccountSubAccountIntervalRepository _repository;

        public NotesFrs105ValidationRunner(IGroupAccountSubAccountIntervalRepository repository) : base(ReportStandardType.FRS105)
        {
            _repository = repository;
        }

        protected override Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>> ValidationRules
        {
            get
            {
                var validationRules = base.ValidationRules;

                validationRules.Add(NotesFrs105Validations.AdvancesCreditGuarantees, ValidateAdvancesCreditGuarantees);

                return validationRules;
            }
        }

        private ValidationIssue ValidateAdvancesCreditGuarantees(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var accountRanges =
                _repository.GetCachedGroupAccountSubAccountIntervalList().Where(g =>
                    g.GroupNo.In(Group631, Group641));

            var hasAccounts = HasAccountsInSearchedGroups(accountsBuilder, accountRanges.ToList());

            if (!hasAccounts) return null;

            var advancesCreditGuarantees =
                accountsBuilder.Notes?.AdvancesCreditAndGuaranteesGrantedToDirectors;
            return Validator.ValidateForNull(advancesCreditGuarantees, NotesFrs105Validations
                .AdvancesCreditGuaranteesConfig);
        }
    }
}