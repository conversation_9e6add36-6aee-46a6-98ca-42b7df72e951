﻿namespace AccountsProduction.AccountsBuilder.Application.Common.Interfaces
{
    public interface ISqsServiceClient
    {
        Task SendMessage(string queueUrl, string message, CancellationToken cancellationToken);

        Task SendMessage(string queueUrl, string message, Dictionary<string, string> messageAttributes,
            CancellationToken cancellationToken);

        Task SendMessage(string queueUrl, string message,
            Dictionary<string, string> messageAttributes, int delaySeconds, CancellationToken cancellationToken);
    }
}
