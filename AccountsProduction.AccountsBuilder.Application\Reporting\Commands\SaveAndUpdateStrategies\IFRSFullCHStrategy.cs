using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies
{
    public class IFRSFullCHStrategy : IFRSBaseStrategy
    {
        public IFRSFullCHStrategy(
            IMediator mediator,
            ILogger<IFRSFullCHStrategy> logger,
            IAccountsProductionReportingDbContext accountsProductionReportingDbContext,
            IMapper mapper) : base(mediator, logger, accountsProductionReportingDbContext, mapper)
        {
        }

        public override string ReportTypeName => ReportType.IFRS_FULL_CH;

        public override async Task SendSaveAndUpdateDataEvents(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
        {
            await SendIFRSBaseSaveAndUpdateDataEvents(data, ReportType.IFRS_FULL_CH, cancellationToken);
        }
    }
}
