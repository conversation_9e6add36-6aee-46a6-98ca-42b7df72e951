﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Domain;
using Amazon.DynamoDBv2.Model;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json.Linq;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.AggregationStrategies
{
    public class NonFinancialDataEventStrategyTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<ILogger<NonFinancialDataEventStrategy>> _logger;
        private readonly IMapper _mapper;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = Guid.NewGuid();
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;

        public NonFinancialDataEventStrategyTests()
        {
            var mapperConfig = new MapperConfiguration(cfg =>
                {
                    cfg.AddProfile<NonFinancialDataMapper>();
                    cfg.AddProfile<AddressMapper>();
                    cfg.AddProfile<ClientAddressMapper>();
                }
            );

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();

            _repository = new Mock<IAccountsBuilderRepository>();
            _logger = new Mock<ILogger<NonFinancialDataEventStrategy>>();
        }

        [Fact]
        public async Task Should_update_accounts_production_data()
        {
            _repository.Setup(repository => repository.GetAll(_clientId, CancellationToken.None))
                .ReturnsAsync(new List<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(){
                    new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId,
                    _periodId, null)
                });

            var requestMessage = GetRequestMessage();
            var strategy = new NonFinancialDataEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(
                repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Once);

        }

        [Fact]
        public async Task Should_not_update_accounts_production_data_if_concurreny_occures()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId);
            _repository.Setup(repository => repository.GetAll(_clientId, CancellationToken.None))
                .ReturnsAsync(new List<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(){
                    accountsBuilder
                });

            _repository.Setup(repository => repository.Get(It.IsAny<Guid>(), It.IsAny<Guid>(), CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            _repository.Setup(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None))
                .Callback(() => throw new ConditionalCheckFailedException("test"));

            var requestMessage = GetRequestMessage();
            var strategy = new NonFinancialDataEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(
                repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Exactly(4));

        }
        private string GetRequestMessage()
        {
            var message = JObject.FromObject(new
            {
                name = Guid.NewGuid().ToString(),
                limitedCompanyType = "shared",
                businessType = "limited",
                businessSubType = "business sub type",
                RegisteredNo = "123",
                mainAddress = JObject.FromObject(new
                {
                    line1 = "line1",
                    line2 = "line2",
                    town = "town",
                    postcode = "postcode",
                    county = "county"
                }),
                RegisteredAddress = JObject.FromObject(new
                {
                    line1 = "line1",
                    line2 = "line2",
                    town = "town",
                    postcode = "postcode",
                    county = "county"
                }),

            }).ToString(Newtonsoft.Json.Formatting.None);

            return message;
        }
    }
}
