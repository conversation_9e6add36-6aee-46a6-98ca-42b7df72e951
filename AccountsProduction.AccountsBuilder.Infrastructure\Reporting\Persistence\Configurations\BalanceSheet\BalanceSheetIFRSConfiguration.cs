﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.BalanceSheet
{
    public class BalanceSheetIFRSConfiguration : IEntityTypeConfiguration<BalanceSheetIFRS>
    {
        public void Configure(EntityTypeBuilder<BalanceSheetIFRS> builder)
        {
            builder.ToTable("BalanceSheetIFRS", "public");

            builder.HasKey(e => new { e.ClientId, e.AccountPeriodId })
                .HasName("balancesheetifrs_pk");

            builder.HasOne(d => d.BalanceSheetGeneral)
                   .WithOne(p => p.BalanceSheetIFRS)
                   .HasForeignKey<BalanceSheetIFRS>(d => new { d.ClientId, d.AccountPeriodId })
                   .OnDelete(DeleteBehavior.Cascade)
                   .HasConstraintName("balancesheetifrs_fk");
        }
    }
}
