﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Address;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.PracticeDetails;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Report;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using Iris.AccountsProduction.Common.Toolkit.Exceptions;
using Iris.Platform.Eventbus.Client.Dotnet.Client;
using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.EventBusClient;
using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.Message;
using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.Message.Attributes;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;

namespace AccountsProduction.AccountsBuilder.Infrastructure.Services
{
    public class GenerateReportDataService : IGenerateReportDataService
    {
        private readonly ILogger<GenerateReportDataService> _logger;
        private readonly IAccountPeriodService _accountPeriodService;
        private readonly UserContext _userContext;
        private readonly IAccountsProductionService _accountsProductionService;
        private readonly IEventBusClient _eventBusClient;
        private readonly IClientDataService _clientDataService;
        private readonly ITrialBalanceService _trialBalanceService;

        public GenerateReportDataService(
            UserContext userContext,
            ILogger<GenerateReportDataService> logger,
            IAccountPeriodService accountPeriodService,
            IClientDataService clientDataService,
            IAccountsProductionService accountsProductionService,
            IEventBusClient eventBusClient,
            ITrialBalanceService trialBalanceService)
        {
            _logger = logger;
            _accountPeriodService = accountPeriodService;
            _userContext = userContext;
            _accountsProductionService = accountsProductionService;
            _eventBusClient = eventBusClient;
            _clientDataService = clientDataService;
            _trialBalanceService = trialBalanceService;
        }

        public async Task<GenerateReportDto> GetReportData(Guid clientId, Guid periodId)
        {
            var entitySetupDto = await GetEntitySetup(clientId, periodId);

            var practiceDetailsTask = GetPracticeDetails(entitySetupDto?.PracticeAddress);
            var clientTask = GetClient(clientId);
            var clientAddressTask = GetClientAddresses(clientId);
            var involvementsTask = GetInvolvements(clientId);
            var trialBalanceTask = GetTrialBalance(clientId, periodId);

            await Task.WhenAll(practiceDetailsTask, trialBalanceTask, involvementsTask, clientTask, clientAddressTask);

            var practiceDetailsDto = await practiceDetailsTask;
            var involvementDtos = await involvementsTask;
            var client = await clientTask;
            var processedClientAddresses = await clientAddressTask;
            var tialBalanceDto = await trialBalanceTask;

            var getGenerateReportDto = new GenerateReportDto
            {
                EntitySetupDto = entitySetupDto,
                InvolvementDtos = involvementDtos,
                PracticeDetailsDto = practiceDetailsDto,
                ClientResponse = client,
                ClientAddressDto = processedClientAddresses,
                TrialBalanceDto = tialBalanceDto
            };

            return getGenerateReportDto;
        }

        public async Task<PracticeDetailMessage?> GetPracticeDetails(string? practiceDetailId)
        {
            PublishWithResponse<List<PracticeDetailMessage>> response;
            var requestPayload = new EventBusMessage<string>
            {
                Payload = _userContext.TenantId.ToString(),
                TenantId = new TenantId(_userContext.TenantId.ToString()),
                Topic = new Topic("practice:details:getall")
            };
            _logger.LogInformation("Start _eventBusClient.PublishWithResponse at {dateTime}", DateTime.UtcNow);
            response = await _eventBusClient.PublishWithResponse<string, List<PracticeDetailMessage>>(requestPayload, 40000);
            _logger.LogInformation("End _eventBusClient.PublishWithResponse at {dateTime}", DateTime.UtcNow);
            if (response.Status != HttpStatusCode.OK || !string.IsNullOrEmpty(response.Error))
            {
                _logger.LogWarning("Error during receiving practice details with statusCode: {status} and message error: {error}.", response.Status, response.Error);
                return null;
            }

            if (!string.IsNullOrEmpty(practiceDetailId) && response.Payload.Any(x => x.PracticeDetailId == Guid.Parse(practiceDetailId)))
            {
                return response.Payload.FirstOrDefault(x => x.PracticeDetailId == Guid.Parse(practiceDetailId));
            }
            else
            {
                return response.Payload.FirstOrDefault(x => x.IsPrimaryPracticeOffice);
            }
        }

        private async Task<EntitySetupDto?> GetEntitySetup(Guid clientId, Guid periodId)
        {
            _logger.LogInformation("Start _accountPeriodService.GetEntitySetupAsync at {dateTime}", DateTime.UtcNow);
            var entitySetupDto = await _accountPeriodService.GetEntitySetupAsync(clientId, periodId);
            _logger.LogInformation("End _accountPeriodService.GetEntitySetupAsync at {dateTime}", DateTime.UtcNow);
            return entitySetupDto;
        }

        private async Task<TrialBalanceDto> GetTrialBalance(Guid clientId, Guid periodId)
        {
            _logger.LogInformation("Start _trialBalanceService.GetTrialBalanceCalculate at {dateTime}", DateTime.UtcNow);
            var trialBalanceDto = await _trialBalanceService.GetTrialBalanceCalculate(clientId, periodId);
            _logger.LogInformation("End _trialBalanceService.GetTrialBalanceCalculate at {dateTime}", DateTime.UtcNow);
            return trialBalanceDto;
        }
        private async Task<List<InvolvementDto>> GetInvolvements(Guid clientId)
        {
            _logger.LogInformation("End _accountPeriodService.GetInvolvements at {dateTime}", DateTime.UtcNow);
            var involvementDtos = (await _accountsProductionService.GetInvolvements(clientId)).ToList();
            _logger.LogInformation("End _accountPeriodService.GetInvolvements at {dateTime}", DateTime.UtcNow);
            return involvementDtos;
        }

        private async Task<ClientAddressDto> GetClientAddresses(Guid clientIdentifier)
        {
            _logger.LogInformation("End _accountPeriodService.GetClientAddress at {dateTime}", DateTime.UtcNow);
            var addressResponses = await _clientDataService.GetClientAddress(clientIdentifier);
            _logger.LogInformation("End _accountPeriodService.GetClientAddress at {dateTime}", DateTime.UtcNow);
            var processedClientAddresses = ProcessClientAddresses(addressResponses);
            return processedClientAddresses;
        }

        private async Task<ClientResponse> GetClient(Guid clientIdentifier)
        {
            _logger.LogInformation("End _accountPeriodService.GetClientAsync at {dateTime}", DateTime.UtcNow);
            var clientResponse = await _accountPeriodService.GetClientAsync(clientIdentifier);
            _logger.LogInformation("End _accountPeriodService.GetClientAsync at {dateTime}", DateTime.UtcNow);

            if (clientResponse == null)
                throw new BadRequestException("Client not found");

            return clientResponse;
        }

        private ClientAddressDto ProcessClientAddresses(List<AddressResponseDto> clientAddresses)
        {
            var clientAddressesToSave = new ClientAddressDto();

            if (clientAddresses == null)
            {
                return clientAddressesToSave;
            }

            foreach (var address in clientAddresses)
            {
                if (address.Context?.AddressContext == null || address.Document?.Document == null)
                {
                    continue;
                }

                var deserializedAddressContext = JsonSerializer.Deserialize<AddressContextDto>(address.Context.AddressContext)!;

                if (!deserializedAddressContext.Prime && !deserializedAddressContext.Registered)
                {
                    continue;
                }

                var deserializedAddressDocument = JsonSerializer.Deserialize<AddressDocumentDto>(address.Document.Document);
                var clientAddress = new AddressDto
                {
                    PostCode = deserializedAddressDocument?.Postcode,
                    County = deserializedAddressDocument?.County,
                    Line1 = ExtractAddressLine(deserializedAddressDocument?.Line, 0),
                    Line2 = ExtractAddressLine(deserializedAddressDocument?.Line, 1),
                    Town = deserializedAddressDocument?.Town
                };
                PopulateMainAddress(clientAddressesToSave, deserializedAddressContext, clientAddress);
                PopulateRegisteredAddress(clientAddressesToSave, deserializedAddressContext, clientAddress);
            }

            return clientAddressesToSave;
        }

        private static string? ExtractAddressLine(List<string>? lines, int lineNumber)
        {
            if (lines != null && lines.Count > lineNumber)
            {
                return lines[lineNumber];
            }

            return null;
        }

        private static void PopulateRegisteredAddress(ClientAddressDto clientAddressesToSave, AddressContextDto deserializedAddressContext, AddressDto clientAddress)
        {
            if (deserializedAddressContext.Registered)
            {
                clientAddressesToSave.RegisteredAddress = clientAddress;
            }
        }

        private static void PopulateMainAddress(ClientAddressDto clientAddressesToSave, AddressContextDto deserializedAddressContext, AddressDto clientAddress)
        {
            if (deserializedAddressContext.Prime)
            {
                clientAddressesToSave.MainAddress = clientAddress;
            }
        }



    }
}
