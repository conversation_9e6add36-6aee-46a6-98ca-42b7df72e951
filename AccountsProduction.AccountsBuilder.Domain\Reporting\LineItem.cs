﻿namespace AccountsProduction.AccountsBuilder.Reporting.Domain
{
    public class LineItem
    {
        public int Id { get; set; }

        public Guid ClientId { get; set; }

        public Guid AccountPeriodId { get; set; }

        public string? Category { get; set; }

        public string? AccountCode { get; set; }

        public string? AccountDescription { get; set; }

        public decimal CurrentValue { get; set; }

        public decimal PreviousValue { get; set; }

        public int DisplayOrder { get; set; }

        public string? SubAccountCode { get; set; }

        public int? InvolvementId { get; set; }
        public Guid? SectorId { get; set; }

        public virtual ReportingPeriod? ReportingPeriod { get; set; }
    }
}
