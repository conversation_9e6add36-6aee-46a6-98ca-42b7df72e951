﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet.Contract;

namespace AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet
{
    public class BalanceSheetFRS105 : IBalanceSheetBase
    {

        public decimal CalledUpShareCapitalNotPaid { get; set; }

        public decimal FixedAssets { get; set; }

        public decimal CurrentAssets { get; set; }

        public decimal PrepaymentsAndAccruedIncome { get; set; }

        public decimal CreditorsAmountsFallingDueWithinOneYear { get; set; }

        public decimal NetCurrentAssetsOrLiabilities { get; set; }

        public decimal TotalAssetsLessCurrentLiabilities { get; set; }

        public decimal CreditorsAmountsFallingAfterMoreThanOneYear { get; set; }

        public decimal ProvisionsForLiabilities { get; set; }

        public decimal AccrualsAndDeferredIncome { get; set; }

        public decimal NetAssets { get; set; }

        public decimal CapitalAndReserves { get; set; }

        public virtual ReportingPeriod? ReportingPeriod { get; set; }
        public Guid ClientId { get; set; }
        public Guid AccountPeriodId { get; set; }
    }
}
