﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Signatory;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents
{
    public class SignatoriesDataEventStrategy : BaseAggregationStrategy
    {
        public override string Name => EventTypes.SignatoriesDataEvent;

        private readonly IAccountsBuilderRepository _repository;
        private readonly ILogger<SignatoriesDataEventStrategy> _logger;
        private readonly IMapper _mapper;

        public SignatoriesDataEventStrategy(IAccountsBuilderRepository repository,
            ILogger<SignatoriesDataEventStrategy> logger, IMapper mapper)
        {
            _repository = repository;
            _logger = logger;
            _mapper = mapper;
        }

        public override async Task Process(string requestMessage, Guid tenantId, Guid clientId, Guid periodId, Guid processId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("START event type {eventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, DateTime.UtcNow);

            var signatoryDto = Deserialize<SignatoryResponseDto>(requestMessage);

            var accountsBuilder = await _repository.Get(clientId, periodId, cancellationToken);

            if (accountsBuilder == null)
            {
                _logger.LogWarning("No accounts builder entity found for clientId {clientId} and periodId {periodId}.", clientId, periodId);
                return;
            }

            var signatoryData = _mapper.Map<Domain.Signatory>(signatoryDto);

            accountsBuilder.AddSignatory(signatoryData);

            await _repository.Save(accountsBuilder, cancellationToken);

            _logger.LogInformation($"Updated accounts builder with new signatory data for client id {clientId} and period id {periodId}.");

            _logger.LogInformation("FINISHED event type {eventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, DateTime.UtcNow);
        }
    }
}