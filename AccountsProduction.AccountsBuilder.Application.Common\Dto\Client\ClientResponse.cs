﻿namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.Client
{
    public class ClientResponse
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public string? BusinessType { get; set; }
        public string? BusinessSubType { get; set; }
        public string? LimitedCompanyType { get; set; }
        public string? RegisteredNo { get; set; }
    }
}
