﻿using Iris.AccountsProduction.Common.Toolkit.Utils;
using Iris.Platform.WebApi.Infrastructure.Middleware;

namespace AccountsProduction.AccountsBuilder.Application.Common.Helper
{
    public static class LicenseHelper
    {
        // Enterprise License code - this may need to be adjusted based on actual license codes
        private const string EnterpriseLicenseCode = "AP_ENTERPRISE";

        public static bool IsAccountsProdTrialLicense(UserContext userContext)
        {
            if (userContext?.Licenses == null) return false;

            return userContext.Licenses.Any(x => x.Code == APLicense.Name && x.IsTrial);
        }

        public static bool HasEnterpriseLicense(UserContext userContext)
        {
            if (userContext?.Licenses == null) return false;

            // Check for Enterprise License or if licensing toggle is off (for development/testing)
            return userContext.Licenses.Any(x => x.Code == EnterpriseLicenseCode && !x.IsTrial) ||
                   userContext.Licenses.Any(x => x.Code == APLicense.Name && !x.IsTrial); // Fallback for now
        }

        public static bool IsIFRSEligible(UserContext userContext, bool isIFRSEnabled)
        {
            // Check all conditions for IFRS eligibility:
            // 1. Has Enterprise License OR licensing toggle is off
            // 2. AccountPeriod.IFRSEnabled is true
            // 3. IFRS was selected on entity setup
            return (HasEnterpriseLicense(userContext) || !IsLicensingToggleOn()) && isIFRSEnabled;
        }

        private static bool IsLicensingToggleOn()
        {
            // This would typically check a feature toggle or configuration
            // For now, return true to enforce licensing checks
            // This can be made configurable via environment variables or feature toggles
            return true;
        }
    }
}
