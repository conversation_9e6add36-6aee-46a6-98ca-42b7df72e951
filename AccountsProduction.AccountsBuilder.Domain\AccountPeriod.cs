﻿namespace AccountsProduction.AccountsBuilder.Domain
{
    public class AccountPeriod
    {
        public Guid ClientId { get; set; }
        public Guid PeriodId { get; set; }
        public string? ReviseType { get; set; }

        public DateTime EntityModificationTime { get; set; }

        public void UpdateModificationTime()
        {
            EntityModificationTime = DateTime.UtcNow;
        }
    }
}
