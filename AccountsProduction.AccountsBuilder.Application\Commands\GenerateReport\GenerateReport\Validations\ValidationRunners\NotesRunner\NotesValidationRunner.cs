﻿using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NotesRunner
{
    public class NotesValidationRunner : ValidationRunner
    {

        private readonly string _reportingType;

        public NotesValidationRunner(string reportingType)
        {
            _reportingType = reportingType;
        }

        protected override Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>> ValidationRules
        {
            get
            {
                return new Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>>
                {
                    {
                        NotesValidations.AverageNumberOfEmployeesCurrentPeriod,
                        accountsBuilder =>
                        {
                            var averageNumberOfEmployees =
                                accountsBuilder?.Notes?.AverageNumberOfEmployees?.CurrentPeriod?.ToString();
                            return Validator.ValidateForNull(averageNumberOfEmployees, NotesValidations
                                .AverageNumberOfEmployeesCurrentPeriodConfig(_reportingType));
                        }
                    },
                    {
                        NotesValidations.AverageNumberOfEmployeesPreviousPeriod,
                        accountsBuilder =>
                        {
                            var averageNumberOfEmployees =
                                accountsBuilder?.Notes?.AverageNumberOfEmployees?.PreviousPeriod?.ToString();
                            return Validator.ValidateForNull(averageNumberOfEmployees, NotesValidations
                                .AverageNumberOfEmployeesPreviousPeriodConfig(_reportingType));
                        }
                    }
                };
            }
        }
    }
}