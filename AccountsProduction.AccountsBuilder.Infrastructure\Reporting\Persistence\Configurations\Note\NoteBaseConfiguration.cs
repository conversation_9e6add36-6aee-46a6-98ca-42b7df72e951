﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.Note.Contract;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.Note
{
    public abstract class NoteConfigurationBase<TEntity> : IEntityTypeConfiguration<TEntity> where TEntity : NoteBase
    {
        public virtual void Configure(EntityTypeBuilder<TEntity> builder)
        {
            builder.Property(e => e.Id).ValueGeneratedOnAdd();

            builder.Property(e => e.NoteType).HasColumnType("character varying");

            builder.Property(e => e.NoteText).HasColumnType("character varying");

            builder.Property(e => e.NoteValue).IsRequired(false).HasColumnType("numeric");

            builder.Property(e => e.ScreenId).HasColumnType("character varying").IsRequired(false);
        }
    }
}
