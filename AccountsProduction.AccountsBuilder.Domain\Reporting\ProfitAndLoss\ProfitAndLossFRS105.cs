﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.ProfitAndLoss.Contract;

namespace AccountsProduction.AccountsBuilder.Reporting.Domain.ProfitAndLoss
{
    public class ProfitAndLossFRS105 : ProfitAndLossBase
    {

        public decimal Turnover { get; set; }

        public decimal OtherIncome { get; set; }

        public decimal CostOfRawMaterialsAndConsumables { get; set; }

        public decimal StaffCosts { get; set; }

        public decimal DepreciationAndOtherAmountsWrittenOffAssets { get; set; }

        public decimal OtherCharges { get; set; }

        public decimal Tax { get; set; }

        public decimal ProfitLossAvailableForDiscretionaryDivision { get; set; }

        public decimal MembersRemunerationAsExpense { get; set; }
    }
}
