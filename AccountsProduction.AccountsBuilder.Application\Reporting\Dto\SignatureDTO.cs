﻿using AccountsProduction.AccountsBuilder.Reporting.Application.Common;


namespace AccountsProduction.AccountsBuilder.Reporting.Application.Dto
{
    public class SignatureDto
    {
        public List<SignatureDetailDto> Signatures { get; set; } = new();
        public DateTime? AccountantSigningDate { get; set; }
        public bool? IncludeAccountantsReport { get; set; }
        public string? AccountantSigningName { get; set; }
    }
    public class SignatureDetailDto
    {
        public string? SignatoryTitle { get; set; }
        public string? SignatoryFirstName { get; set; }
        public string? SignatorySurname { get; set; }
        public DateTime? SignatureDate { get; set; }
        public int DisplayOrder { get; set; }
        public SignatureType SignatureType { get; set; }
        public Guid InvolvementUUID { get; set; }
        public string? InvolvementType { get; set; }
    }
}
