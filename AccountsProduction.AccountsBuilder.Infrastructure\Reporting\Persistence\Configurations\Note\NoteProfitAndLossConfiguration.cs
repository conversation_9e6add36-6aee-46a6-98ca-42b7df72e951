﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.Note
{
    public class NoteProfitAndLossConfiguration : NoteConfigurationBase<NoteProfitAndLoss>
    {
        public override void Configure(EntityTypeBuilder<NoteProfitAndLoss> builder)
        {
            builder.ToTable("NoteProfitAndLoss", "public");

            builder.HasOne(d => d.ReportingPeriod)
                .WithMany(p => p!.NoteProfitAndLoss)
                .HasForeignKey(d => new { d.ClientId, d.AccountPeriodId })
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("noteprofitandloss_fk");

            base.Configure(builder);
        }
    }
}
