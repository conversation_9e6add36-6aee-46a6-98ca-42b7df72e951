using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.TangibleFixedAssetsRunner
{
    public static class TangibleFixedAssetsValidations
    {
        public const string TangibleFixedAssets = "TangibleFixedAssets";
        public const string TangibleFixedAssetsLandAndBuildings = "LandAndBuildings";
        public const string TangibleFixedAssetsPlantAndMachineries = "PlantAndMachineries";
        public const string TangibleFixedAssetsFreeholdProperty = "FreeholdProperty";
        public const string TangibleFixedAssetsShortLeaseholdProperty = "ShortLeaseholdProperty";
        public const string TangibleFixedAssetsLongLeaseholdProperty = "LongLeaseholdProperty";
        public const string TangibleFixedAssetsImprovementsToProperty = "ImprovementsToProperty";
        public const string TangibleFixedAssetsPlantAndMachinery = "PlantAndMachinery";
        public const string TangibleFixedAssetsFixturesAndFittings = "Fixtures & Fittings";
        public const string TangibleFixedAssetsMotorVehicles = "Motor Vehicles";
        public const string TangibleFixedAssetsComputerEquipment = "Computer Equipment";

        public static readonly ValidationRuleConfig TangibleFixedAssetsConfig =
            new ValidationRuleConfig
            {
                Breadcrumb = TangibleFixedAssetsBreadcrumbs.TangibleFixedAssetsRule.ToString(),
                Description = "Tangible fixed assets group should be hidden.",
                Name = TangibleFixedAssets,
                Type = ValidationRuleType.Missing,
                Target = Target.SectionValidation,
                ErrorCategory = ErrorCategoryType.Advisory,
                DisplayName = "Tangible fixed assets",
                ErrorCode = ValidationCodes.TangibleFixedAssets
            };

        public static readonly ValidationRuleConfig LandAndBuildingsConfig = new ValidationRuleConfig
        {
            Breadcrumb = TangibleFixedAssetsBreadcrumbs.LandAndBuildingsRule.ToString(),
            Description = "Tangible fixed assets - land and buildings group should be hidden.",
            Name = TangibleFixedAssetsLandAndBuildings,
            Type = ValidationRuleType.Missing,
            Target = Target.SectionValidation,
            ErrorCategory = ErrorCategoryType.Advisory,
            DisplayName = "Land and Buildings",
            ErrorCode = ValidationCodes.TangibleFixedAssetsLandAndBuildings
        };

        public static readonly ValidationRuleConfig PlantAndMachineriesConfig = new ValidationRuleConfig
        {
            Breadcrumb = TangibleFixedAssetsBreadcrumbs.PlantAndMachineriesRule.ToString(),
            Description = "Tangible fixed assets - plant and machinery group should be hidden.",
            Name = TangibleFixedAssetsPlantAndMachineries,
            Type = ValidationRuleType.Missing,
            Target = Target.SectionValidation,
            ErrorCategory = ErrorCategoryType.Advisory,
            DisplayName = "Plant and Machinery",
            ErrorCode = ValidationCodes.TangibleFixedAssetsPlantAndMachineries
        };

        public static readonly ValidationRuleConfig FreeholdPropertyConfig =
            new ValidationRuleConfig
            {
                Breadcrumb = TangibleFixedAssetsBreadcrumbs.FreeholdPropertyRule.ToString(),
                Description = "Freehold property account codes have been populated in the Trial balance. An accounting policy is therefore required to be entered.",
                Name = TangibleFixedAssetsFreeholdProperty,
                Type = ValidationRuleType.Missing,
                Target = Target.IssueLog,
                ErrorCategory = ErrorCategoryType.Mandatory,
                DisplayName = "Freehold property accounting policy",
                ErrorCode = ValidationCodes.FreeholdProperty
            };

        public static readonly ValidationRuleConfig ShortLeaseholdPropertyConfig =
            new ValidationRuleConfig
            {
                Breadcrumb = TangibleFixedAssetsBreadcrumbs.ShortLeaseholdPropertyRule.ToString(),
                Description = "Short leasehold property account codes have been populated in the Trial balance. An accounting policy is therefore required to be entered.",
                Name = TangibleFixedAssetsShortLeaseholdProperty,
                Type = ValidationRuleType.Missing,
                Target = Target.IssueLog,
                ErrorCategory = ErrorCategoryType.Mandatory,
                DisplayName = "Short leasehold property accounting policy",
                ErrorCode = ValidationCodes.ShortLeaseholdProperty
            };

        public static readonly ValidationRuleConfig LongLeaseholdPropertyConfig =
            new ValidationRuleConfig
            {
                Breadcrumb = TangibleFixedAssetsBreadcrumbs.LongLeaseholdPropertyRule.ToString(),
                Description = "Long leasehold property account codes have been populated in the Trial balance. An accounting policy is therefore required to be entered.",
                Name = TangibleFixedAssetsLongLeaseholdProperty,
                Type = ValidationRuleType.Missing,
                Target = Target.IssueLog,
                ErrorCategory = ErrorCategoryType.Mandatory,
                DisplayName = "Long leasehold property accounting policy",
                ErrorCode = ValidationCodes.LongLeaseholdProperty
            };

        public static readonly ValidationRuleConfig TangibleFixedAssetsImprovementsToPropertyConfig =
            new ValidationRuleConfig
            {
                Breadcrumb = TangibleFixedAssetsBreadcrumbs.ImprovementsToPropertyRule.ToString(),
                Description = "Improvements to property account codes have been populated in the Trial balance. An accounting policy is therefore required to be entered.",
                Name = TangibleFixedAssetsImprovementsToProperty,
                Type = ValidationRuleType.Missing,
                Target = Target.IssueLog,
                ErrorCategory = ErrorCategoryType.Mandatory,
                DisplayName = "Improvements to property accounting policy",
                ErrorCode = ValidationCodes.ImprovementsToProperty
            };

        public static readonly ValidationRuleConfig TangibleFixedAssetsPlantAndMachineryConfig =
            new ValidationRuleConfig
            {
                Breadcrumb = TangibleFixedAssetsBreadcrumbs.PlantAndMachineryRule.ToString(),
                Description = "Plant and machinery account codes have been populated in the Trial balance. An accounting policy is therefore required to be entered.",
                Name = TangibleFixedAssetsPlantAndMachinery,
                Type = ValidationRuleType.Missing,
                Target = Target.IssueLog,
                ErrorCategory = ErrorCategoryType.Mandatory,
                DisplayName = "Plant and machinery accounting policy",
                ErrorCode = ValidationCodes.PlantAndMachinery
            };

        public static readonly ValidationRuleConfig TangibleFixedAssetsFixturesAndFittingsConfig =
            new ValidationRuleConfig
            {
                Breadcrumb = TangibleFixedAssetsBreadcrumbs.FixturesAndFittingsRule.ToString(),
                Description = "Fixtures and fittings account codes have been populated in the Trial balance. An accounting policy is therefore required to be entered.",
                Name = TangibleFixedAssetsFixturesAndFittings,
                Type = ValidationRuleType.Missing,
                Target = Target.IssueLog,
                ErrorCategory = ErrorCategoryType.Mandatory,
                DisplayName = "Fixtures and fittings accounting policy",
                ErrorCode = ValidationCodes.FixturesAndFittings
            };

        public static readonly ValidationRuleConfig TangibleFixedAssetsMotorVehiclesConfig =
            new ValidationRuleConfig
            {
                Breadcrumb = TangibleFixedAssetsBreadcrumbs.MotorVehiclesRule.ToString(),
                Description = "Motor vehicles account codes have been populated in the Trial balance. An accounting policy is therefore required to be entered.",
                Name = TangibleFixedAssetsMotorVehicles,
                Type = ValidationRuleType.Missing,
                Target = Target.IssueLog,
                ErrorCategory = ErrorCategoryType.Mandatory,
                DisplayName = "Motor vehicles accounting policy",
                ErrorCode = ValidationCodes.MotorVehicles
            };

        public static readonly ValidationRuleConfig TangibleFixedAssetsComputerEquipmentConfig =
            new ValidationRuleConfig
            {
                Breadcrumb = TangibleFixedAssetsBreadcrumbs.ComputerEquipmentRule.ToString(),
                Description = "Computer equipment account codes have been populated in the Trial balance. An accounting policy is therefore required to be entered.",
                Name = TangibleFixedAssetsComputerEquipment,
                Type = ValidationRuleType.Missing,
                Target = Target.IssueLog,
                ErrorCategory = ErrorCategoryType.Mandatory,
                DisplayName = "Computer equipment accounting policy",
                ErrorCode = ValidationCodes.ComputerEquipment
            };
    }
}