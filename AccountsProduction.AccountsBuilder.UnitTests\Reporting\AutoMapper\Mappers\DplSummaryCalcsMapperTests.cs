﻿using AccountsProduction.AccountsBuilder.Application.Reporting.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Domain.Reporting.Other;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.AutoMapper.Mappers
{
    public class DplSummaryCalcsMapperTests
    {
        private readonly IMapper _mapper;

        public DplSummaryCalcsMapperTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<DplSummaryCalcsMapper>();
            });
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Should_Return_Empty_List_When_Source_Is_Empty()
        {
            // Arrange
            var otherMessages = new List<OtherMessage>();

            // Act
            var result = _mapper.Map<List<DplSummaryCalcs>>(otherMessages);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void Should_Map_OtherMessageList_To_DplSummaryCalcsList()
        {
            // Arrange
            var otherMessages = new List<OtherMessage>
            {
                new OtherMessage
                {
                    PeriodId = Guid.NewGuid(),
                    WagesAndSalaries = new FinancialDataCategoryMessage
                    {
                        Value = "100",
                        DrilldownData = new List<FinancialDataDrilldownMessage> {
                            new FinancialDataDrilldownMessage { Amount = 100 }
                        }
                    },
                    SocialSecurityCosts = new FinancialDataCategoryMessage
                    {
                        Value = "200",
                        DrilldownData = new List<FinancialDataDrilldownMessage> {
                            new FinancialDataDrilldownMessage { Amount = 200 }
                        }
                    }
                }
            };

            // Act
            var result = _mapper.Map<List<DplSummaryCalcs>>(otherMessages);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("WagesAndSalaries", result[0].DPLCalcType);
            Assert.Equal(100, result[0].DPLValue);
            Assert.Equal("SocialSecurityCosts", result[1].DPLCalcType);
            Assert.Equal(200, result[1].DPLValue);
        }
    }
}

