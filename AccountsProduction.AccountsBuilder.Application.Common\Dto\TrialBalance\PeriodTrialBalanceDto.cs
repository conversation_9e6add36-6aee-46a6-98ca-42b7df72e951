﻿namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance
{
    public class PeriodTrialBalanceDto
    {
        public int AccountCode { get; set; }

        public decimal Amount { get; set; }

        public int? SubAccountCode { get; set; }

        public DateTime? Year { get; set; }

        public string Description { get; set; } = null!;

        public Guid? PeriodId { get; set; }

        public int? DirectorInvolvementId { get; set; }

        public Guid? SectorId { get; set; }

        public DateTime? SectorCreatedDate { get; set; }
    }
}
