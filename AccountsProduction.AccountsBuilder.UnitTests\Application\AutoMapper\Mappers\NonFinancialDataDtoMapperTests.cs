﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.AutoMapper.Mappers
{
    public class NonFinancialDataDtoMapperTests
    {
        private readonly IMapper _mapper;
        public NonFinancialDataDtoMapperTests()
        {
            var mapperConfig = new MapperConfiguration(cfg =>
                {
                    cfg.AddProfile<NonFinancialDataMapper>();
                    cfg.AddProfile<AddressMapper>();
                    cfg.AddProfile<ClientAddressMapper>();
                }
            );

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();
        }

        [Fact]
        public void Should_convert_client_response_to_non_financial_data()
        {
            var client = new ClientResponse
            {
                Name = "test company",
                BusinessType = "test",
                BusinessSubType = "business sub type",
                LimitedCompanyType = "LimitedCompanyType",
                RegisteredNo = "RegisteredNo"
            };

            var result = _mapper.Map<ClientResponse, NonFinancialData>(client);
            result.ShouldBeOfType(typeof(NonFinancialData));
            result.CompanyName.ShouldBe(client.Name);
            result.BusinessType.ShouldBe(client.BusinessType);
            result.BusinessSubType.ShouldBe(client.BusinessSubType);
            result.LimitedCompanyType.ShouldBe(client.LimitedCompanyType);
            result.RegisteredNo.ShouldBe(client.RegisteredNo);
        }

        [Fact]
        public void Should_convert_non_financial_data_to_client_response()
        {
            var nonFinancialData = new NonFinancialData
            {
                CompanyName = "test company",
                BusinessType = "test",
                BusinessSubType = "business sub type",
                LimitedCompanyType = "LimitedCompanyType",
                RegisteredNo = "RegisteredNo"
            };

            var result = _mapper.Map<NonFinancialData, ClientResponse>(nonFinancialData);
            result.ShouldBeOfType(typeof(ClientResponse));
            result.Name.ShouldBe(nonFinancialData.CompanyName);
            result.BusinessType.ShouldBe(nonFinancialData.BusinessType);
            result.BusinessSubType.ShouldBe(nonFinancialData.BusinessSubType);
            result.LimitedCompanyType.ShouldBe(nonFinancialData.LimitedCompanyType);
            result.RegisteredNo.ShouldBe(nonFinancialData.RegisteredNo);
        }

        [Fact]
        public void Should_convert_to_non_financial_data_to_client_document_message_dto()
        {
            var involvementDateOfDeath = DateTime.UtcNow;
            var nonFinancialData = new NonFinancialData
            {
                BusinessType = "business type",
                BusinessSubType = "business sub type",
                ClientAddresses = new ClientAddress
                {
                    MainAddress = new Address
                    {
                        County = "county 1",
                        Line1 = "line 1",
                        Line2 = "line 2",
                        PostCode = "post code 1",
                        Town = "town 1"
                    },
                    RegisteredAddress = new Address
                    {
                        County = "county 2",
                        Line1 = "line 12",
                        Line2 = "line 22",
                        PostCode = "post code 2",
                        Town = "town 2"
                    }
                },
                CompanyName = "company name",
                LimitedCompanyType = "limited company type",
                RegisteredNo = "registered no"
            };

            var result = _mapper.Map<NonFinancialData, ClientDocumentMessageDto>(nonFinancialData);

            result.ShouldNotBeNull();

            result.CompanyRegistrationNumber.ShouldBe("registered no");
            result.CompanySubType.ShouldBe("limited company type");
            result.CompanyType.ShouldBe("business type");
            result.CompanyName.ShouldBe("company name");
            result.CompanyCategory.ShouldBe("business sub type");
            result.Addresses.MainAddress.County.ShouldBe("county 1");
            result.Addresses.MainAddress.Line1.ShouldBe("line 1");
            result.Addresses.MainAddress.Line2.ShouldBe("line 2");
            result.Addresses.MainAddress.PostCode.ShouldBe("post code 1");
            result.Addresses.MainAddress.Town.ShouldBe("town 1");

            result.Addresses.RegisteredAddress.County.ShouldBe("county 2");
            result.Addresses.RegisteredAddress.Line1.ShouldBe("line 12");
            result.Addresses.RegisteredAddress.Line2.ShouldBe("line 22");
            result.Addresses.RegisteredAddress.PostCode.ShouldBe("post code 2");
            result.Addresses.RegisteredAddress.Town.ShouldBe("town 2");
        }
    }
}
