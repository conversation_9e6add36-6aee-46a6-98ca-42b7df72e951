﻿using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies
{
    public class FRS102Strategy : FRS102BaseStrategy
    {
        public FRS102Strategy(
            IMediator mediator, 
            ILogger<FRS102Strategy> logger, 
            IAccountsProductionReportingDbContext accountsProductionReportingDbContext, 
            IMapper mapper) : base(mediator, logger, accountsProductionReportingDbContext, mapper)
        {
        }

        public override string ReportTypeName => ReportType.FRS102;

        public override async Task SendSaveAndUpdateDataEvents(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
        {
            await SendFRS102BaseSaveAndUpdateDataEvents(data, ReportType.FRS102, cancellationToken);
        }       
    }
}
