﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Address;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.PracticeDetails;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Application.IntegrationEvents;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents
{
    public class EntitySetupDataEventStrategy : BaseAggregationStrategy
    {
        public override string Name => EventTypes.EntitySetupDataEvent;

        private readonly IAccountsBuilderRepository _repository;
        private readonly ILogger<EntitySetupDataEventStrategy> _logger;
        private readonly IMapper _mapper;
        private readonly UserContext _userContext;
        private readonly IGenerateReportDataService _generateReportDataService;
        private readonly IDomainEventService _domainEventService;
        public EntitySetupDataEventStrategy(IAccountsBuilderRepository repository, ILogger<EntitySetupDataEventStrategy> logger,
            IMapper mapper, UserContext userContext, IGenerateReportDataService generateReportDataService, IDomainEventService domainEventService)
        {
            _repository = repository;
            _logger = logger;
            _mapper = mapper;
            _userContext = userContext;
            _generateReportDataService = generateReportDataService;
            _domainEventService = domainEventService;
        }

        public override async Task Process(string requestMessage, Guid tenantId, Guid clientId, Guid periodId, Guid processId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("START event type {eventType} for clientId {clientId}, periodId {periodId} and processId {processId} at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, processId, DateTime.UtcNow);

            var entitySetupDto = Deserialize<EntitySetupDto>(requestMessage);

            var entitySetup = _mapper.Map<EntitySetup>(entitySetupDto);

            var accountsBuilder = await _repository.Get(clientId, periodId, cancellationToken);
            var calculteFinancialDataUsingDPL = false;
            var practiceDetailsChanged = false;
            if (HasEmptyData(accountsBuilder))
            {
                _logger.LogInformation("Empty AccountsBuilder found for clientId {clientId}, periodId {periodId} and processId {processId}.", clientId, periodId, processId);

                if (accountsBuilder is null)
                {
                    accountsBuilder = new Domain.AccountsBuilderModels.AccountsBuilder(_userContext.TenantId, clientId, periodId);
                }

                accountsBuilder.AddEntitySetup(entitySetup);

                var generateReportDto = await _generateReportDataService.GetReportData(clientId, periodId);
                var involvements = GetInvolvements(generateReportDto.InvolvementDtos);
                var trialBalance = GetTrialBalance(generateReportDto.TrialBalanceDto);
                var practiceDetails = GetPracticeDetails(generateReportDto.PracticeDetailsDto);
                var nonFinancialData = GetNonFinancialData(generateReportDto.ClientResponse, generateReportDto.ClientAddressDto);

                accountsBuilder.UpdateNonFinancialData(nonFinancialData);
                var involvementsData = new InvolvementsData
                {
                    Involvements = involvements,
                    IsDataCompleted = true
                };

                accountsBuilder.SetProcessId(processId);
                accountsBuilder.FinancialData.ResetRetry();

                accountsBuilder.AddTrialBalance(trialBalance);
                calculteFinancialDataUsingDPL = trialBalance.TrialBalances.Any();

                accountsBuilder.AddInvolvements(involvementsData);

                if (practiceDetails is not null)
                {
                    accountsBuilder.AddPracticeDetails(practiceDetails);
                }

            }
            else
            {
                calculteFinancialDataUsingDPL = entitySetup.ReportingStandard != accountsBuilder.EntitySetup.ReportingStandard && accountsBuilder.TrialBalance.TrialBalances.Any();
                practiceDetailsChanged = entitySetup.PracticeAddress != accountsBuilder.EntitySetup.PracticeAddress;

                if (calculteFinancialDataUsingDPL)
                {
                    accountsBuilder.SetProcessId(processId);
                    accountsBuilder.FinancialData.ResetRetry();
                    _logger.LogInformation("Re-calculate finanacial data using DPL because reporting standard has been udpated from {AccountsBuilderEntitySetupReportingStandard} to {EntitySetupReportingStandard} for clientId {clientId}, periodId {periodId} and processId {processId}.", accountsBuilder.EntitySetup.ReportingStandard, entitySetup.ReportingStandard, clientId, periodId, processId);
                }

                if (practiceDetailsChanged)
                {
                    accountsBuilder.SetProcessId(processId);
                    var practiceDetails = _mapper.Map<PracticeDetails>(await _generateReportDataService.GetPracticeDetails(entitySetup.PracticeAddress));

                    if (practiceDetails is not null)
                    {
                        accountsBuilder.AddPracticeDetails(practiceDetails);
                    }
                    _logger.LogInformation($"Get practice details again and save new selected address to accountsBuilder, from {accountsBuilder.EntitySetup.PracticeAddress} to {entitySetup.PracticeAddress}, for clientId {clientId}, periodId {periodId} and processId {processId}");
                }

                accountsBuilder.AddEntitySetup(entitySetup);
            }

            await _repository.Save(accountsBuilder, cancellationToken);


            if (calculteFinancialDataUsingDPL)
            {
                var notificationTrialBalanceChanged = new NotificationTrialBalanceChanged(accountsBuilder.ClientId, accountsBuilder.PeriodId, accountsBuilder.ProcessId, accountsBuilder.EntitySetup.ReportingStandard, _mapper.Map<TrialBalanceDto>(accountsBuilder.TrialBalance));
                await _domainEventService.Publish(notificationTrialBalanceChanged, CancellationToken.None);
            }

            _logger.LogInformation("Updated accounts builder with new entity setup data for clientId {clientId} and periodId {periodId}.", clientId, periodId);

            _logger.LogInformation("FINISHED event type {eventType} for clientId {clientId}, periodId {periodId} nd processId {processId} at eventTimestamp {eventTimestamp}.", Name, clientId, periodId, processId, DateTime.UtcNow);
        }

        private bool HasEmptyData(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            return (accountsBuilder is null || accountsBuilder.EntitySetup is null || !accountsBuilder.TrialBalance.TrialBalances.Any() || string.IsNullOrEmpty(accountsBuilder.NonFinancialData?.CompanyName) || string.IsNullOrEmpty(accountsBuilder.NonFinancialData?.BusinessType));
        }

        private List<Involvement> GetInvolvements(IEnumerable<InvolvementDto> involvementDtos)
        {
            var involvements = _mapper.Map<List<Involvement>>(involvementDtos);
            return involvements;
        }

        private PracticeDetails? GetPracticeDetails(PracticeDetailMessage? practiceDetailMessage)
        {
            var practiceDetails = _mapper.Map<PracticeDetails>(practiceDetailMessage);
            return practiceDetails;
        }

        private TrialBalance GetTrialBalance(TrialBalanceDto trialBalanceDto)
        {
            var trialBalance = _mapper.Map<TrialBalance>(trialBalanceDto);
            return trialBalance;
        }

        private NonFinancialData GetNonFinancialData(ClientResponse client, ClientAddressDto processedClientAddresses)
        {
            var nonFinancialData = _mapper.Map<NonFinancialData>(client);
            nonFinancialData.ClientAddresses = _mapper.Map<ClientAddress>(processedClientAddresses);
            nonFinancialData.IsDataCompleted = true;
            return nonFinancialData;
        }
    }
}