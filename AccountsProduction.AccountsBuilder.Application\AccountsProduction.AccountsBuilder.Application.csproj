﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<SonarQubeTestProject>false</SonarQubeTestProject>
		<PublishReadyToRun>true</PublishReadyToRun>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Commands\AccountsBuilder\**" />
		<Compile Remove="Commands\AggregationRequests\**" />
		<Compile Remove="Commands\GenerateReport\GenerateReport\ReportStrategies\**" />
		<EmbeddedResource Remove="Commands\AccountsBuilder\**" />
		<EmbeddedResource Remove="Commands\AggregationRequests\**" />
		<EmbeddedResource Remove="Commands\GenerateReport\GenerateReport\ReportStrategies\**" />
		<None Remove="Commands\AccountsBuilder\**" />
		<None Remove="Commands\AggregationRequests\**" />
		<None Remove="Commands\GenerateReport\GenerateReport\ReportStrategies\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Iris.AccountsProduction.AccountsBuilder.Messages" Version="1.0.0.278" />
		<PackageReference Include="Iris.AccountsProduction.Common.Toolkit" Version="2.0.0.259" />
		<PackageReference Include="Iris.Elements.Messaging" Version="1.0.0.33" />
		<PackageReference Include="Iris.Platform.Eventbus.Client.Dotnet" Version="5.0.0.475" />
		<PackageReference Include="FluentValidation" Version="11.11.0" />
		<PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
		<PackageReference Include="AutoMapper" Version="13.0.1" />
		<PackageReference Include="System.Text.Json" Version="9.0.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\AccountsProduction.AccountsBuilder.Application.Common\AccountsProduction.AccountsBuilder.Application.Common.csproj" />
		<ProjectReference Include="..\AccountsProduction.AccountsBuilder.Domain\AccountsProduction.AccountsBuilder.Domain.csproj" />
	</ItemGroup>

</Project>
