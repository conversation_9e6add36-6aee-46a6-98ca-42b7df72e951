﻿using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.ProfitShareModels;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.ProfitShare;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;

public class ProfitShareMapper : Profile
{
    public ProfitShareMapper()
    {
        CreateMap<ProfitShare, ProfitShareMessage>()
            .ReverseMap();

        CreateMap<ProfitShareDataMessage, ProfitShareData>()
            .ForMember(s => s.ProfitShares, o => o.MapFrom(d => d.ProfitShares))
            .ForMember(s => s.IsSuccessful, o => o.MapFrom(d => d.IsSuccessful))
            .ForMember(s => s.Error, o => o.MapFrom(d => d.Error))
            .ForMember(s => s.UnallocatedAmount, o => o.MapFrom(d => d.UnallocatedAmount))
            .ReverseMap();

        CreateMap<ProfitShareDataDto, ProfitShareData>()
            .ForMember(s => s.ProfitShares, o => o.MapFrom(d => d.ProfitShares))
            .ForMember(s => s.UnallocatedAmount, o => o.MapFrom(d => d.UnallocatedAmount))
            .ReverseMap();
    }
}