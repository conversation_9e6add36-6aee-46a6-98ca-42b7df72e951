﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.Involvements;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.AutoMapper.Mappers
{
    public class InvolvementMapperTests
    {
        private readonly IMapper _mapper;
        public InvolvementMapperTests()
        {
            var mapperConfig = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<InvolvementMapper>();
            }
            );

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();
        }

        [Fact]
        public void Should_convert_to_client_involvement_dto()
        {
            var involvementMessage = new Involvement
            {
                InvolvementClientGuid = TestHelpers.Guids.GuidOne,
                InvolvedClientType = "InvolvedClientType",
                InvolvementClientName = "InvolvementClientName",
                InvolvementType = "InvolvementType",
                InvolvementSurname = "InvolvementSurname",
                InvolvementTitle = "InvolvementTitle",
                InvolvementFirstName = "InvolvementFirstName",
                StartDate = DateTime.UtcNow.AddYears(-1),
                EndDate = DateTime.UtcNow.AddYears(1),
                InvolvedDateOfDeath = DateTime.UtcNow.AddYears(2),
                PdoCode = 1,
                IsDeleted = true,
                InvolvementId = 111
            };

            var result = _mapper.Map<ClientInvolvementDto>(involvementMessage);

            result.ShouldNotBeNull();
            result.ShouldBeOfType(typeof(ClientInvolvementDto));
            result.InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidOne);
            result.InvolvementFirstName.ShouldBe(involvementMessage.InvolvementFirstName);
            result.InvolvementSurname.ShouldBe(involvementMessage.InvolvementSurname);
            result.InvolvementTitle.ShouldBe(involvementMessage.InvolvementTitle);
            result.InvolvementType.ShouldBe(involvementMessage.InvolvementType);
            result.InvolvedClientType.ShouldBe(involvementMessage.InvolvedClientType);
            result.InvolvementClientName.ShouldBe(involvementMessage.InvolvementClientName);
            result.StartDate.ShouldBe(involvementMessage.StartDate);
            result.EndDate.ShouldBe(involvementMessage.EndDate);
            result.InvolvedDateOfDeath.ShouldBe(involvementMessage.InvolvedDateOfDeath);
            result.PdoCode.ShouldBe(involvementMessage.PdoCode);
            result.IsDeleted.ShouldBe(involvementMessage.IsDeleted);
            result.InvolvementId.ShouldBe(involvementMessage.InvolvementId);
        }

        [Fact]
        public void Should_convert_to_involvements_entity()
        {
            var clientInvolvementDto = new ClientInvolvementDto
            {
                InvolvementId = 111,
                InvolvementClientGuid = TestHelpers.Guids.GuidOne,
                InvolvedClientType = "InvolvedClientType",
                InvolvementClientName = "InvolvementClientName",
                InvolvementType = "InvolvementType",
                InvolvementSurname = "InvolvementSurname",
                InvolvementTitle = "InvolvementTitle",
                InvolvementFirstName = "InvolvementFirstName",
                StartDate = DateTime.UtcNow.AddYears(-1),
                EndDate = DateTime.UtcNow.AddYears(1),
                IsDeleted = true,
                InvolvedDateOfDeath = DateTime.UtcNow.AddYears(2)
            };

            var result = _mapper.Map<Involvement>(clientInvolvementDto);

            result.ShouldBeOfType(typeof(Involvement));
            result.ShouldNotBeNull();
            result.ShouldBeOfType(typeof(Involvement));
            result.InvolvementId.ShouldBe(111);
            result.InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidOne);
            result.InvolvementFirstName.ShouldBe(clientInvolvementDto.InvolvementFirstName);
            result.InvolvementSurname.ShouldBe(clientInvolvementDto.InvolvementSurname);
            result.InvolvementTitle.ShouldBe(clientInvolvementDto.InvolvementTitle);
            result.InvolvementType.ShouldBe(clientInvolvementDto.InvolvementType);
            result.InvolvedClientType.ShouldBe(clientInvolvementDto.InvolvedClientType);
            result.InvolvementClientName.ShouldBe(clientInvolvementDto.InvolvementClientName);
            result.StartDate.ShouldBe(clientInvolvementDto.StartDate);
            result.EndDate.ShouldBe(clientInvolvementDto.EndDate);
            result.IsDeleted.ShouldBe(clientInvolvementDto.IsDeleted);
            result.InvolvedDateOfDeath.ShouldBe(clientInvolvementDto.InvolvedDateOfDeath);
        }

        [Fact]
        public void Should_convert_to_involvement_from_message()
        {
            var involvementMessage = new InvolvementMessage
            {
                InvolvedClientGuid = TestHelpers.Guids.GuidOne,
                InvolvedClientType = "InvolvedClientType",
                InvolvedClientName = "InvolvementClientName",
                InvolvementType = "InvolvementType",
                InvolvedClientSurname = "InvolvementSurname",
                InvolvedClientTitle = "InvolvementTitle",
                InvolvedClientFirstName = "InvolvementFirstName",
                StartDate = DateTime.UtcNow.AddYears(-1),
                EndDate = DateTime.UtcNow.AddYears(1),
                InvolvedClientDateOfDeath = DateTime.UtcNow.AddYears(2),
                PdoCode = 1,
                IsDeleted = true,
                InvolvementId = 111
            };

            var result = _mapper.Map<Involvement>(involvementMessage);

            result.ShouldNotBeNull();
            result.ShouldBeOfType(typeof(Involvement));
            result.InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidOne);
            result.InvolvementFirstName.ShouldBe(involvementMessage.InvolvedClientFirstName);
            result.InvolvementSurname.ShouldBe(involvementMessage.InvolvedClientSurname);
            result.InvolvementTitle.ShouldBe(involvementMessage.InvolvedClientTitle);
            result.InvolvementType.ShouldBe(involvementMessage.InvolvementType);
            result.InvolvedClientType.ShouldBe(involvementMessage.InvolvedClientType);
            result.InvolvementClientName.ShouldBe(involvementMessage.InvolvedClientName);
            result.StartDate.ShouldBe(involvementMessage.StartDate);
            result.EndDate.ShouldBe(involvementMessage.EndDate);
            result.InvolvedDateOfDeath.ShouldBe(involvementMessage.InvolvedClientDateOfDeath);
            result.PdoCode.ShouldBe(involvementMessage.PdoCode);
            result.IsDeleted.ShouldBe(involvementMessage.IsDeleted);
            result.InvolvementId.ShouldBe(involvementMessage.InvolvementId);
        }
    }
}
