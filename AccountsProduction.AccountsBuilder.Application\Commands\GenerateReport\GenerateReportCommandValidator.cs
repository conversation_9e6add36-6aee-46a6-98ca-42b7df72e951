﻿using AccountsProduction.AccountsBuilder.Application.Common;
using FluentValidation;
using System.Reflection;

namespace AccountsProduction.AccountsBuilder.Application.Commands.GenerateReport
{
    public class GenerateReportCommandValidator : AbstractValidator<GenerateReportCommand>
    {
        private readonly List<string> _validReportTypes = Assembly.GetAssembly(typeof(ReportStandardType))?
            .GetTypes().First(x => x.Name == nameof(ReportStandardType)).GetFields().Select(x => x.GetValue(null)?.ToString()).ToList();

        public GenerateReportCommandValidator()
        {
            RuleFor(command => command.PeriodId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty();

            RuleFor(command => command.ReportingStandard.Type)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                .Must(x => _validReportTypes.Contains(x))
                .WithMessage("Report type is invalid!");

        }
    }
}
