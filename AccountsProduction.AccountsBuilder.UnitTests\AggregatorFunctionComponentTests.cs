﻿using AccountsProduction.AccountsBuilder.AggregatorFunc;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Application.IntegrationEvents;
using AccountsProduction.AccountsBuilder.Domain;
using Amazon.Lambda.SNSEvents;
using Amazon.Lambda.TestUtilities;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Iris.Platform.Eventbus.Client.Dotnet.Client;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Newtonsoft.Json.Linq;
using System.Text;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests
{
    public class AggregatorFunctionComponentTests
    {
        private static readonly Guid ClientId = TestHelpers.Guids.GuidTwo;
        private static readonly Guid PeriodId = TestHelpers.Guids.GuidThree;
        private static readonly Guid ProcessId = TestHelpers.Guids.GuidFour;
        private readonly ServiceCollection _serviceCollection;
        private readonly Mock<IDomainEventService> _domainEventService;
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private const string ApplicationName = "Aggregator";

        public AggregatorFunctionComponentTests()
        {
            AddEnivonmentVariables();
            _serviceCollection = InitializeServiceCollection();
            _serviceCollection.AddApplication(ApplicationName);
            _domainEventService = MockDomainEventService(_serviceCollection);
            MockEnvVariableProvider(_serviceCollection);
            MockEventBusClient(_serviceCollection);
            _repository = MockAccountsBuilderRepository(_serviceCollection);
            MockUserContext(_serviceCollection);
            MockAccountPeriodService(_serviceCollection);
        }

        [Fact]
        public async Task Should_send_correct_message_for_TrialBalanceResponseEvent()
        {
            var serviceProvider = _serviceCollection.BuildServiceProvider();
            var function = new Handler(serviceProvider);
            var snsEvent = BuildSnsEvent(BuildTrialBalanceMessage());
            var snsEventStream = new MemoryStream(Encoding.UTF8.GetBytes(JsonSerializer.Serialize(snsEvent)));

            await function.HandleEvent(snsEventStream, new TestLambdaContext());

            _domainEventService.Verify(x =>
                x.Publish(It.IsAny<NotificationTrialBalanceChanged>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Should_send_correct_message_for_Nonfinancial_DataEvent()
        {
            var serviceProvider = _serviceCollection.BuildServiceProvider();
            var function = new Handler(serviceProvider);
            var messageBody = BuildNonfinancialDataMessage();
            var snsEvent = BuildSnsEvent(messageBody);
            var snsEventStream = new MemoryStream(Encoding.UTF8.GetBytes(JsonSerializer.Serialize(snsEvent)));

            await function.HandleEvent(snsEventStream, new TestLambdaContext());

            _repository.Verify(
               repository => repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(entity =>
                   entity.NonFinancialData.CompanyName != null),
                   CancellationToken.None),
               Times.Once);
        }

        private void MockUserContext(ServiceCollection serviceCollection)
        {
            var existingService = serviceCollection.Single(x => x.ServiceType == typeof(UserContext));
            serviceCollection.Remove(existingService);

            var mockService = new Mock<UserContext>();
            mockService.Setup(x => x.TenantId).Returns(TestHelpers.Guids.GuidOne.ToString());
            mockService.Setup(x => x.CorrelationId).Returns(TestHelpers.Guids.GuidTwo.ToString());
            mockService.Setup(x => x.UserId).Returns(TestHelpers.Guids.GuidThree.ToString());

            serviceCollection.AddScoped(_ => mockService.Object);
        }

        private Mock<IDomainEventService> MockDomainEventService(ServiceCollection serviceCollection)
        {
            var existingService = serviceCollection.Single(x => x.ServiceType == typeof(IDomainEventService));
            serviceCollection.Remove(existingService);

            var mockService = new Mock<IDomainEventService>();

            serviceCollection.AddScoped(_ => mockService.Object);

            return mockService;
        }

        private Mock<IAccountsBuilderRepository> MockAccountsBuilderRepository(ServiceCollection serviceCollection)
        {
            var existingService = serviceCollection.Single(x => x.ServiceType == typeof(IAccountsBuilderRepository));
            serviceCollection.Remove(existingService);

            var entitySetup = new EntitySetup
            {
                EntitySize = "Small",
                Terminology = "Companies Act",
                ReportingStandard = ReportStandardType.FRS102_1A
            };

            var accountBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
            {
                ClientId = TestHelpers.Guids.GuidTwo,
                EntitySetup = entitySetup,
                ReportingStandard = new ReportingStandard(Guid.NewGuid().ToString(), "frs 102 full", ReportStandardType.FRS102_1A, ReportingStandardVersion.Full),
                LicenseData = new LicenseData()
            };

            var mockService = new Mock<IAccountsBuilderRepository>();
            mockService
                .Setup(x => x.Get(ClientId, PeriodId, CancellationToken.None))
                .ReturnsAsync(accountBuilder);

            mockService
              .Setup(x => x.Get(ProcessId, CancellationToken.None))
              .ReturnsAsync(accountBuilder);


            mockService
              .Setup(x => x.GetAll(accountBuilder.ClientId, CancellationToken.None))
              .ReturnsAsync(new List<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>() { accountBuilder });

            serviceCollection.AddScoped(_ => mockService.Object);

            return mockService;
        }


        private void MockEnvVariableProvider(ServiceCollection serviceCollection)
        {
            var existingService = serviceCollection.Single(x => x.ServiceType == typeof(IEnvVariableProvider));
            serviceCollection.Remove(existingService);

            var mockService = new Mock<IEnvVariableProvider>();
            mockService.Setup(x => x.ClientApiScheme).Returns("scheme");
            mockService.Setup(x => x.ClientApiHost).Returns("host");
            mockService.Setup(x => x.AccountPeriodApiScheme).Returns("scheme");
            mockService.Setup(x => x.AccountPeriodApiHost).Returns("host");
            mockService.Setup(x => x.TrialBalanceApiScheme).Returns("scheme");
            mockService.Setup(x => x.TrialBalanceApiHost).Returns("host");
            mockService.Setup(x => x.ReportingTrialBalanceRequestTopic).Returns("ReportingTrialBalanceRequestTopic");
            mockService.Setup(x => x.AccountsProductionApiScheme).Returns("scheme");
            mockService.Setup(x => x.AccountsProductionApiHost).Returns("host");

            serviceCollection.AddScoped(_ => mockService.Object);
        }

        private void AddEnivonmentVariables()
        {
            Environment.SetEnvironmentVariable("AWS_DEFAULT_REGION", "eu-west-2");
            Environment.SetEnvironmentVariable("REGION", "eu-west-2");
            Environment.SetEnvironmentVariable("AWS_REGION", "1");
            Environment.SetEnvironmentVariable("AWS_ACCESS_KEY_ID", "2");
            Environment.SetEnvironmentVariable("AWS_SECRET_ACCESS_KEY", "123");
            Environment.SetEnvironmentVariable("AWS_ACCOUNT_ID", "123");
            Environment.SetEnvironmentVariable("EVENTBUS_AWS_ACCOUNT_ID", "123");
            Environment.SetEnvironmentVariable("EVENT_BUS_MAIN_QUEUE_URL", "https://sqs.eu-west-2.amazonaws.com");
            Environment.SetEnvironmentVariable("EVENTBUS_LARGE_PAYLOADS_S3_BUCKET_NAME", "platform-eventbus-large-payloads");
        }

        private Mock<IEventBusClient> MockEventBusClient(ServiceCollection serviceCollection)
        {
            var mockService = new Mock<IEventBusClient>();

            serviceCollection.AddScoped<IEventBusClient>(e => mockService.Object);

            return mockService;
        }

        private Mock<IAccountPeriodService> MockAccountPeriodService(ServiceCollection serviceCollection)
        {
            var mockService = new Mock<IAccountPeriodService>();

            mockService.Setup(x => x.GetRoundingOptionsAsync(It.Is<Guid>(g => g == ClientId), It.Is<Guid>(p => p == PeriodId)))
              .ReturnsAsync(new AccountsBuilder.Application.Common.Dto.RoundingOptions.RoundingOptionsResponse()
              {
                  AccountPeriodId = PeriodId,
                  ProfitLossRoundingAccount = 280,
                  ProfitLossRoundingAccountDescription = "Sundry expenses",
                  BalanceSheetRoundingAccount = 737,
                  BalanceSheetRoundingAccountDescription = "Trade creditors",
                  UseAdvancedRounding = true
              });

            serviceCollection.AddScoped(e => mockService.Object);

            return mockService;
        }

        private static ServiceCollection InitializeServiceCollection()
        {
            var serviceCollection = new ServiceCollection();
            return serviceCollection;
        }

        private static SNSEvent BuildSnsEvent(string messageBody)
        {
            var sqsEvent = new SNSEvent
            {
                Records = new List<SNSEvent.SNSRecord>
                {
                    new SNSEvent.SNSRecord
                    {
                        Sns = new SNSEvent.SNSMessage
                        {
                            Message = messageBody,
                            Subject = "subject",
                            MessageAttributes = new Dictionary<string, SNSEvent.MessageAttribute>
                            {
                                {
                                    "ClientId", new SNSEvent.MessageAttribute
                                    {
                                        Type = "String",
                                        Value = ClientId.ToString()
                                    }
                                },
                                {
                                    "PeriodId", new SNSEvent.MessageAttribute
                                    {
                                        Type = "String",
                                        Value = PeriodId.ToString()
                                    }
                                },
                                {
                                    "ProcessId", new SNSEvent.MessageAttribute
                                    {
                                        Type = "String",
                                        Value = ProcessId.ToString()
                                    }
                                }
                            },
                            MessageId = Guid.NewGuid().ToString()
                        }
                    }
                }
            };

            return sqsEvent;
        }

        private static string BuildTrialBalanceMessage()
        {
            var body = new
            {
                Meta = new
                {
                    CorelationId = TestHelpers.Guids.GuidOne,
                    Version = "1",
                    CreatedAtUtc = new DateTime(2022, 2, 2),
                    Type = EventTypes.TrialBalanceDataEvent,
                    TenantId = TestHelpers.Guids.GuidTwo,
                    Source = string.Empty,
                    CreatedByUserId = TestHelpers.Guids.GuidFive
                },
                Message = new TrialBalanceDto
                {
                    PeriodId = TestHelpers.Guids.GuidOne,
                    ClientId = ClientId,
                    AccountsChartId = 1,
                    AccountsChartIdentifier = "1",
                    GroupStructureId = 1,
                    GroupStructureCode = 2,
                    TrialBalances = new List<PeriodTrialBalanceDto>
                    {
                        new PeriodTrialBalanceDto
                        {
                            PeriodId = PeriodId,
                            AccountCode = 1,
                            Amount = 1,
                            Description = "Description",
                            DirectorInvolvementId = 1,
                            SubAccountCode = 1,
                            Year = new DateTime(2022,2,2),
                            SectorId = TestHelpers.Guids.GuidThree,
                            SectorCreatedDate = new DateTime(2022,2,2)
                        }
                    }
                }
            };

            return JsonSerializer.Serialize(body, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
        }

        private static string BuildNonfinancialDataMessage()
        {
            var body = new
            {
                Meta = new
                {
                    CorelationId = TestHelpers.Guids.GuidOne,
                    Version = "1",
                    CreatedAtUtc = new DateTime(2022, 2, 2),
                    Type = EventTypes.ClientDataEvent,
                    TenantId = TestHelpers.Guids.GuidTwo,
                    Source = string.Empty,
                    CreatedByUserId = TestHelpers.Guids.GuidFive
                },
                Message = JObject.FromObject(new
                {
                    name = Guid.NewGuid().ToString()
                }).ToString(Newtonsoft.Json.Formatting.None)
            };

            return JsonSerializer.Serialize(body, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
        }

    }
}
