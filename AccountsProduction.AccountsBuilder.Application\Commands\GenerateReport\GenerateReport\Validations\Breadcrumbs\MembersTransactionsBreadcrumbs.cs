﻿using AccountsProduction.AccountsBuilder.Application.Common;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs
{
    public static class MembersTransactionsBreadcrumbs
    {
        private const string AccountsBuilderName = "Accounts Builder";
        private const string MembersTransactionsName = "Members transactions";

        public static Breadcrumb MembersTransactionsRule =>
            new Breadcrumb
            {
                Value = AccountsBuilderName,
                Child = new Breadcrumb
                {
                    Value = BreadcrumbSection.Frs1021A,
                    Child = new Breadcrumb
                    {
                        Value = MembersTransactionsName,
                        Child = null
                    }
                }
            };
    }
}