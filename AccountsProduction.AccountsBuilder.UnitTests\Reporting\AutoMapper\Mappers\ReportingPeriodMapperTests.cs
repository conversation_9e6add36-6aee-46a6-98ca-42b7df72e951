﻿using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper;
using AccountsProduction.AccountsBuilder.Reporting.Application.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Domain;
using AutoMapper;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.AutoMapper.Mappers
{
    public class ReportingPeriodMapperTests
    {
        private readonly IMapper _mapper;

        public ReportingPeriodMapperTests()
        {
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
        }

        [Fact]
        public void Should_map_reporting_period()
        {
            var accountPeriodId = Guid.NewGuid();
            var clientId = Guid.NewGuid();

            var reportingPeriod = new ReportingPeriod
            {
                Client = new Client(),
                AccountPeriodId = accountPeriodId,
                ClientId = clientId,
                WatermarkText = "Trial"
            };

            var reportingPeriodEndDate = DateTime.Now;
            var reportingPeriodStartDate = DateTime.Now.AddYears(-1);

            var reportingPeriodDto = new ReportingPeriodDto
            {
                AccountPeriodId = Guid.NewGuid(),
                ClientId = Guid.NewGuid(),
                ReportingPeriodEndDate = reportingPeriodEndDate,
                ReportingPeriodStartDate = reportingPeriodStartDate
            };

            _mapper.Map(reportingPeriodDto, reportingPeriod);

            reportingPeriod.ClientId.ShouldBe(clientId);
            reportingPeriod.AccountPeriodId.ShouldBe(accountPeriodId);
            reportingPeriod.Client.ShouldNotBeNull();
            reportingPeriod.WatermarkText.ShouldNotBeNull();

            reportingPeriod.ReportingPeriodEndDate.ShouldBe(reportingPeriodEndDate);
            reportingPeriod.ReportingPeriodStartDate.ShouldBe(reportingPeriodStartDate);
        }
    }
}
