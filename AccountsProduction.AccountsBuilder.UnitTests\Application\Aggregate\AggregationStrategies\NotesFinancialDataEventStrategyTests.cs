﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.AggregationStrategies
{
    public class NotesFinancialDataEventStrategyTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<ILogger<NotesFinancialDataEventStrategy>> _logger;
        private readonly IMapper _mapper;
        private readonly Mock<UserContext> _userContext;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = TestHelpers.Guids.GuidTwo;
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;

        public NotesFinancialDataEventStrategyTests()
        {
            _repository = new Mock<IAccountsBuilderRepository>();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _logger = new Mock<ILogger<NotesFinancialDataEventStrategy>>();
            _userContext = new Mock<UserContext>();
            _userContext.Setup(q => q.TenantId).Returns(_tenantId.ToString());
        }

        [Theory]
        [InlineData(ReportStandardType.FRS105)]
        [InlineData(ReportStandardType.FRS102_1A)]
        [InlineData(ReportStandardType.FRS102)]
        [InlineData(ReportStandardType.UNINCORPORATED)]
        public async Task Should_update_notes_data_and_publish_to_reporting_domain(string reportType)
        {
            var reportingStandardDetail = new ReportingStandard
            {
                Id = "1",
                Name = reportType,
                Version = ReportingStandardVersion.Full,
                Type = reportType
            };

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId, null, null, reportingStandardDetail));

            var strategy = new NotesFinancialDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Once);
        }

        [Theory]
        [InlineData(ReportStandardType.FRS105)]
        [InlineData(ReportStandardType.FRS102_1A)]
        [InlineData(ReportStandardType.FRS102)]
        [InlineData(ReportStandardType.UNINCORPORATED)]
        public async Task Should_update_in_error_state(string reportType)
        {
            var reportingStandardDetail = new ReportingStandard
            {
                Id = "1",
                Name = reportType,
                Version = ReportingStandardVersion.Full,
                Type = reportType
            };
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId, null, null, reportingStandardDetail));

            var strategy = new NotesFinancialDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetFailedRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Once);
        }

        [Fact]
        public async Task Should_not_update_when_no_process_found()
        {
            var strategy = new NotesFinancialDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_not_update_when_invalid_tenant_process_found()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId));
            _userContext.Setup(q => q.TenantId).Returns(TestHelpers.Guids.GuidFive.ToString());
            var strategy = new NotesFinancialDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_not_update_when_no_accounts_builder_found()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(() => null);
            var strategy = new NotesFinancialDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_not_update_when_invalid_tenant_accounts_builder_found()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(TestHelpers.Guids.GuidSix.ToString(), _clientId, _periodId));

            _userContext.Setup(q => q.TenantId).Returns(TestHelpers.Guids.GuidFive.ToString());
            var strategy = new NotesFinancialDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Should_throw_on_exception()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId));
            _repository.Setup(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None)).Throws<Exception>();
            var strategy = new NotesFinancialDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await Should.ThrowAsync<Exception>(async () => { await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None); });
        }

        private string GetSuccessRequestMessage()
        {
            var message = new NotesResponseMessage
            {
                PreviousPeriodId = TestHelpers.Guids.GuidOne,
                PeriodId = TestHelpers.Guids.GuidTwo,
                ClientId = TestHelpers.Guids.GuidThree,
                TenantId = TestHelpers.Guids.GuidFour,
                CorrelationId = TestHelpers.Guids.GuidFour,
                Error = "",
                IsSuccessful = true,
                CurrentPeriodNotes = new NotesResponseDataMessage
                {
                    AverageNumberOfEmployees = new AverageNumberOfEmployeesMessage
                    {
                        CurrentPeriod = 1,
                        PreviousPeriod = 2
                    },
                    OffBalanceSheetArrangements = "text1",
                    AdvancesCreditAndGuaranteesGrantedToDirectors = "text2",
                    GuaranteesAndOtherFinancialCommitments = "text3",
                    RelatedPartyTransactions = "text4",
                    LoansAndOtherDebtsDueToMembers = "text44",
                    MembersLiabilityText = new MembersLiabilityTextMessage { NoteTitle = "Members Liability Note Title", NoteText = "Members Liability Note Text" },
                    AdditionalNote1 = new AdditionalNoteMessage { NoteTitle = "text5", NoteText = "text6" },
                    AdditionalNote2 = new AdditionalNoteMessage { NoteTitle = "text7", NoteText = "text8" },
                    ControllingPartyNote = "text9",
                    OperatingProfitLoss = new OperatingProfitLossMessage
                    {
                        IsEnabled = true,
                        Items = new List<OperatingProfitLossItemMessage>
                        {
                            new OperatingProfitLossItemMessage { Index = 1, Description = "Test1", Value = 123 },
                            new OperatingProfitLossItemMessage { Index = 1, Description = "Test1", Value = -234 },
                            new OperatingProfitLossItemMessage { Index = 1, Description = "Test1", Value = 1234.56M },
                        }
                    },
                    AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = new AdvancesCreditAndGuaranteesGrantedToDirectorsMessage
                    {
                        Guarantees = "Guarantees",
                        Items = new List<AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage>
                        {
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage
                            {
                                Index = 1,
                                InvolvementClientGuid = TestHelpers.Guids.GuidFive,
                                DirectorName = "Director1",
                                BalanceOutstandingAtStartOfYear = 1000M,
                                AmountsAdvanced = 100M,
                                AmountsRepaid = 0M,
                                AmountsWrittenOff = 0M,
                                AmountsWaived = 0M,
                                AdvanceCreditConditions = "Director1 AdvanceCreditConditions",
                                BalanceOutstandingAtEndOfYear = 1100M
                            },
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage
                            {
                                Index = 2,
                                InvolvementClientGuid = TestHelpers.Guids.GuidSix,
                                DirectorName = "Director2",
                                BalanceOutstandingAtStartOfYear = 2000M,
                                AmountsAdvanced = 200M,
                                AmountsRepaid = 0M,
                                AmountsWrittenOff = 0M,
                                AmountsWaived = 0M,
                                AdvanceCreditConditions = "Director2 AdvanceCreditConditions",
                                BalanceOutstandingAtEndOfYear = 2200M
                            },
                        }
                    },
                    TangibleFixedAssetsNotes = new TangibleFixedAssetsNotesMessage
                    {
                        ValuationInCurrentReportingPeriod = new ValuationInCurrentReportingPeriodMessage
                        {
                            ValuationDetails = "Valuation Details long message",
                            IndependentValuerInvolved = true,
                            RevaluationBasis = "Revaluation Basis  short",
                            DateOfRevaluation = new DateTime(2019, 05, 09, 09, 15, 00)
                        },
                        HistoricalCostBreakdown = new HistoricalCostBreakdownMessage
                        {
                            RevaluedAssetClass = "Revalued Asset Class",
                            RevaluedClassPronoun = "Revalued Class Pronoun",
                            CurrentReportingPeriodAccumulatedDepreciation = 1.33m,
                            CurrentReportingPeriodCost = 1.45m,
                        },
                        AnalysisOfCostOrValuation = new AnalysisOfCostOrValuationMessage
                        {
                            AnalysisOfCostOrValuationItems = new System.Collections.Generic.List<AnalysisOfCostOrValuationItemMessage>
                            {
                                new AnalysisOfCostOrValuationItemMessage
                                {
                                    Index = 3,
                                    Year = 2019,
                                    LandAndBuildings = 1.1m,
                                    PlantAndMachineryEtc = 2.2m
                                },
                                new AnalysisOfCostOrValuationItemMessage
                                {
                                    Index = 4,
                                    Year = 2020,
                                    LandAndBuildings = 3.3m,
                                    PlantAndMachineryEtc = 4.4m
                                },
                                new AnalysisOfCostOrValuationItemMessage
                                {
                                    Index = 5,
                                    Year = 2021,
                                    LandAndBuildings = -3.3m,
                                    PlantAndMachineryEtc = -4.4m
                                }
                            },
                            CostLandAndBuildings = 5.5m,
                            CostPlantAndMachineryEtc = 6.6m,
                            TotalLandAndBuildings = 11,
                            TotalPlantAndMachineryEtc = 12
                        }
                    }
                }
            };

            return JsonSerializer.Serialize(message, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
        }

        private static string GetFailedRequestMessage()
        {
            var message = new NotesResponseMessage { IsSuccessful = false, Error = "error" };

            return JsonSerializer.Serialize(message, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
        }
    }
}