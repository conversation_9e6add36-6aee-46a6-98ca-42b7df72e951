﻿using Amazon.DynamoDBv2.DocumentModel;
using AccountsProduction.AccountsBuilder.Domain.DataScreens;
using System.Text.Json;
using Xunit;
using Shouldly;

namespace AccountsProduction.AccountsBuilder.UnitTests.Domain.DataScreens
{
    public class PolymorphicConverterTests
    {
        private readonly PolymorphicConverter _converter = new();

        [Theory]
        [InlineData(null, typeof(DynamoDBNull))]
        [InlineData(123, typeof(Primitive))]
        [InlineData(123L, typeof(Primitive))]
        [InlineData(123.45f, typeof(Primitive))]
        [InlineData(123.45, typeof(Primitive))]
        [InlineData(true, typeof(DynamoDBBool))]
        [InlineData("test", typeof(Primitive))]
        public void ToEntry_ValidValues_ReturnsExpectedDynamoDBEntry(object value, Type expectedType)
        {
            var entry = _converter.ToEntry(value);
            Assert.IsType(expectedType, entry);
        }

        [Fact]
        public void ToEntry_Int32_ReturnsExpectedDynamoDBEntry()
        {
            // Arrange
            var converter = new PolymorphicConverter();
            int input = 42;
            var expected = new Primitive(input.ToString(), true);

            // Act
            var result = converter.ToEntry(input);

            // Assert
            Assert.IsType<Primitive>(result);

            var primitiveResult = (Primitive)result;
            Assert.Equal(expected.Value, primitiveResult.Value);
            Assert.Equal(expected.Type, primitiveResult.Type);
        }

        [Fact]
        public void ToEntry_Int64_ReturnsExpectedDynamoDBEntry()
        {
            // Arrange
            var converter = new PolymorphicConverter();
            long input = 123L;
            var expected = new Primitive(123L.ToString(), true);

            // Act
            var result = converter.ToEntry(input);

            // Assert
            Assert.IsType<Primitive>(result);

            var primitiveResult = (Primitive)result;
            Assert.Equal(expected.Value, primitiveResult.Value);
            Assert.Equal(expected.Type, primitiveResult.Type);
        }

        [Fact]
        public void ToEntry_Single_ReturnsExpectedDynamoDBEntry()
        {
            // Arrange
            var converter = new PolymorphicConverter();
            float input = 123.45f;
            var expected = new Primitive(input.ToString(), true);

            // Act
            var result = converter.ToEntry(input);

            // Assert
            Assert.IsType<Primitive>(result);

            var primitiveResult = (Primitive)result;
            Assert.Equal(expected.Value, primitiveResult.Value);
            Assert.Equal(expected.Type, primitiveResult.Type);
        }

        [Fact]
        public void ToEntry_Double_ReturnsExpectedDynamoDBEntry()
        {
            // Arrange
            var converter = new PolymorphicConverter();
            double input = 123.45;
            var expected = new Primitive(input.ToString(), true);

            // Act
            var result = converter.ToEntry(input);

            // Assert
            Assert.IsType<Primitive>(result);

            var primitiveResult = (Primitive)result;
            Assert.Equal(expected.Value, primitiveResult.Value);
            Assert.Equal(expected.Type, primitiveResult.Type);

        }

        [Fact]
        public void ToEntry_Decimal_ReturnsExpectedDynamoDBEntry()
        {
            // Arrange
            var converter = new PolymorphicConverter();
            decimal input = 123.45m;
            var expected = new Primitive(input.ToString(), true);

            // Act
            var result = converter.ToEntry(input);

            // Assert
            Assert.IsType<Primitive>(result);

            var primitiveResult = (Primitive)result;
            Assert.Equal(expected.Value, primitiveResult.Value);
            Assert.Equal(expected.Type, primitiveResult.Type);
        }

        [Fact]
        public void ToEntry_Bool_ReturnsExpectedDynamoDBEntry()
        {
            // Arrange
            var converter = new PolymorphicConverter();
            bool input = true;
            var expected = new DynamoDBBool(input);

            // Act
            var result = converter.ToEntry(input);

            // Assert
            Assert.IsType<DynamoDBBool>(result);

            var primitiveResult = (DynamoDBBool)result;
            Assert.Equal(expected.Value, primitiveResult.Value);
        }

        [Fact]
        public void ToEntry_String_ReturnsExpectedDynamoDBEntry()
        {
            // Arrange
            var converter = new PolymorphicConverter();
            string input = "test";
            var expected = new Primitive(input.ToString(), false);

            // Act
            var result = converter.ToEntry(input);

            // Assert
            Assert.IsType<Primitive>(result);

            var primitiveResult = (Primitive)result;
            Assert.Equal(expected.Value, primitiveResult.Value);
            Assert.Equal(expected.Type, primitiveResult.Type);
        }

        [Fact]
        public void ToEntry_JsonElement_ReturnsExpectedDynamoDBEntry()
        {
            var jsonElement = JsonDocument.Parse("\"test\"").RootElement;
            var entry = _converter.ToEntry(jsonElement);
            Assert.IsType<Primitive>(entry);
        }

        [Fact]
        public void ToEntry_UnsupportedType_ThrowsError()
        {
            Should.Throw<ArgumentException>(() => _converter.ToEntry(new { }));
        }


        [Theory]
        [InlineData(typeof(DynamoDBNull), null)]
        [InlineData(typeof(DynamoDBBool), true)]
        [InlineData(typeof(Primitive), 123.45)]
        [InlineData(typeof(Primitive), "test")]
        public void FromEntry_ValidEntries_ReturnsExpectedValue(Type entryType, object expectedValue)
        {
            DynamoDBEntry entry = entryType switch
            {
                Type t when t == typeof(DynamoDBNull) => new DynamoDBNull(),
                Type t when t == typeof(DynamoDBBool) => new DynamoDBBool((bool)expectedValue),
                Type t when t == typeof(Primitive) && expectedValue is double => new Primitive(expectedValue.ToString(), true),
                Type t when t == typeof(Primitive) && expectedValue is string => new Primitive((string)expectedValue, false),
                _ => throw new ArgumentException($"Unsupported entry type: {entryType}")
            };

            var value = _converter.FromEntry(entry);
            Assert.Equal(expectedValue, value);
        }
    }
}

