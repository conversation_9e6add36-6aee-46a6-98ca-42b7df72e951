﻿using AccountsProduction.AccountsBuilder.Reporting.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations
{
    public class ClientConfiguration : IEntityTypeConfiguration<Client>
    {
        public void Configure(EntityTypeBuilder<Client> builder)
        {
            builder.ToTable("Client", "public");

            builder.Property(e => e.Id).ValueGeneratedNever();

            builder.HasOne(d => d.Tenant)
                .WithMany(p => p!.Client)
                .HasForeignKey(d => d.TenantId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("client_fk");
        }
    }
}
