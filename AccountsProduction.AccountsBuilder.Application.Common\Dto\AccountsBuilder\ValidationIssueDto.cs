﻿namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder
{
    public class ValidationIssueDto
    {
        public string Name { get; set; } = null!;
        public string DisplayName { get; set; } = null!;
        public string ErrorCategory { get; set; } = null!;
        public string Type { get; set; } = null!;
        public string Breadcrumb { get; set; } = null!;
        public string Description { get; set; } = null!;
        public string Target { get; set; } = null!;
        public string ErrorCode { get; set; } = null!;
    }
}