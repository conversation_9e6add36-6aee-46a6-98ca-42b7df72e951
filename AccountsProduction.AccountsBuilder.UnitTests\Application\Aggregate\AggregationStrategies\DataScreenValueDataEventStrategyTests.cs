﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.DataScreens;
using Amazon.DynamoDBv2.DocumentModel;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.ScreenValues;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.AggregationStrategies
{
    public class DataScreenValueDataEventStrategyTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<ILogger<NotesFinancialDataEventStrategy>> _logger;
        private readonly IMapper _mapper;
        private readonly Mock<UserContext> _userContext;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = TestHelpers.Guids.GuidTwo;
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;
        private PolymorphicConverter _converter;

        public DataScreenValueDataEventStrategyTests()
        {
            _repository = new Mock<IAccountsBuilderRepository>();
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
            _logger = new Mock<ILogger<NotesFinancialDataEventStrategy>>();
            _userContext = new Mock<UserContext>();
            _userContext.Setup(q => q.TenantId).Returns(_tenantId.ToString());
            _converter = new PolymorphicConverter();
        }

        [Theory]
        [InlineData(ReportStandardType.FRS102_1A)]
        [InlineData(ReportStandardType.FRS102)]
        public async Task Should_update_datascreen_value_data(string reportType)
        {
            var reportingStandardDetail = new ReportingStandard
            {
                Id = "1",
                Name = reportType,
                Version = ReportingStandardVersion.Full,
                Type = reportType
            };

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId, null, null, reportingStandardDetail));

            var strategy = new DataScreenValueDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetSuccessRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Once);
        }

        [Theory]
        [InlineData(ReportStandardType.FRS102_1A)]
        [InlineData(ReportStandardType.FRS102)]
        public async Task Should_not_update_in_error_state(string reportType)
        {
            var reportingStandardDetail = new ReportingStandard
            {
                Id = "1",
                Name = reportType,
                Version = ReportingStandardVersion.Full,
                Type = reportType
            };
            _repository.Setup(repository => repository.Get(Guid.Empty, _periodId, CancellationToken.None)).ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId, null, null, reportingStandardDetail));

            var strategy = new DataScreenValueDataEventStrategy(_logger.Object, _mapper, _repository.Object, _userContext.Object);
            var requestMessage = GetFailedRequestMessage();

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public void ToEntry_IntValue_ReturnsPrimitive()
        {
            int inputValue = 42;
            DynamoDBEntry result = _converter.ToEntry(inputValue);
            Assert.Equal("42", ((Primitive)result).AsString());
        }

        [Fact]
        public void ToEntry_StringValue_ReturnsPrimitive()
        {
            string inputValue = "test string";
            DynamoDBEntry result = _converter.ToEntry(inputValue);
            Assert.Equal("test string", ((Primitive)result).AsString());
        }

        [Fact]
        public void ToEntry_BoolValue_ReturnsDynamoDBBool()
        {
            bool inputValue = true;
            var expected = new DynamoDBBool(inputValue);
            var actual = _converter.ToEntry(inputValue);
            Assert.Equal(expected, actual);
        }

        [Fact]
        public void ToEntry_DoubleValue_ReturnsPrimitive()
        {
            double inputValue = 123.45;
            DynamoDBEntry result = _converter.ToEntry(inputValue);
            Assert.Equal("123.45", ((Primitive)result).AsString());
        }

        [Fact]
        public void FromEntry_PrimitiveWithIntValue_ReturnsInt()
        {
            var primitiveEntry = new Primitive("42", true);
            var result = _converter.FromEntry(primitiveEntry);
            Assert.Equal((double)42, result);
        }

        [Fact]
        public void FromEntry_DynamoDBBool_ReturnsBool()
        {
            var boolEntry = new DynamoDBBool(true);
            var result = _converter.FromEntry(boolEntry);
            Assert.IsType<bool>(result);
            Assert.True((bool)result);
        }

        [Fact]
        public void FromEntry_PrimitiveWithStringValue_ReturnsString()
        {
            var primitiveEntry = new Primitive("test string");
            object result = _converter.FromEntry(primitiveEntry);
            Assert.Equal("test string", result);
        }

        [Fact]
        public void FromEntry_PrimitiveWithDoubleValue_ReturnsDouble()
        {
            var primitiveEntry = new Primitive("123.45", true);
            var result = _converter.FromEntry(primitiveEntry);
            Assert.IsType<double>(result);
            Assert.Equal(123.45, (double)result, precision: 2);
        }

        [Fact]
        public void FromEntry_PrimitiveWithJsonValue_DeserializesFromJson()
        {
            var inputValue = new { Name = "Test", Value = 100 };
            string jsonString = JsonConvert.SerializeObject(inputValue);
            var primitiveEntry = new Primitive(jsonString);
            var result = _converter.FromEntry(primitiveEntry);
            var deserializedValue = JsonConvert.DeserializeObject(jsonString);
            Assert.Equal(JsonConvert.SerializeObject(deserializedValue), result);
        }

        [Fact]
        public void FromEntry_NullEntry_ReturnsNull()
        {
            object result = _converter.FromEntry(new DynamoDBNull());
            Assert.Null(result);
        }

        [Fact]
        public void FromEntry_PrimitiveWithNonNumericString_ReturnsString()
        {
            var primitiveEntry = new Primitive("NotANumber");
            object result = _converter.FromEntry(primitiveEntry);
            Assert.Equal("NotANumber", result);
        }

        private string GetSuccessRequestMessage()
        {
            var message = new ScreenValuesResponseMessage
            {
                PreviousPeriodId = TestHelpers.Guids.GuidOne,
                PeriodId = TestHelpers.Guids.GuidTwo,
                ClientId = TestHelpers.Guids.GuidThree,
                TenantId = TestHelpers.Guids.GuidFour,
                CorrelationId = TestHelpers.Guids.GuidFour,
                Error = "",
                IsSuccessful = true,
                CurrentPeriod = new List<Iris.AccountsProduction.AccountsBuilder.Messages.ScreenValues.ScreenValues>()
                {
                    new Iris.AccountsProduction.AccountsBuilder.Messages.ScreenValues.ScreenValues()
                    {
                        ScreenId = "Test",
                        ReportMappingTable = "Test",
                        ScreenFields = new List<Iris.AccountsProduction.AccountsBuilder.Messages.ScreenValues.ScreenField>()
                        {
                            new Iris.AccountsProduction.AccountsBuilder.Messages.ScreenValues.ScreenField
                            {
                                Name = "Test",
                                Value = 4
                            }
                        }
                    }
                },
                PreviousPeriod = new List<Iris.AccountsProduction.AccountsBuilder.Messages.ScreenValues.ScreenValues>()
                {
                    new Iris.AccountsProduction.AccountsBuilder.Messages.ScreenValues.ScreenValues()
                    {
                        ScreenId = "Test2",
                        ReportMappingTable = "Test2",
                        ScreenFields = new List<Iris.AccountsProduction.AccountsBuilder.Messages.ScreenValues.ScreenField>()
                        {
                            new Iris.AccountsProduction.AccountsBuilder.Messages.ScreenValues.ScreenField
                            {
                                Name = "Test2",
                                Value = "Test2"
                            }
                        }
                    }
                },
            };

            return System.Text.Json.JsonSerializer.Serialize(message, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
        }

        private static string GetFailedRequestMessage()
        {
            var message = new ScreenValuesResponseMessage { IsSuccessful = false, Error = "error" };

            return System.Text.Json.JsonSerializer.Serialize(message, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
        }
    }
}