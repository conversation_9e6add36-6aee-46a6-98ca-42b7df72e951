﻿using Iris.AccountsProduction.Common.Toolkit.Exceptions;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.Api.Filters
{
    [ExcludeFromCodeCoverage]
    public class ModelStateActionFilter : IAsyncActionFilter
    {
        public ModelStateActionFilter()
        {

        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            if (!context.ModelState.IsValid)
            {
                throw new ValidationsException(context.ModelState);
            }

            await next();
        }
    }
}
