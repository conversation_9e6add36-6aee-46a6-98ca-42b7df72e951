﻿using AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations.BalanceSheet
{
    public class BalanceSheetCHConfiguration : IEntityTypeConfiguration<BalanceSheetCH>
    {
        public void Configure(EntityTypeBuilder<BalanceSheetCH> builder)
        {
            builder.ToTable("BalanceSheetCH", "public");

            builder.<PERSON><PERSON>ey(e => new { e.ClientId, e.AccountPeriodId })
                .HasName("balancesheetch_pk");

            builder.HasOne(d => d.BalanceSheetGeneral)
                   .WithOne(p => p.BalanceSheetCH)
                   .HasForeignKey<BalanceSheetCH>(d => new { d.ClientId, d.AccountPeriodId })
                   .OnDelete(DeleteBehavior.Cascade)
                   .HasConstraintName("balancesheetch_fk");
        }
    }
}
