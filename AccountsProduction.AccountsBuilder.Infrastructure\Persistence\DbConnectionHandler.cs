﻿using Microsoft.Extensions.Configuration;
using Npgsql;
using System.Data.Common;
using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.Infrastructure.Persistence
{
    [ExcludeFromCodeCoverage]
    public static class DbConnectionHandler
    {
        public static DbConnection CreateDbConnection(IConfiguration configuration)
        {
            var connectionString = DbConnectionStringFactory.ConnectionString(configuration);
            var connection = new NpgsqlConnection(connectionString)
            {
                ProvidePasswordCallback = DbConnectionStringFactory.PasswordCallback()
            };
            return connection;
        }
    }
}