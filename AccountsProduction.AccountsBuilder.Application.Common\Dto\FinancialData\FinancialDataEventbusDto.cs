﻿namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData
{
    public class FinancialDataEventbusDto : FinancialDataEventbusBaseDto
    {
        public List<FinancialDto> Data { get; set; } = [];
    }

    public class ErrorFinancialDataEventbusDto : FinancialDataEventbusBaseDto
    {
        public FinancialDataExceptionDetailsDto? ErrorMessage { get; set; } = null!;
    }

    public class FinancialDataEventbusBaseDto
    {
        public Guid ProcessId { get; set; }
    }
}