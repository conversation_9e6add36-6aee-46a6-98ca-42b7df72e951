﻿using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.Helper;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Helper
{
    public class DplMappingHelperTest
    {

        public class When_Passing_Reporting_Type_Get_Correct_Dpl : DplMappingHelperTest
        {
            [Fact]
            public void Should_return_102a1_as_report_type_is_120()
            {
                var result = DplMappingHelper.ReportingTrialBalanceEventType(ReportStandardType.FRS102_1A);
                result.ShouldBe(ReportingTrialBalanceEventType.FRS1021A);
            }

            [Fact]
            public void Should_return_105_as_report_type_is_105()
            {
                var result = DplMappingHelper.ReportingTrialBalanceEventType(ReportStandardType.FRS105);
                result.ShouldBe(ReportingTrialBalanceEventType.FRS105);
            }

            [Fact]
            public void Should_return_unincorporated_as_report_type_is_unincorporated()
            {
                var result = DplMappingHelper.ReportingTrialBalanceEventType(ReportStandardType.UNINCORPORATED);
                result.ShouldBe(ReportingTrialBalanceEventType.UNINCORPORATED);
            }
        }
    }
}
