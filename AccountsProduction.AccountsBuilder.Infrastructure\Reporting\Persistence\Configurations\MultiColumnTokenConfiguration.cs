﻿using AccountsProduction.AccountsBuilder.Domain.Reporting.Other;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations;

public class MultiColumnTokenConfiguration : IEntityTypeConfiguration<MultiColumnToken>
{
    public void Configure(EntityTypeBuilder<MultiColumnToken> builder)
    {
        builder.ToTable("MultiColumnToken", "public");
        builder.Property(e => e.Id).ValueGeneratedOnAdd();

        builder.Property(e => e.AssignedToken).HasColumnType("character");
        builder.Property(e => e.TokenCount).HasColumnType("numeric");
        builder.Property(e => e.Token1).HasColumnType("character");
        builder.Property(e => e.Token2).HasColumnType("character");
        builder.Property(e => e.Token3).HasColumnType("character");
        builder.Property(e => e.Token4).HasColumnType("character");
        builder.Property(e => e.Token5).HasColumnType("character");
        builder.Property(e => e.Token6).HasColumnType("character");
        builder.Property(e => e.Token7).HasColumnType("character");
        builder.Property(e => e.Token8).HasColumnType("character");
        builder.Property(e => e.Token9).HasColumnType("character");

        builder.HasOne(d => d.ReportingPeriod)
        .WithMany(p => p!.MultiColumnToken)
        .HasForeignKey(d => new { d.ClientId, d.AccountPeriodId })
        .OnDelete(DeleteBehavior.ClientSetNull)
        .HasConstraintName("multicolumntoken_fk");
    }
}
