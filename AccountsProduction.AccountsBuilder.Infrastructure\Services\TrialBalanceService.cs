﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Application.Common.Helper;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using Flurl;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AccountsProduction.AccountsBuilder.Infrastructure.Services
{
    public class TrialBalanceService : ITrialBalanceService
    {
        private readonly HttpClient _httpClient;
        private readonly IEnvVariableProvider _envVariableProvider;
        private readonly UserContext _userContext;
        private readonly ILogger<TrialBalanceService> _logger;


        public TrialBalanceService(IEnvVariableProvider envVariableProvider,
            HttpClient httpClient,
            UserContext userContext,
            ILogger<TrialBalanceService> logger)
        {
            _envVariableProvider = envVariableProvider;
            _httpClient = httpClient;
            _userContext = userContext;
            _logger = logger;
            _httpClient.SetupIrisClient(_envVariableProvider.TrialBalanceApiKey, _envVariableProvider.TrialBalanceApiId, _envVariableProvider.TrialBalanceApiScheme, _envVariableProvider.TrialBalanceApiHost);
        }


        public async Task<TrialBalanceDto> GetTrialBalanceCalculate(Guid clientId, Guid accountPeriodId)
        {
            var trialBalanceFullUrl = GetFullUrl(string.Format(Constants.TrialBalanceApi.TrialBalanceCalculateEndpoint, clientId, accountPeriodId));

            var httpRequestMessage = _httpClient.CreateGetSignedRequest(trialBalanceFullUrl, _envVariableProvider);
            httpRequestMessage.AddRequestHeaders(_userContext);


            var response = await _httpClient.SendAsync(httpRequestMessage);

            response.EnsureSuccessStatusCode();

            var contentString = await response.Content.ReadAsStringAsync();

            _logger.LogInformation("TrialBalance response received");

            var trialBalanceDto = JsonSerializer.Deserialize<TrialBalanceDto>(contentString, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return trialBalanceDto;
        }

        private string GetFullUrl(string path)
        {
            return _httpClient.BaseAddress!.ToString().AppendPathSegment(path).ToString();
        }
    }
}