﻿using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper;
using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using AccountsProduction.AccountsBuilder.UnitTests.Reporting.StaticData;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.AutoMapper.Mappers
{
    public class NotesMapperTests
    {

        private readonly IMapper _mapper;

        public NotesMapperTests()
        {
            _mapper = AutoMapperConfiguration.GetConfiguredAutoMapper();
        }

        [Fact]
        public void Should_map_AccountingPoliciesResponseDataMessage_object_to_note_accounting_policies_list()
        {
            var accountingPoliciesResponseDataMessage = Data.GetAccountingPoliciesNotes();

            var result = _mapper.Map<List<NoteAccountingPolicies>>(accountingPoliciesResponseDataMessage);

            result.Count.ShouldBe(60);
            result.SingleOrDefault(x => x.NoteType == "ConsolidatedAccountsExemptionSection" && x.NoteText == "2").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ConsolidatedAccountsExemptionParentName" && x.NoteText == "ParentName").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ConsolidatedAccountsExemptionParentAddress" && x.NoteText == "ParentAddress").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "PresentationCcyAcctgPolYesNo" && x.NoteText == "True").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ResearchAndDevAcctgPolYesNo" && x.NoteText == "True").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ForeignCurrenciesAcctgPolYesNo" && x.NoteText == "True").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ChangesInAccountingPoliciesText" && x.NoteText == "ChangesInAccountingPolicies").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "FinancialInstrumentsAcctgPolicyText" && x.NoteText == "FinancialInstrumentsAccountingPolicy").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "GovernmentGrantsAcctgPolicyText" && x.NoteText == "GovernmentGrantsAccountingPolicy").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "MembersTransactionsWithTheLlpText" && x.NoteText == "MembersTransactionsWithTheLlpText").ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "PlantAndMachineryEtcDescription" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.ClassNameCustomization).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ImprovementsToPropertyDescription" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.CategoryDescription).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ImprovementsToPropertyDepreciationPolicyAB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.AlternativeBasis).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ImprovementsToPropertyDepreciationPolicySL" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.StraightLineBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ImprovementsToPropertyDepreciationPolicyRB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.ReducingBalanceBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "PlantAndMachineryDescription" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.CategoryDescription).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "PlantAndMachineryDepreciationPolicyAB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.AlternativeBasis).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "PlantAndMachineryDepreciationPolicySL" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.StraightLineBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "PlantAndMachineryDepreciationPolicyRB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.ReducingBalanceBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "FixturesAndFittingsDescription" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.CategoryDescription).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "FixturesAndFittingsDepreciationPolicyAB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.AlternativeBasis).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "FixturesAndFittingsDepreciationPolicySL" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.StraightLineBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "FixturesAndFittingsDepreciationPolicyRB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.ReducingBalanceBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "MotorVehiclesDescription" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.CategoryDescription).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "MotorVehiclesDepreciationPolicyAB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.AlternativeBasis).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "MotorVehiclesDepreciationPolicySL" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.StraightLineBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "MotorVehiclesDepreciationPolicyRB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.ReducingBalanceBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ComputerEquipmentDescription" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.CategoryDescription).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ComputerEquipmentDepreciationPolicyAB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.AlternativeBasis).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ComputerEquipmentDepreciationPolicySL" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.StraightLineBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ComputerEquipmentDepreciationPolicyRB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.ReducingBalanceBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "GoodwillDescription" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.Goodwill.CategoryDescription).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "GoodwillAmortisationPolicyAB" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.Goodwill.AlternativeBasis).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "GoodwillAmortisationPolicySL" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.Goodwill.StraightLineBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "GoodwillAmortisationPolicyRB" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.Goodwill.ReducingBalanceBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "PatentsAndLicencesDescription" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.PatentsAndLicenses.CategoryDescription).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "PatentsAndLicencesAmortisationPolicyAB" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.PatentsAndLicenses.AlternativeBasis).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "PatentsAndLicencesAmortisationPolicySL" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.PatentsAndLicenses.StraightLineBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "PatentsAndLicencesAmortisationPolicyRB" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.PatentsAndLicenses.ReducingBalanceBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "DevelopmentCostsDescription" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.DevelopmentCosts.CategoryDescription).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "DevelopmentCostsAmortisationPolicyAB" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.DevelopmentCosts.AlternativeBasis).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "DevelopmentCostsAmortisationPolicySL" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.DevelopmentCosts.StraightLineBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "DevelopmentCostsAmortisationPolicyRB" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.DevelopmentCosts.ReducingBalanceBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ComputerSoftwareDescription" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.ComputerSoftware.CategoryDescription).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ComputerSoftwareAmortisationPolicyAB" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.ComputerSoftware.AlternativeBasis).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ComputerSoftwareAmortisationPolicySL" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.ComputerSoftware.StraightLineBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ComputerSoftwareAmortisationPolicyRB" && x.NoteText == accountingPoliciesResponseDataMessage.IntangibleAssets.ComputerSoftware.ReducingBalanceBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "LandAndBuildingsDescription" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.LandAndBuildings.ClassNameCustomization).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "FreeholdPropertyDescription" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.CategoryDescription).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "FreeholdPropertyDepreciationPolicyAB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.AlternativeBasis).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "FreeholdPropertyDepreciationPolicySL" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.StraightLineBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "FreeholdPropertyDepreciationPolicyRB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.ReducingBalanceBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ShortLeaseholdPropertyDescription" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.CategoryDescription).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ShortLeaseholdPropertyDepreciationPolicyAB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.AlternativeBasis).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ShortLeaseholdPropertyDepreciationPolicySL" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.StraightLineBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "ShortLeaseholdPropertyDepreciationPolicyRB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.ReducingBalanceBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "LongLeaseholdPropertyDescription" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.CategoryDescription).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "LongLeaseholdPropertyDepreciationPolicyAB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.AlternativeBasis).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "LongLeaseholdPropertyDepreciationPolicySL" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.StraightLineBasis.ToString()).ShouldNotBeNull();
            result.SingleOrDefault(x => x.NoteType == "LongLeaseholdPropertyDepreciationPolicyRB" && x.NoteText == accountingPoliciesResponseDataMessage.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.ReducingBalanceBasis.ToString()).ShouldNotBeNull();
        }

        [Theory, MemberData(nameof(InputTestCases))]
        public void Should_return_no_records_when_map_empty_AccountingPoliciesResponseDataMessage_object_to_note_accounting_policies_list(AccountingPoliciesResponseDataMessage data, int expected)
        {
            var actual = _mapper.Map<List<NoteAccountingPolicies>>(data);

            actual.Count.ShouldBe(expected);
        }

        [Fact]
        public void Should_map_notesDto_object_to_note_balance_sheet_list()
        {
            var noteDto = new NotesResponseDataMessage
            {
                AverageNumberOfEmployees = new AverageNumberOfEmployeesMessage
                {
                    PreviousPeriod = 123,
                    CurrentPeriod = 332
                },
                OperatingProfitLoss = new OperatingProfitLossMessage
                {
                    IsEnabled = true,
                    Items = new List<OperatingProfitLossItemMessage>
                    {
                        new OperatingProfitLossItemMessage
                        {
                            Index = 1,
                            Description = "Description1",
                            Value = 1
                        }
                    }
                },
                OffBalanceSheetArrangements = "text1",
                AdvancesCreditAndGuaranteesGrantedToDirectors = "text2",
                AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = new AdvancesCreditAndGuaranteesGrantedToDirectorsMessage
                {
                    Items = new List<AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage>{
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage
                            {
                                Index = 1,
                                InvolvementClientGuid = TestHelpers.Guids.GuidOne,
                                DirectorName = "Test director 1",
                                BalanceOutstandingAtStartOfYear = (decimal)1.1,
                                AmountsAdvanced = (decimal)1.2,
                                AmountsRepaid = (decimal)1.3,
                                AmountsWrittenOff = (decimal) 1.4,
                                AmountsWaived = (decimal) 1.5,
                                BalanceOutstandingAtEndOfYear = (decimal) 1.6,
                                AdvanceCreditConditions = "Added text 1"
                            },
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage
                            {
                                Index = 2,
                                InvolvementClientGuid = TestHelpers.Guids.GuidTwo,
                                DirectorName = "Test director 2",
                                BalanceOutstandingAtStartOfYear = (decimal)2.1,
                                AmountsAdvanced = (decimal)2.2,
                                AmountsRepaid = (decimal)2.3,
                                AmountsWrittenOff = (decimal) 2.4,
                                AmountsWaived = (decimal) 2.5,
                                BalanceOutstandingAtEndOfYear = (decimal) 2.6,
                                AdvanceCreditConditions = "Added text 2"
                            },
                        },
                    Guarantees = "guarantee text"
                },
                GuaranteesAndOtherFinancialCommitments = "text3",
                RelatedPartyTransactions = "text4",
                MembersLiabilityText = new MembersLiabilityTextMessage
                {
                    NoteText = "MembersLiabilityTextText",
                    NoteTitle = "MembersLiabilityTextTitle"
                },
                AdditionalNote1 = new AdditionalNoteMessage
                {
                    NoteText = "text5",
                    NoteTitle = "text6"
                },
                AdditionalNote2 = new AdditionalNoteMessage
                {
                    NoteText = "text7",
                    NoteTitle = "text8"
                },
                ControllingPartyNote = "text9",
                TangibleFixedAssetsNotes = new TangibleFixedAssetsNotesMessage
                {
                    AnalysisOfCostOrValuation = null,
                    HistoricalCostBreakdown = null,
                    ValuationInCurrentReportingPeriod = null,
                }
            };

            var result = _mapper.Map<List<NoteBalanceSheet>>(noteDto);

            result.Count.ShouldBe(0);
        }

        [Fact]
        public void Should_map_notesDto_object_to_note_with_assets_balance_sheet_list()
        {
            var noteDto = new NotesResponseDataMessage
            {
                AverageNumberOfEmployees = new AverageNumberOfEmployeesMessage
                {
                    PreviousPeriod = 123,
                    CurrentPeriod = 332
                },
                OperatingProfitLoss = new OperatingProfitLossMessage
                {
                    IsEnabled = true,
                    Items = new List<OperatingProfitLossItemMessage>
                    {
                        new OperatingProfitLossItemMessage
                        {
                            Index = 1,
                            Description = "Description1",
                            Value = 1
                        }
                    }
                },
                OffBalanceSheetArrangements = "text1",
                AdvancesCreditAndGuaranteesGrantedToDirectors = "text2",
                AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = new AdvancesCreditAndGuaranteesGrantedToDirectorsMessage
                {
                    Items = new List<AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage>{
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage
                            {
                                Index = 1,
                                InvolvementClientGuid = TestHelpers.Guids.GuidOne,
                                DirectorName = "Test director 1",
                                BalanceOutstandingAtStartOfYear = (decimal)1.1,
                                AmountsAdvanced = (decimal)1.2,
                                AmountsRepaid = (decimal)1.3,
                                AmountsWrittenOff = (decimal) 1.4,
                                AmountsWaived = (decimal) 1.5,
                                BalanceOutstandingAtEndOfYear = (decimal) 1.6,
                                AdvanceCreditConditions = "Added text 1"
                            },
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage
                            {
                                Index = 2,
                                InvolvementClientGuid = TestHelpers.Guids.GuidTwo,
                                DirectorName = "Test director 2",
                                BalanceOutstandingAtStartOfYear = (decimal)2.1,
                                AmountsAdvanced = (decimal)2.2,
                                AmountsRepaid = (decimal)2.3,
                                AmountsWrittenOff = (decimal) 2.4,
                                AmountsWaived = (decimal) 2.5,
                                BalanceOutstandingAtEndOfYear = (decimal) 2.6,
                                AdvanceCreditConditions = "Added text 2"
                            },
                        },
                    Guarantees = "guarantee text"
                },
                GuaranteesAndOtherFinancialCommitments = "text3",
                RelatedPartyTransactions = "text4",
                MembersLiabilityText = new MembersLiabilityTextMessage
                {
                    NoteText = "MembersLiabilityTextText",
                    NoteTitle = "MembersLiabilityTextTitle"
                },
                AdditionalNote1 = new AdditionalNoteMessage
                {
                    NoteText = "text5",
                    NoteTitle = "text6"
                },
                AdditionalNote2 = new AdditionalNoteMessage
                {
                    NoteText = "text7",
                    NoteTitle = "text8"
                },
                ControllingPartyNote = "text9",
                IntangibleAssetsRevaluation = "text10",
                TangibleFixedAssetsNotes = new TangibleFixedAssetsNotesMessage
                {
                    AnalysisOfCostOrValuation = new AnalysisOfCostOrValuationMessage
                    {
                        AnalysisOfCostOrValuationItems = new List<AnalysisOfCostOrValuationItemMessage> {
                            new AnalysisOfCostOrValuationItemMessage {
                                Index = 0,
                            }
                        },
                        CostLandAndBuildings = 99.99m,
                        CostPlantAndMachineryEtc = 99.99m,
                        TotalLandAndBuildings = 99.99m,
                        TotalPlantAndMachineryEtc = 99.99m,
                    },
                    HistoricalCostBreakdown = new HistoricalCostBreakdownMessage
                    {
                        CurrentReportingPeriodAccumulatedDepreciation = 99.99m,
                        CurrentReportingPeriodCost = 99.99m,
                        RevaluedAssetClass = "text11",
                        RevaluedClassPronoun = "text12"
                    },
                    ValuationInCurrentReportingPeriod = new ValuationInCurrentReportingPeriodMessage
                    {
                        ValuationDetails = "text13",
                        IndependentValuerInvolved = true,
                        RevaluationBasis = "text14"
                    },
                },
                LoansAndOtherDebtsDueToMembers = "text15",
            };

            var result = _mapper.Map<List<NoteBalanceSheet>>(noteDto);

            result.Count.ShouldBe(14);
        }

        [Fact]
        public void Should_map_notesDto_object_to_note_other_list()
        {
            var noteDto = new NotesResponseDataMessage
            {
                AverageNumberOfEmployees = new AverageNumberOfEmployeesMessage
                {
                    PreviousPeriod = 123,
                    CurrentPeriod = 332
                },
                OperatingProfitLoss = new OperatingProfitLossMessage
                {
                    IsEnabled = true,
                    Items = new List<OperatingProfitLossItemMessage>
                    {
                        new OperatingProfitLossItemMessage
                        {
                            Index = 1,
                            Description = "Description1",
                            Value = 1
                        }
                    }
                },
                OffBalanceSheetArrangements = "text1",
                AdvancesCreditAndGuaranteesGrantedToDirectors = "text2",
                AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = new AdvancesCreditAndGuaranteesGrantedToDirectorsMessage
                {
                    Items = new List<AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage>{
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage
                            {
                                Index = 1,
                                InvolvementClientGuid = TestHelpers.Guids.GuidOne,
                                DirectorName = "Test director 1",
                                BalanceOutstandingAtStartOfYear = (decimal)1.1,
                                AmountsAdvanced = (decimal)1.2,
                                AmountsRepaid = (decimal)1.3,
                                AmountsWrittenOff = (decimal) 1.4,
                                AmountsWaived = (decimal) 1.5,
                                BalanceOutstandingAtEndOfYear = (decimal) 1.6,
                                AdvanceCreditConditions = "Added text 1"
                            }
                        },
                    Guarantees = "guarantee text"
                },
                GuaranteesAndOtherFinancialCommitments = "text3",
                RelatedPartyTransactions = "text4",
                MembersLiabilityText = new MembersLiabilityTextMessage
                {
                    NoteText = "MembersLiabilityTextText",
                    NoteTitle = "MembersLiabilityTextTitle"
                },
                AdditionalNote1 = new AdditionalNoteMessage
                {
                    NoteText = "text5",
                    NoteTitle = "text6"
                },
                AdditionalNote2 = new AdditionalNoteMessage
                {
                    NoteText = "text7",
                    NoteTitle = "text8"
                },
                ControllingPartyNote = "text9"
            };

            var result = _mapper.Map<List<NoteOther>>(noteDto);

            result.Count.ShouldBe(17);

            result[0].NoteType.ShouldBe("AdvancesCreditGrantedDirector1Name");
            result[0].NoteText.ShouldBe("Test director 1");
            result[1].NoteType.ShouldBe("AdvancesCreditGrantedDirector1AmountsBfwd");
            result[1].NoteValue.ShouldBe((decimal)1.1);
            result[2].NoteType.ShouldBe("AdvancesCreditGrantedDirector1AmountsAdvanced");
            result[2].NoteValue.ShouldBe((decimal)1.2);
            result[3].NoteType.ShouldBe("AdvancesCreditGrantedDirector1AmountsRepaid");
            result[3].NoteValue.ShouldBe((decimal)1.3);
            result[4].NoteType.ShouldBe("AdvancesCreditGrantedDirector1AmountsWOff");
            result[4].NoteValue.ShouldBe((decimal)1.4);
            result[5].NoteType.ShouldBe("AdvancesCreditGrantedDirector1AmountsWaived");
            result[5].NoteValue.ShouldBe((decimal)1.5);
            result[6].NoteType.ShouldBe("AdvancesCreditGrantedDirector1AmountsCfwd");
            result[6].NoteValue.ShouldBe((decimal)1.6);
            result[7].NoteType.ShouldBe("AdvancesCreditGrantedDirector1Details");
            result[7].NoteText.ShouldBe("Added text 1");
            result[8].NoteType.ShouldBe("GuaranteesGrantedToDirectorsText");
            result[8].NoteText.ShouldBe("guarantee text");
            result[9].NoteType.ShouldBe("OffBalanceSheetArrangements");
            result[9].NoteText.ShouldBe("text1");
            result[10].NoteType.ShouldBe("AdvancesCreditAndGuaranteesGrantedToDirectors");
            result[10].NoteText.ShouldBe("text2");
            result[11].NoteType.ShouldBe("GuaranteesAndOtherFinancialCommitments");
            result[11].NoteText.ShouldBe("text3");
            result[12].NoteType.ShouldBe("RelatedPartyTransactions");
            result[12].NoteText.ShouldBe("text4");
            result[13].NoteType.ShouldBe("ControllingPartyNote");
            result[13].NoteText.ShouldBe("text9");

            result[14].NoteType.ShouldBe("MembersLiabilityText");
            result[14].NoteTitle.ShouldBe("MembersLiabilityTextTitle");
            result[14].NoteText.ShouldBe("MembersLiabilityTextText");

            result[15].NoteType.ShouldBe("AdditionalNote1");
            result[15].NoteTitle.ShouldBe("text6");
            result[15].NoteText.ShouldBe("text5");

            result[16].NoteType.ShouldBe("AdditionalNote2");
            result[16].NoteTitle.ShouldBe("text8");
            result[16].NoteText.ShouldBe("text7");
        }

        [Fact]
        public void Should_map_notesDto_object_to_note_profit_and_loss_list()
        {
            var noteDto = new NotesResponseDataMessage
            {
                AverageNumberOfEmployees = new AverageNumberOfEmployeesMessage
                {
                    PreviousPeriod = 123,
                    CurrentPeriod = 332
                },
                OperatingProfitLoss = new OperatingProfitLossMessage
                {
                    IsEnabled = true,
                    Items = new List<OperatingProfitLossItemMessage>
                    {
                        new OperatingProfitLossItemMessage
                        {
                            Index = 1,
                            Description = "Description1",
                            Value = 1
                        }
                    }
                },
                OffBalanceSheetArrangements = "text1",
                AdvancesCreditAndGuaranteesGrantedToDirectors = "text2",
                GuaranteesAndOtherFinancialCommitments = "text3",
                RelatedPartyTransactions = "text4",
                MembersLiabilityText = new MembersLiabilityTextMessage
                {
                    NoteText = "MembersLiabilityTextText",
                    NoteTitle = "MembersLiabilityTextTitle"
                },
                AdditionalNote1 = new AdditionalNoteMessage
                {
                    NoteText = "text5",
                    NoteTitle = "text6"
                },
                AdditionalNote2 = new AdditionalNoteMessage
                {
                    NoteText = "text7",
                    NoteTitle = "text8"
                },
                ControllingPartyNote = "text9"
            };

            var result = _mapper.Map<List<NoteProfitAndLoss>>(noteDto);

            result.Count.ShouldBe(4);
            result[0].NoteType.ShouldBe("AverageNumberOfEmployees");
            result[0].NoteValue.ShouldBe(332);
            result[1].NoteType.ShouldBe("PreviousAverageNumberOfEmployees");
            result[1].NoteValue.ShouldBe(123);
            result[2].NoteType.ShouldBe("OperatingProfitNoteYesNo");
            result[2].NoteText.ShouldBe("Yes");
            result[3].NoteType.ShouldBe("OperatingProfitExtraItem1");
            result[3].NoteText.ShouldBe("Description1");
            result[3].NoteValue.ShouldBe(1);
        }


        [Fact]
        public void Should_not_map_notesDto_object_to_note_profit_and_loss_list_when_empty()
        {
            var noteDto = new NotesResponseDataMessage
            {
                AverageNumberOfEmployees = new AverageNumberOfEmployeesMessage
                {
                    PreviousPeriod = null,
                    CurrentPeriod = null
                },
                OperatingProfitLoss = null,
                OffBalanceSheetArrangements = "text1",
                AdvancesCreditAndGuaranteesGrantedToDirectors = "text2",
                GuaranteesAndOtherFinancialCommitments = "text3",
                RelatedPartyTransactions = "text4",
                MembersLiabilityText = new MembersLiabilityTextMessage
                {
                    NoteText = "MembersLiabilityTextText",
                    NoteTitle = "MembersLiabilityTextTitle"
                },
                AdditionalNote1 = new AdditionalNoteMessage
                {
                    NoteText = "text5",
                    NoteTitle = "text6"
                },
                AdditionalNote2 = new AdditionalNoteMessage
                {
                    NoteText = "text7",
                    NoteTitle = "text8"
                },
                ControllingPartyNote = "text9"
            };

            var result = _mapper.Map<List<NoteProfitAndLoss>>(noteDto);

            result.Count.ShouldBe(0);
        }

        [Fact]
        public void Should_map_noteAccountingPoliciesDto_object_to_note_balance_sheet_list()
        {
            var noteDto = new AccountingPoliciesResponseDataMessage
            {
                GoodwillMaterial = true
            };

            var result = _mapper.Map<List<NoteBalanceSheet>>(noteDto);

            result.Count.ShouldBe(1);
            result.SingleOrDefault(x => x.NoteType == "GoodwillMaterialYesNo" && x.NoteText == "True").ShouldNotBeNull();
        }

        public static IEnumerable<object[]> InputTestCases => new List<object[]>
        {
            new object[]
            {
                new AccountingPoliciesResponseDataMessage(), 0
            },
            new object[]
            {
                new AccountingPoliciesResponseDataMessage
                {
                    IntangibleAssets = new IntangibleAssetsMessage(),
                    TangibleFixedAssets = new TangibleFixedAssetsMessage()
                }, 0
            },
            new object[]
            {
                new AccountingPoliciesResponseDataMessage
                {
                    IntangibleAssets = new IntangibleAssetsMessage
                    {
                        ComputerSoftware = new AssetsAdjustmentMessage(),
                        DevelopmentCosts = new AssetsAdjustmentMessage(),
                        Goodwill = new AssetsAdjustmentMessage(),
                        PatentsAndLicenses = new AssetsAdjustmentMessage()
                    },
                    TangibleFixedAssets = new TangibleFixedAssetsMessage
                    {
                        LandAndBuildings = new LandAndBuildingsMessage(),
                        PlantAndMachinery = new PlantAndMachineriesMessage()
                    }
                }, 0
            },
            new object[]
            {
                new AccountingPoliciesResponseDataMessage
                {
                    IntangibleAssets = new IntangibleAssetsMessage
                    {
                        ComputerSoftware = new AssetsAdjustmentMessage(),
                        DevelopmentCosts = new AssetsAdjustmentMessage(),
                        Goodwill = new AssetsAdjustmentMessage(),
                        PatentsAndLicenses = new AssetsAdjustmentMessage()
                    },
                    TangibleFixedAssets = new TangibleFixedAssetsMessage
                    {
                        LandAndBuildings = new LandAndBuildingsMessage
                        {
                            ClassNameCustomization = null,
                            FreeholdProperty = new AssetsAdjustmentMessage(),
                            LongLeaseholdProperty = new AssetsAdjustmentMessage(),
                            ShortLeaseholdProperty = new AssetsAdjustmentMessage()
                        },
                        PlantAndMachinery = new PlantAndMachineriesMessage
                        {
                            ClassNameCustomization = null,
                            PlantAndMachinery = new AssetsAdjustmentMessage(),
                            ComputerEquipment = new AssetsAdjustmentMessage(),
                            FixturesAndFittings = new AssetsAdjustmentMessage(),
                            ImprovementsToProperty = new AssetsAdjustmentMessage(),
                            MotorVehicles = new AssetsAdjustmentMessage()
                        }
                    }
                }, 0
            }
        };
    }
}
