﻿namespace AccountsProduction.AccountsBuilder.Reporting.Domain
{
    public class Member
    {
        public int Id { get; set; }

        public Guid ClientId { get; set; }

        public Guid InvolvementUUID { get; set; }

        public string? MemberType { get; set; }

        public string? MemberSubtype { get; set; }

        public string? PersonTitle { get; set; }

        public string? PersonFirstName { get; set; }

        public string? PersonSurname { get; set; }

        public string? EntityName { get; set; }

        public DateTime? ActiveFrom { get; set; }

        public DateTime? ActiveTo { get; set; }

        public DateTime? DateOfDeath { get; set; }

        public virtual Client? Client { get; set; }

        public int InvolvementId { get; set; }

        public int? PDOCode { get; set; }

        public bool IsDeleted { get; set; }
    }
}
