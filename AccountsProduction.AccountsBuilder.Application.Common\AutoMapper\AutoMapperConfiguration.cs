﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AutoMapper;

namespace AccountsProduction.AccountsBuilder.Application.Common.AutoMapper
{
    public static class AutoMapperConfiguration
    {
        public static IMapper GetConfiguredAutoMapper()
        {
            var config = new MapperConfiguration(configuration =>
            {
                configuration.AddProfile(new AccountsBuilderMapper());
                configuration.AddProfile(new ReportingStandardMapper());
                configuration.AddProfile(new AccountPeriodMapper());
                configuration.AddProfile(new EntitySetupMapper());
                configuration.AddProfile(new LicenseDataMapper());
                configuration.AddProfile(new FinancialDataMapper());
                configuration.AddProfile(new TrialBalanceMapper());
                configuration.AddProfile(new SignatoryMapper());
                configuration.AddProfile(new DplDataMapper());
                configuration.AddProfile(new ProfitAndLossMapper());
                configuration.AddProfile(new NonFinancialDataMapper());
                configuration.AddProfile(new InvolvementMapper());
                configuration.AddProfile(new AddressMapper());
                configuration.AddProfile(new ClientAddressMapper());
                configuration.AddProfile(new BalanceSheetMapper());
                configuration.AddProfile(new OtherMapper());
                configuration.AddProfile(new ValidationMapper());
                configuration.AddProfile(new NotesMapper());
                configuration.AddProfile(new PracticeDetailsMapper());
                configuration.AddProfile(new FinancialDataCategoryMapper());
                configuration.AddProfile(new FinancialDataDrilldownMapper());
                configuration.AddProfile(new AccountingPoliciesMapper());
                configuration.AddProfile(new ProfitShareMapper());
                configuration.AddProfile(new DataScreensValueMapper());
                configuration.AddProfile(new DataScreenValuesMapper());
            });

            var mapper = config.CreateMapper();

            return mapper;
        }
    }
}