{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:13022/", "sslPort": 44317}}, "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "AccountsProduction.AccountsBuilder.Api": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "Logging__LogLevel__Default": "Debug", "AWS__Region": "eu-west-2", "AWS_DEFAULT_REGION": "eu-west-2", "AWS_REGION": "eu-west-2", "AWS_ACCOUNT_ID": "************", "AWS_ACCESS_KEY_ID": "********************", "AWS_SECRET_ACCESS_KEY": "qEa6YglkVNfTk9TMWOuKkRHvWWz5QZJ5YibgLJzc", "AWS_SESSION_TOKEN": "IQoJb3JpZ2luX2VjEMX//////////wEaCWV1LXdlc3QtMiJHMEUCIQDVVhW4LfnPW5xGV/WZAsQ1Hh5H1uxNh9CwdGddxvJptwIgDUGeCurUYfpl87suhALQ0LLELulKCyaADin6KMLh7TYqowIIjv//////////ARAEGgwyNDU2MzM5MzQ4MTIiDLTwvhGL34wa3oKlIir3AYvYq1ZYqgPvxPh5ydowb3fxhaqHIDNY8dFYuyeHuABhutP4rn67TPYXRZzUWCx6pXHGHlZRlkdqjvlZJO1niM4vRERca1XrvP615rENBI4a1osVvY6Ldsc+f98pK5XoyJG3e1Rt/GB9/mU0NjL53kF5teAUg3uKG4fbftraUffBA7S6iw8M4TItw8WjkZ49oViZKCyKDdLgL2RNUjAjzGIxg57Bm7RTXgDg++iGH7l1QpCVeeU9k+mQgJ+tpUccHNIeQ4HtCWpCBIwGnenIhniHq7L7Eo5i+Y4FiJxxV9PrOWX7mRrCUX9qOmIumcyfcNmaiejkYVUwx6XhwQY6nQFPrZz/HIO5BgSiscBoaB5feUEYmxeQ0IgLKWGS4gWjdKSPDWKAUW3WkPJ+E/YrjdC69MVvpJCyYJ3DKUY1xK5zXnLiLnDHfZr9h7o4gpRg1Rk4CK2hFmoel6RQRN7iZGaW/9avHcH4vJ7cdZP1iYTetEoaqMiBHfJn6hGGcP6CKJVLHmM9aSSgF5NxVs5y96GmhRJggbE2ny4DSok2", "AWS_SVC_ROLE_ARN": "arn:aws:sts::************:assumed-role/Developer/aniketsession", "AWS_Profile": "Development", "AWS__Profile": "Development", "ACCOUNT_PERIOD_API_HOST": "vpce-0243baf16b4d45d9b-orv6s6vv.execute-api.eu-west-2.vpce.amazonaws.com/v1", "ACCOUNT_PERIOD_API_ID": "2fjj3yrota", "ACCOUNT_PERIOD_API_KEY": "7LfTggYWY72nuOdBBrIRrgc1UhwqMxT1Gqy7groh", "ACCOUNT_PERIOD_API_SCHEME": "https", "DB_CONNECTION": "Server=localhost;Port=5432;Database=hubdb;User Id=********;Password=********;", "REPORTINGCORE_DB_CONNECTION": "Server=localhost;Port=5432;Database=coredb;User Id=********;Password=********;", "ACCOUNTS_PRODUCTION_DB_DATABASE_NAME": "accountsproduction_hub_db", "ACCOUNTS_PRODUCTION_DB_HOST": "accountsproduction-hub-dbs-instance-development.cluster-ckvo9ttwlplu.eu-west-2.rds.amazonaws.com", "ACCOUNTS_PRODUCTION_DB_PORT": "5432", "ACCOUNTS_PRODUCTION_DB_USERNAME": "accountsproduction_hub_db_user", "ACCOUNTSBUILDER_API_HOST": "vpce-0243baf16b4d45d9b-orv6s6vv.execute-api.eu-west-2.vpce.amazonaws.com/v1", "ACCOUNTSBUILDER_API_ID": "bnc83xp1ji", "ACCOUNTSBUILDER_API_KEY": "aG6VrQzlAm3cS7Z8Y3kOXa9Mn25GSTwq6ABCvOPE", "ACCOUNTSBUILDER_API_SCHEME": "https", "ACCOUNTSBUILDER_REPORTING_QUEUE": "https://sqs.eu-west-2.amazonaws.com/************/accountsproduction-accountsbuilder-reporting-sqs", "ACCOUNTSPRODUCTION_ACCOUNTPERIOD_REQUEST_TOPIC": "arn:aws:sns:eu-west-2:************:accountsproduction-accountperiod-request-topic", "ACCOUNTSPRODUCTION_ACCOUNTSBUILDER_INPUT_TOPIC": "arn:aws:sns:eu-west-2:************:accountsproduction-accountsbuilder-input-topic", "ACCOUNTSPRODUCTION_API_HOST": "vpce-0243baf16b4d45d9b-orv6s6vv.execute-api.eu-west-2.vpce.amazonaws.com/v1", "ACCOUNTSPRODUCTION_API_ID": "x2i0zl16zk", "ACCOUNTSPRODUCTION_API_KEY": "kU0tUSxSwU5z0Zu5ujNZb6dsH0e1efC455kQoTc5", "ACCOUNTSPRODUCTION_API_SCHEME": "https", "BUILD_NUMBER": "%build.number%", "CLIENT_ADDRESS_API_URL": "address/client/", "CLIENT_API_HOST": "vpce-0243baf16b4d45d9b-orv6s6vv.execute-api.eu-west-2.vpce.amazonaws.com/v1", "CLIENT_API_ID": "jsv7iud33f", "CLIENT_API_KEY": "SyAnWvAoQn3YALnLVuRUj1ZBfCtjNfZI1KxYEz7N", "CLIENT_API_SCHEME": "https", "CLIENT_API_URL": "client/", "CLIENT_INVOLVEMENT_API_URL": "involvement/client/", "CONCURRENCY_RETRY_COUNT": "3", "ENTITY_INVOLVEMENT_API_URL": "involvement/entity/", "REPORTING_TRIALBALANCE_REQUEST_TOPIC": "arn:aws:sns:eu-west-2:************:reporting-trialbalance-processing-request-topic", "TRIAL_BALANCE_API_HOST": "vpce-0243baf16b4d45d9b-orv6s6vv.execute-api.eu-west-2.vpce.amazonaws.com/v1", "TRIAL_BALANCE_API_ID": "2y1qwvx271", "TRIAL_BALANCE_API_KEY": "WxKJXO31oC1iS26QV9oee4ETfKPACF8W33GdEXMp", "TRIAL_BALANCE_API_SCHEME": "https", "EVENTBUS_AWS_ACCOUNT_ID": "************", "EVENT_BUS_MAIN_QUEUE_URL": "https://sqs.eu-west-2.amazonaws.com/************/platform-eventbus-main-queue", "EVENTBUS_LARGE_PAYLOADS_S3_BUCKET_NAME": "platform-eventbus-large-payloads"}, "applicationUrl": "https://localhost:5001;http://localhost:5000"}, "Mock Lambda Test Tool": {"commandName": "Executable", "executablePath": "%USERPROFILE%\\.dotnet\\tools\\dotnet-lambda-test-tool-8.0.exe", "commandLineArgs": "--port 5050", "workingDirectory": ".\\bin\\$(Configuration)\\net8.0"}}}