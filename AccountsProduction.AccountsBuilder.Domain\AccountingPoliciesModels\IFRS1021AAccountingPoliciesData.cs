﻿using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels.TangibleFixedAsset;

namespace AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels
{
    public class FRS1021AAccountingPoliciesData : IFRS1021AAccountingPoliciesData
    {
        ExemptionsFinancialStatements IFRS1021AAccountingPoliciesData.ExemptionsFinancialStatements { get; set; }
        TangibleFixedAssets IFRS1021AAccountingPoliciesData.TangibleFixedAssets { get; set; }
        IntangibleAssets IFRS1021AAccountingPoliciesData.IntangibleAssets { get; set; }
        string IFRS1021AAccountingPoliciesData.ChangesInAccountingPolicies { get; set; }
        string IFRS1021AAccountingPoliciesData.FinancialInstrumentsAccountingPolicy { get; set; }
        string IFRS1021AAccountingPoliciesData.GovernmentGrantsAccountingPolicy { get; set; }
        string IFRS1021AAccountingPoliciesData.MembersTransactionsWithTheLlpText { get; set; }
        bool? IFRS1021AAccountingPoliciesData.PresentationCurrency { get; set; }
        bool? IFRS1021AAccountingPoliciesData.ResearchAndDevelopment { get; set; }
        bool? IFRS1021AAccountingPoliciesData.ForeignCurrencies { get; set; }
        bool? IFRS1021AAccountingPoliciesData.GoodwillMaterial { get; set; }
    }
}
