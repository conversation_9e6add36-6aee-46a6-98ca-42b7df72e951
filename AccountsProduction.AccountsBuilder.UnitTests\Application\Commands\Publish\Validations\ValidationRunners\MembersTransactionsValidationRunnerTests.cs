﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.MembersTransactionsRunner;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations.ValidationRunners
{
    public class MembersTransactionsValidationRunnerTests
    {
        private readonly AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder _accountsBuilder;

        public MembersTransactionsValidationRunnerTests()
        {
            _accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder { AccountingPolicies = new AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels.AccountingPolicies() };
        }

        [Fact]
        public void Should_trigger_validation_if_no_accounts_found()
        {
            var issues = new MembersTransactionsValidationRunner().Validate(_accountsBuilder);

            var membersTransactionsIssue = issues.SingleOrDefault(x => x.Name == MembersTransactionsValidation.MembersTransactions);
            membersTransactionsIssue.ShouldNotBeNull();
            membersTransactionsIssue.ErrorCode.ShouldBe(MembersTransactionsValidation.MembersTransactionsConfig.ErrorCode);
            membersTransactionsIssue.DisplayName.ShouldBe(MembersTransactionsValidation.MembersTransactionsConfig.DisplayName);
            membersTransactionsIssue.ErrorCategory.ShouldBe(MembersTransactionsValidation.MembersTransactionsConfig.ErrorCategory);
            membersTransactionsIssue.Target.ShouldBe(MembersTransactionsValidation.MembersTransactionsConfig.Target);
            membersTransactionsIssue.Type.ShouldBe(MembersTransactionsValidation.MembersTransactionsConfig.Type);
            membersTransactionsIssue.Name.ShouldBe(MembersTransactionsValidation.MembersTransactionsConfig.Name);
            membersTransactionsIssue.Description.ShouldBe(MembersTransactionsValidation.MembersTransactionsConfig.Description);
            membersTransactionsIssue.Breadcrumb.ShouldBe("Accounts Builder > Sections FRS102 - Section 1A > Members transactions");
        }
    }
}
