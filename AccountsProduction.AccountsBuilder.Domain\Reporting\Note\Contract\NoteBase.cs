﻿namespace AccountsProduction.AccountsBuilder.Reporting.Domain.Note.Contract
{
    public abstract class NoteBase
    {
        public int Id { get; set; }

        public Guid ClientId { get; set; }

        public Guid AccountPeriodId { get; set; }

        public string? NoteType { get; set; }

        public string? NoteTitle { get; set; }

        public string? NoteText { get; set; }

        public decimal? NoteValue { get; set; }

        public string? ScreenId { get; set; }

        public virtual ReportingPeriod? ReportingPeriod { get; set; }
    }
}
