﻿namespace AccountsProduction.AccountsBuilder.Domain.NoteModels
{
    public interface IFRS105NotesData
    {
        AverageNumberOfEmployees AverageNumberOfEmployees { get; set; }

        string OffBalanceSheetArrangements { get; set; }

        string AdvancesCreditAndGuaranteesGrantedToDirectors { get; set; }

        string GuaranteesAndOtherFinancialCommitments { get; set; }

        MembersLiabilityText MembersLiabilityText { get; set; }

        AdditionalNote AdditionalNote1 { get; set; }

        AdditionalNote AdditionalNote2 { get; set; }

        string ControllingPartyNote { get; set; }

        string RelatedPartyTransactions { get; set; }
    }
}
