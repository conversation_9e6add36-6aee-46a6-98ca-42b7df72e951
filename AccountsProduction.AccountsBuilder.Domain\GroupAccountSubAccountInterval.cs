﻿namespace AccountsProduction.AccountsBuilder.Domain
{
    public class GroupAccountSubAccountInterval
    {
        public int Id { get; set; }
        public int GroupStructureCode { get; set; }
        public string AccountChartIdentifier { get; set; }
        public int GroupNo { get; set; }
        public int PartNo { get; set; }
        public int AccountIntervalFrom { get; set; }
        public int SubAccountIntervalFrom { get; set; }
        public int AccountIntervalTo { get; set; }
        public int SubAccountIntervalTo { get; set; }

        public bool IsAccountCodeSubAccountCodeInRange(int accountCode, int? subAccountCode)
        {
            if (subAccountCode.HasValue && SubAccountIntervalFrom != 0 && SubAccountIntervalTo != 0)
            {
                return AccountIntervalTo == accountCode && AccountIntervalFrom == AccountIntervalTo
                    && SubAccountIntervalFrom <= subAccountCode && subAccountCode <= SubAccountIntervalTo;
            }

            return AccountIntervalFrom <= accountCode && accountCode <= AccountIntervalTo;
        }
    }
}