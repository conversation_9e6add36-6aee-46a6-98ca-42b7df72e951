﻿using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Domain.AccountingPoliciesModels
{
    public class AccountingPoliciesTests
    {
        private readonly AccountingPoliciesData _currentAccountingPoliciesData;
        private readonly AccountingPoliciesData _previousAccountingPoliciesData;

        public AccountingPoliciesTests()
        {
            _currentAccountingPoliciesData = TestData.GetAccountingPoliciesDataForCurrentPeriod();
            _previousAccountingPoliciesData = TestData.GetAccountingPoliciesDataForPreviousPeriod();
        }

        [Fact]
        public void Should_map_accountingPolicies_from_currentPeriod_when_all_data_is_available()
        {
            var accountingPolicies = new AccountingPolicies
            {
                CurrentPeriodAccountingPolicies = _currentAccountingPoliciesData,
                PreviousPeriodAccountingPolicies = _previousAccountingPoliciesData
            };

            accountingPolicies.ExemptionsFinancialStatements.ShouldBe(_currentAccountingPoliciesData.ExemptionsFinancialStatements);
            accountingPolicies.ChangesInAccountingPolicies.ShouldBe(_currentAccountingPoliciesData.ChangesInAccountingPolicies);
            accountingPolicies.FinancialInstrumentsAccountingPolicy.ShouldBe(_currentAccountingPoliciesData.FinancialInstrumentsAccountingPolicy);
            accountingPolicies.GovernmentGrantsAccountingPolicy.ShouldBe(_currentAccountingPoliciesData.GovernmentGrantsAccountingPolicy);
            accountingPolicies.MembersTransactionsWithTheLlpText.ShouldBe(_currentAccountingPoliciesData.MembersTransactionsWithTheLlpText);
            accountingPolicies.PresentationCurrency.ShouldBe(_currentAccountingPoliciesData.PresentationCurrency);
            accountingPolicies.ForeignCurrencies.ShouldBe(_currentAccountingPoliciesData.ForeignCurrencies);
            accountingPolicies.ResearchAndDevelopment.ShouldBe(_currentAccountingPoliciesData.ResearchAndDevelopment);
            accountingPolicies.GoodwillMaterial.ShouldBe(_currentAccountingPoliciesData.GoodwillMaterial);
            accountingPolicies.IntangibleAssetsGoodwill.ShouldBeEquivalentTo(_currentAccountingPoliciesData.IntangibleAssets.Goodwill);
            accountingPolicies.IntangibleAssetsPatentsAndLicenses.ShouldBeEquivalentTo(_currentAccountingPoliciesData.IntangibleAssets.PatentsAndLicenses);
            accountingPolicies.IntangibleAssetsDevelopmentCosts.ShouldBeEquivalentTo(_currentAccountingPoliciesData.IntangibleAssets.DevelopmentCosts);
            accountingPolicies.IntangibleAssetsComputerSoftware.ShouldBeEquivalentTo(_currentAccountingPoliciesData.IntangibleAssets.ComputerSoftware);
            accountingPolicies.TangibleAssetsFreeholdProperty.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.FreeholdProperty);
            accountingPolicies.TangibleAssetsLongLeaseholdProperty.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty);
            accountingPolicies.TangibleAssetsShortLeaseholdProperty.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty);
            accountingPolicies.TangibleAssetsImprovementsToProperty.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty);
            accountingPolicies.TangibleAssetsPlantAndMachinery.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery);
            accountingPolicies.TangibleAssetsFixturesAndFittings.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings);
            accountingPolicies.TangibleAssetsMotorVehicles.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.MotorVehicles);
            accountingPolicies.TangibleAssetsComputerEquipment.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment);
            //Assert all default properties below for coverage
            accountingPolicies.CurrentPeriodAccountingPolicies.ShouldBeEquivalentTo(_currentAccountingPoliciesData);
            accountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ShouldBeEquivalentTo(_currentAccountingPoliciesData.IntangibleAssets);
            accountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.ShouldBeEquivalentTo(_currentAccountingPoliciesData.IntangibleAssets.DevelopmentCosts);
            accountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.ShouldBeEquivalentTo(_currentAccountingPoliciesData.IntangibleAssets.ComputerSoftware);
            accountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.Goodwill.ShouldBeEquivalentTo(_currentAccountingPoliciesData.IntangibleAssets.Goodwill);
            accountingPolicies.CurrentPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.ShouldBeEquivalentTo(_currentAccountingPoliciesData.IntangibleAssets.PatentsAndLicenses);
            accountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets);
            accountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings);
            accountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.FreeholdProperty);
            accountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty);
            accountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty);
            accountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery);
            accountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery);
            accountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment);
            accountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings);
            accountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty);
            accountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.MotorVehicles);
            accountingPolicies.PreviousPeriodAccountingPolicies.ShouldBeEquivalentTo(_previousAccountingPoliciesData);
            accountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ClassNameCustomization.ShouldBe(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ClassNameCustomization);
            accountingPolicies.CurrentPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ClassNameCustomization.ShouldBe(_currentAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.ClassNameCustomization);
            accountingPolicies.PreviousPeriodAccountingPolicies.IntangibleAssets.ShouldBeEquivalentTo(_previousAccountingPoliciesData.IntangibleAssets);
            accountingPolicies.PreviousPeriodAccountingPolicies.IntangibleAssets.DevelopmentCosts.ShouldBeEquivalentTo(_previousAccountingPoliciesData.IntangibleAssets.DevelopmentCosts);
            accountingPolicies.PreviousPeriodAccountingPolicies.IntangibleAssets.ComputerSoftware.ShouldBeEquivalentTo(_previousAccountingPoliciesData.IntangibleAssets.ComputerSoftware);
            accountingPolicies.PreviousPeriodAccountingPolicies.IntangibleAssets.Goodwill.ShouldBeEquivalentTo(_previousAccountingPoliciesData.IntangibleAssets.Goodwill);
            accountingPolicies.PreviousPeriodAccountingPolicies.IntangibleAssets.PatentsAndLicenses.ShouldBeEquivalentTo(_previousAccountingPoliciesData.IntangibleAssets.PatentsAndLicenses);
            accountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets);
            accountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings);
            accountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.FreeholdProperty.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.FreeholdProperty);
            accountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty);
            accountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty);
            accountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery);
            accountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery);
            accountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment);
            accountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings);
            accountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty);
            accountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.MotorVehicles);
            accountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.PlantAndMachinery.ClassNameCustomization.ShouldBe(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ClassNameCustomization);
            accountingPolicies.PreviousPeriodAccountingPolicies.TangibleFixedAssets.LandAndBuildings.ClassNameCustomization.ShouldBe(_previousAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.ClassNameCustomization);
        }

        [Fact]
        public void Should_map_accountingPolicies_from_previousPeriod_when_currentPeriod_data_is_unavailable()
        {
            var accountingPolicies = new AccountingPolicies
            {
                CurrentPeriodAccountingPolicies = null,
                PreviousPeriodAccountingPolicies = _previousAccountingPoliciesData
            };

            accountingPolicies.CurrentPeriodAccountingPolicies.ShouldBeNull();
            accountingPolicies.ExemptionsFinancialStatements.ShouldBe(_previousAccountingPoliciesData.ExemptionsFinancialStatements);
            accountingPolicies.ChangesInAccountingPolicies.ShouldBeNull();
            accountingPolicies.FinancialInstrumentsAccountingPolicy.ShouldBe(_previousAccountingPoliciesData.FinancialInstrumentsAccountingPolicy);
            accountingPolicies.GovernmentGrantsAccountingPolicy.ShouldBe(_previousAccountingPoliciesData.GovernmentGrantsAccountingPolicy);
            accountingPolicies.MembersTransactionsWithTheLlpText.ShouldBe(_previousAccountingPoliciesData.MembersTransactionsWithTheLlpText);
            accountingPolicies.PresentationCurrency.ShouldBe(_previousAccountingPoliciesData.PresentationCurrency);
            accountingPolicies.ForeignCurrencies.ShouldBe(_previousAccountingPoliciesData.ForeignCurrencies);
            accountingPolicies.ResearchAndDevelopment.ShouldBe(_previousAccountingPoliciesData.ResearchAndDevelopment);
            accountingPolicies.GoodwillMaterial.ShouldBe(_previousAccountingPoliciesData.GoodwillMaterial);
            accountingPolicies.IntangibleAssetsGoodwill.ShouldBeEquivalentTo(_previousAccountingPoliciesData.IntangibleAssets.Goodwill);
            accountingPolicies.IntangibleAssetsPatentsAndLicenses.ShouldBeEquivalentTo(_previousAccountingPoliciesData.IntangibleAssets.PatentsAndLicenses);
            accountingPolicies.IntangibleAssetsDevelopmentCosts.ShouldBeEquivalentTo(_previousAccountingPoliciesData.IntangibleAssets.DevelopmentCosts);
            accountingPolicies.IntangibleAssetsComputerSoftware.ShouldBeEquivalentTo(_previousAccountingPoliciesData.IntangibleAssets.ComputerSoftware);
            accountingPolicies.TangibleAssetsFreeholdProperty.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.FreeholdProperty);
            accountingPolicies.TangibleAssetsLongLeaseholdProperty.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty);
            accountingPolicies.TangibleAssetsShortLeaseholdProperty.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty);
            accountingPolicies.TangibleAssetsImprovementsToProperty.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty);
            accountingPolicies.TangibleAssetsPlantAndMachinery.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery);
            accountingPolicies.TangibleAssetsFixturesAndFittings.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings);
            accountingPolicies.TangibleAssetsMotorVehicles.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.MotorVehicles);
            accountingPolicies.TangibleAssetsComputerEquipment.ShouldBeEquivalentTo(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment);
            accountingPolicies.TangibleFixedAssets.ShouldNotBeNull();
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.ShouldNotBeNull();
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.ShouldBe(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.MotorVehicles);
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.ShouldBe(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment);
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.ShouldBe(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery);
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.ShouldBe(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings);
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.ShouldBe(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty);
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.ClassNameCustomization.ShouldBe(_previousAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ClassNameCustomization);
            accountingPolicies.TangibleFixedAssets.LandAndBuildings.ClassNameCustomization.ShouldBe(_previousAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.ClassNameCustomization);
            accountingPolicies.IntangibleAssets.ShouldNotBeNull();
            accountingPolicies.IntangibleAssets.Goodwill.ShouldBe(_previousAccountingPoliciesData.IntangibleAssets.Goodwill);
            accountingPolicies.IntangibleAssets.DevelopmentCosts.ShouldBe(_previousAccountingPoliciesData.IntangibleAssets.DevelopmentCosts);
            accountingPolicies.IntangibleAssets.PatentsAndLicenses.ShouldBe(_previousAccountingPoliciesData.IntangibleAssets.PatentsAndLicenses);
            accountingPolicies.IntangibleAssets.ComputerSoftware.ShouldBe(_previousAccountingPoliciesData.IntangibleAssets.ComputerSoftware);
        }

        [Fact]
        public void Should_map_accountingPolicies_from_currentPeriod_when_only_currentPeriod_data_is_available()
        {
            var accountingPolicies = new AccountingPolicies
            {
                CurrentPeriodAccountingPolicies = _currentAccountingPoliciesData,
                PreviousPeriodAccountingPolicies = null
            };

            accountingPolicies.PreviousPeriodAccountingPolicies.ShouldBeNull();
            accountingPolicies.ExemptionsFinancialStatements.ShouldBe(_currentAccountingPoliciesData.ExemptionsFinancialStatements);
            accountingPolicies.ChangesInAccountingPolicies.ShouldBe(_currentAccountingPoliciesData.ChangesInAccountingPolicies);
            accountingPolicies.FinancialInstrumentsAccountingPolicy.ShouldBe(_currentAccountingPoliciesData.FinancialInstrumentsAccountingPolicy);
            accountingPolicies.GovernmentGrantsAccountingPolicy.ShouldBe(_currentAccountingPoliciesData.GovernmentGrantsAccountingPolicy);
            accountingPolicies.MembersTransactionsWithTheLlpText.ShouldBe(_currentAccountingPoliciesData.MembersTransactionsWithTheLlpText);
            accountingPolicies.PresentationCurrency.ShouldBe(_currentAccountingPoliciesData.PresentationCurrency);
            accountingPolicies.ForeignCurrencies.ShouldBe(_currentAccountingPoliciesData.ForeignCurrencies);
            accountingPolicies.ResearchAndDevelopment.ShouldBe(_currentAccountingPoliciesData.ResearchAndDevelopment);
            accountingPolicies.GoodwillMaterial.ShouldBe(_currentAccountingPoliciesData.GoodwillMaterial);
            accountingPolicies.IntangibleAssetsGoodwill.ShouldBeEquivalentTo(_currentAccountingPoliciesData.IntangibleAssets.Goodwill);
            accountingPolicies.IntangibleAssetsPatentsAndLicenses.ShouldBeEquivalentTo(_currentAccountingPoliciesData.IntangibleAssets.PatentsAndLicenses);
            accountingPolicies.IntangibleAssetsDevelopmentCosts.ShouldBeEquivalentTo(_currentAccountingPoliciesData.IntangibleAssets.DevelopmentCosts);
            accountingPolicies.IntangibleAssetsComputerSoftware.ShouldBeEquivalentTo(_currentAccountingPoliciesData.IntangibleAssets.ComputerSoftware);
            accountingPolicies.TangibleFixedAssets.LandAndBuildings.ClassNameCustomization.ShouldBe(_currentAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.ClassNameCustomization);
            accountingPolicies.TangibleAssetsFreeholdProperty.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.FreeholdProperty);
            accountingPolicies.TangibleAssetsLongLeaseholdProperty.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.LongLeaseholdProperty);
            accountingPolicies.TangibleAssetsShortLeaseholdProperty.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.LandAndBuildings.ShortLeaseholdProperty);
            accountingPolicies.TangibleAssetsImprovementsToProperty.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty);
            accountingPolicies.TangibleAssetsPlantAndMachinery.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery);
            accountingPolicies.TangibleAssetsFixturesAndFittings.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings);
            accountingPolicies.TangibleAssetsMotorVehicles.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.MotorVehicles);
            accountingPolicies.TangibleAssetsComputerEquipment.ShouldBeEquivalentTo(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment);
            accountingPolicies.TangibleFixedAssets.ShouldNotBeNull();
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.ShouldNotBeNull();
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.ClassNameCustomization.ShouldBe(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ClassNameCustomization);
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.ShouldBe(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.MotorVehicles);
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.ShouldBe(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment);
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.ShouldBe(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery);
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.ShouldBe(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings);
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.ShouldBe(_currentAccountingPoliciesData.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty);
            accountingPolicies.IntangibleAssets.ShouldNotBeNull();
            accountingPolicies.IntangibleAssets.Goodwill.ShouldBe(_currentAccountingPoliciesData.IntangibleAssets.Goodwill);
            accountingPolicies.IntangibleAssets.DevelopmentCosts.ShouldBe(_currentAccountingPoliciesData.IntangibleAssets.DevelopmentCosts);
            accountingPolicies.IntangibleAssets.PatentsAndLicenses.ShouldBe(_currentAccountingPoliciesData.IntangibleAssets.PatentsAndLicenses);
            accountingPolicies.IntangibleAssets.ComputerSoftware.ShouldBe(_currentAccountingPoliciesData.IntangibleAssets.ComputerSoftware);
        }

        [Fact]
        public void Should_map_accountingPolicies_when_previousPeriod_and_currentPeriod_not_available()
        {
            var accountingPolicies = new AccountingPolicies
            {
                CurrentPeriodAccountingPolicies = null,
                PreviousPeriodAccountingPolicies = null
            };

            accountingPolicies.CurrentPeriodAccountingPolicies.ShouldBeNull();
            accountingPolicies.PreviousPeriodAccountingPolicies.ShouldBeNull();
            accountingPolicies.ExemptionsFinancialStatements.ShouldBeNull();
            accountingPolicies.ChangesInAccountingPolicies.ShouldBeNull();
            accountingPolicies.FinancialInstrumentsAccountingPolicy.ShouldBeNull();
            accountingPolicies.GovernmentGrantsAccountingPolicy.ShouldBeNull();
            accountingPolicies.MembersTransactionsWithTheLlpText.ShouldBeNull();
            accountingPolicies.PresentationCurrency.ShouldBeNull();
            accountingPolicies.ForeignCurrencies.ShouldBeNull();
            accountingPolicies.ResearchAndDevelopment.ShouldBeNull();
            accountingPolicies.GoodwillMaterial.ShouldBe(false);
            accountingPolicies.IntangibleAssetsGoodwill.ShouldBeNull();
            accountingPolicies.IntangibleAssetsPatentsAndLicenses.ShouldBeNull();
            accountingPolicies.IntangibleAssetsDevelopmentCosts.ShouldBeNull();
            accountingPolicies.IntangibleAssetsComputerSoftware.ShouldBeNull();
            accountingPolicies.TangibleAssetsFreeholdProperty.ShouldBeNull();
            accountingPolicies.TangibleAssetsLongLeaseholdProperty.ShouldBeNull();
            accountingPolicies.TangibleAssetsShortLeaseholdProperty.ShouldBeNull();
            accountingPolicies.TangibleAssetsImprovementsToProperty.ShouldBeNull();
            accountingPolicies.TangibleAssetsPlantAndMachinery.ShouldBeNull();
            accountingPolicies.TangibleAssetsFixturesAndFittings.ShouldBeNull();
            accountingPolicies.TangibleAssetsMotorVehicles.ShouldBeNull();
            accountingPolicies.TangibleAssetsComputerEquipment.ShouldBeNull();
            accountingPolicies.TangibleFixedAssets.ShouldNotBeNull();
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.ShouldNotBeNull();
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.MotorVehicles.ShouldBeNull();
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.ComputerEquipment.ShouldBeNull();
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.PlantAndMachinery.ShouldBeNull();
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.FixturesAndFittings.ShouldBeNull();
            accountingPolicies.TangibleFixedAssets.PlantAndMachinery.ImprovementsToProperty.ShouldBeNull();
            accountingPolicies.IntangibleAssets.ShouldNotBeNull();
            accountingPolicies.IntangibleAssets.Goodwill.ShouldBeNull();
            accountingPolicies.IntangibleAssets.DevelopmentCosts.ShouldBeNull();
            accountingPolicies.IntangibleAssets.PatentsAndLicenses.ShouldBeNull();
            accountingPolicies.IntangibleAssets.ComputerSoftware.ShouldBeNull();
        }
    }
}