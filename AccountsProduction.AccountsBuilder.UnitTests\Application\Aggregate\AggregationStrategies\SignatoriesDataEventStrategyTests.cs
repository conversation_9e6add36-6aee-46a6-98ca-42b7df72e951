﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.NonFinancialData;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Signatory;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.AggregationStrategies
{
    public class SignatoriesDataEventStrategyTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<ILogger<SignatoriesDataEventStrategy>> _logger;
        private readonly IMapper _mapper;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = Guid.NewGuid();
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;

        public SignatoriesDataEventStrategyTests()
        {
            var mapperConfig = new MapperConfiguration(cfg =>
                {
                    cfg.AddProfile<SignatoryMapper>();
                }
            );

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();

            _repository = new Mock<IAccountsBuilderRepository>();
            _logger = new Mock<ILogger<SignatoriesDataEventStrategy>>();
        }

        [Fact]
        public async Task Should_update_accounts_production_data()
        {
            var nonFinancialDataResponse = new NonFinancialDataDto
            {
                UUID = TestHelpers.Guids.GuidOne,
                Document = new ClientDocumentDto
                {
                    Title = "Test Title",
                    Forenames = "Test Forenames",
                    Surname = "Test Surname",
                }
            };

            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId,
                    _periodId, null));
            var requestMessage = GetRequestMessage();
            var strategy = new SignatoriesDataEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(
                repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Once);

        }

        [Fact]
        public async Task Should_update_accounts_production_in_error_state()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, null));
            var requestMessage = GetRequestMessage(false);
            var strategy = new SignatoriesDataEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository =>
                    repository.Save(It.Is<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(x => x.Signatory.IsSuccessful == false), CancellationToken.None),
                Times.Once);
        }

        [Fact]
        public async Task Should_not_update_when_no_accounts_builder_found()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(() => null);
            var requestMessage = GetRequestMessage();
            var strategy = new SignatoriesDataEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Never);
        }

        [Fact]
        public async Task Should_throw_on_exception()
        {
            _repository.Setup(repository => repository.Get(_clientId, _periodId, CancellationToken.None))
                .ReturnsAsync(new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(Guid.NewGuid().ToString(), _clientId, _periodId, null));
            _repository.Setup(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None))
                .Throws<Exception>();
            var requestMessage = GetRequestMessage();
            var strategy = new SignatoriesDataEventStrategy(_repository.Object, _logger.Object, _mapper);

            await Should.ThrowAsync<Exception>(async () =>
            {
                await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);
            });
        }

        private string GetRequestMessage(bool isSuccessful = true)
        {
            var message = JsonSerializer.Serialize(new SignatoryResponseDto
            {
                ClientId = _clientId,
                PeriodId = _periodId,
                Signatory = new SignatureDetailResponseDto
                {
                    AccountantSigningDate = DateTime.Now,
                    IncludeAccountantsReport = true,
                    Signatures = new List<SignatureResponseDto>
                    {
                        new SignatureResponseDto
                        {
                            SignatoryId = Guid.NewGuid(),
                            OrderNumber = 1,
                            SigningDate = DateTime.Now,
                            SignatureType = SignatureType.BalanceSheet
                        },
                        new SignatureResponseDto
                        {
                            SignatoryId = Guid.NewGuid(),
                            OrderNumber = 2,
                            SignatureType = SignatureType.DirectorsReport
                        }
                    }
                },
                IsSuccessful = isSuccessful
            }, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return message;
        }
    }
}
