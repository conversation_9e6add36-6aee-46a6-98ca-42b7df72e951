﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AutoMapper;
using Shouldly;
using Xunit;
using AccountsProduction.AccountsBuilder.Domain;
using Iris.AccountsProduction.AccountsBuilder.Messages.AccountPeriod;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.AutoMapper.Mappers
{
    public class AccountPeriodMapperTests
    {
        private readonly IMapper _mapper;
        public AccountPeriodMapperTests()
        {
            var mapperConfig = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<AccountPeriodMapper>();
            }
            );

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();
        }

        [Fact]
        public void Should_map_accountperiodmessage_to_accountperiod()
        {
            var accountPeriodMessage = new AccountPeriodMessage
            {
                ClientId = Guid.NewGuid(),
                PeriodId = Guid.NewGuid(),
                ReviseType = "reviseType",
            };

            var result = _mapper.Map<AccountPeriod>(accountPeriodMessage);

            result.ClientId.ShouldBe(accountPeriodMessage.ClientId);
            result.PeriodId.ShouldBe(accountPeriodMessage.PeriodId);
            result.ReviseType.ShouldBe(accountPeriodMessage.ReviseType);
        }

        [Fact]
        public void Should_map_accountperiod_to_accountperiodmessage()
        {
            var accountPeriod = new AccountPeriod
            {
                ClientId = Guid.NewGuid(),
                PeriodId = Guid.NewGuid(),
                ReviseType = "reviseType",
            };

            var result = _mapper.Map<AccountPeriodMessage>(accountPeriod);

            result.ClientId.ShouldBe(accountPeriod.ClientId);
            result.PeriodId.ShouldBe(accountPeriod.PeriodId);
            result.ReviseType.ShouldBe(accountPeriod.ReviseType);
        }

    }
}
