﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.DirectorsRunner
{
    public static class DirectorValidations
    {
        public const string Director = "Director";
        public const string ActiveDirector = "ActiveDirector";

        public static readonly ValidationRuleConfig DirectorRuleConfig = new ValidationRuleConfig
        {
            Description =
                "A company must have at least one director to be able to approve and submit financial statements.",
            Name = Director,
            Breadcrumb = ClientManagementBreadcrumbs.RelationShipTab.ToString(),
            DisplayName = "Director",
            ErrorCategory = ErrorCategoryType.Mandatory,
            Type = ValidationRuleType.Missing,
            Target = Target.IssueLog,
            ErrorCode = ValidationCodes.Director
        };

        public static readonly ValidationRuleConfig ActiveDirectorRuleConfig = new ValidationRuleConfig
        {
            Description =
                "The company director must be active at the time of the approval and submission of financial statements.",
            Name = ActiveDirector,
            Breadcrumb = ClientManagementBreadcrumbs.RelationShipTab.ToString(),
            DisplayName = "Director",
            ErrorCategory = ErrorCategoryType.Mandatory,
            Type = ValidationRuleType.Invalid,
            Target = Target.IssueLog,
            ErrorCode = ValidationCodes.DirectorActive
        };
    }
}