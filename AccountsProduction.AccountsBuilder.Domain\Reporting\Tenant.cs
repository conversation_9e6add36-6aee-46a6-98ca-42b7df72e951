﻿namespace AccountsProduction.AccountsBuilder.Reporting.Domain
{
    public class Tenant
    {
        public Tenant()
        {
            Client = new HashSet<Client>();
        }

        public Guid Id { get; set; }

        public int ReferredType { get; set; }

        public string? SupervisingBody { get; set; }

        public string? Name { get; set; }

        public string? AddressLine1 { get; set; }

        public string? AddressLine2 { get; set; }

        public string? AddressLine3 { get; set; }

        public string? AddressTown { get; set; }

        public string? AddressCounty { get; set; }

        public string? AddressPostcode { get; set; }

        public string? AddressCountry { get; set; }

        public virtual ICollection<Client> Client { get; set; }
    }
}
