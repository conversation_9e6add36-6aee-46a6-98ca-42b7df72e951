﻿using Iris.AccountsProduction.Common.Toolkit.Utils;
using Iris.Platform.WebApi.Infrastructure.Middleware;

namespace AccountsProduction.AccountsBuilder.Application.Common.Helper
{
    public static class LicenseHelper
    {
        public static bool IsAccountsProdTrialLicense(UserContext userContext)
        {
            if (userContext?.Licenses == null) return false;

            return userContext.Licenses.Any(x => x.Code == APLicense.Name && x.IsTrial);
        }
    }
}
