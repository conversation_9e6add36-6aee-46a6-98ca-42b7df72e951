﻿using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.
    LlpDesignatedMemberRunner
{
    public class LlpDesignatedMemberValidationRunner : ValidationRunner
    {
        private const string DesignatedMemberInvolvementType = "Designated Member";

        protected override Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>>
            ValidationRules =>
            new()
            {
                {
                    LlpDesignatedMemberValidations.DesignatedMember,
                    ValidateDesignatedMembers
                },
                {
                    LlpDesignatedMemberValidations.ActiveDesignatedMember,
                    ValidateActiveDesignatedMembers
                },
                {
                    LlpDesignatedMemberValidations.UnallocatedProfit,
                    ValidateUnallocatedProfit
                }
            };

        private static ValidationIssue ValidateDesignatedMembers(
            Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var designatedMembers =
                accountsBuilder.InvolvementsData.Involvements.Where(involvement =>
                    involvement.InvolvementType == DesignatedMemberInvolvementType);

            if (designatedMembers.Count() < 2)
                return LlpDesignatedMemberValidations.DesignatedMemberRuleConfig.MapToValidationIssue();

            return null;
        }

        private static ValidationIssue ValidateActiveDesignatedMembers(
            Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var designatedMembers =
                accountsBuilder.InvolvementsData.Involvements.Where(involvement =>
                    Validator.IsInvolvementActive(involvement, DesignatedMemberInvolvementType,
                        accountsBuilder.EntityModificationTime.Date));


            if (designatedMembers.Count() < 2)
                return LlpDesignatedMemberValidations.ActiveDesignatedMemberRuleConfig.MapToValidationIssue();

            return null;
        }

        private static ValidationIssue ValidateUnallocatedProfit(
            Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var absUnallocatedAmount = Math.Abs(accountsBuilder.ProfitShareData.UnallocatedAmount);
            if (absUnallocatedAmount >= 0.5m)
            {
                return LlpDesignatedMemberValidations.UnallocatedProfitLossSevereRuleConfig.MapToValidationIssue();
            }
            else if (absUnallocatedAmount > 0.0m)
            {
                return LlpDesignatedMemberValidations.UnallocatedProfitLossRuleConfig.MapToValidationIssue();
            }

            return null;
        }
    }
}