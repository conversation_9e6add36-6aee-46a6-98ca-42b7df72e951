﻿using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels.TangibleFixedAsset;

namespace AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels
{
    public interface IFRS102AccountingPoliciesData
    {
        ExemptionsFinancialStatements ExemptionsFinancialStatements { get; set; }
        TangibleFixedAssets TangibleFixedAssets { get; set; }
        IntangibleAssets IntangibleAssets { get; set; }
        string ChangesInAccountingPolicies { get; set; }
        string FinancialInstrumentsAccountingPolicy { get; set; }
        string GovernmentGrantsAccountingPolicy { get; set; }
        string MembersTransactionsWithTheLlpText { get; set; }
        bool? PresentationCurrency { get; set; }
        bool? ResearchAndDevelopment { get; set; }
        bool? ForeignCurrencies { get; set; }
        bool? GoodwillMaterial { get; set; }
    }
}
