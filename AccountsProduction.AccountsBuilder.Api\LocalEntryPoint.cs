﻿using Iris.Elements.Logging.Serilog.AspNet;
using Serilog;
using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.Api
{
    /// <summary>
    /// The Main function can be used to run the ASP.NET Core application locally using the Kestrel webserver.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class LocalEntryPoint
    {
        public static void Main(string[] args)
        {
            Log.Logger = HostBuilderSerilogExtensions.InitializeBootstrapLogger();

            try
            {
                Log.Warning("Starting web host");
                CreateHostBuilder(args).Build().Run();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Host terminated unexpectedly");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .AddSerilog()
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                });
    }
}
