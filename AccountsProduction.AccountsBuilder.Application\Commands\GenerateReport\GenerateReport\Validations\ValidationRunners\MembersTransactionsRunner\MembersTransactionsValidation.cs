﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.MembersTransactionsRunner
{
    public static class MembersTransactionsValidation
    {
        public const string MembersTransactions = "MembersTransactions";

        public static readonly ValidationRuleConfig MembersTransactionsConfig =
            new ValidationRuleConfig
            {
                Breadcrumb = MembersTransactionsBreadcrumbs.MembersTransactionsRule.ToString(),
                Description = "Enter an accounting policy describing members' transactions with the business as recommended by the LLP SORP.",
                Name = MembersTransactions,
                Type = ValidationRuleType.Missing,
                Target = Target.IssueLog,
                ErrorCategory = ErrorCategoryType.Mandatory,
                DisplayName = "Members transactions",
                ErrorCode = ValidationCodes.MembersTransations
            };
    }
}