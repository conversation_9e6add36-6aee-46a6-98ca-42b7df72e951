﻿using AccountsProduction.AccountsBuilder.Api.Controllers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder;
using AccountsProduction.AccountsBuilder.Application.Queries.AccountsBuilder;
using Iris.AccountsProduction.Common.Toolkit.Exceptions;
using Iris.AccountsProduction.Common.Toolkit.Filters;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Shouldly;
using Xunit;
using static Iris.AccountsProduction.Common.Toolkit.Utils.Constants;

namespace AccountsProduction.AccountsBuilder.UnitTests.Controllers
{
    public class AccountsBuilderControllerTests
    {
        private readonly AccountsBuilderController _abController;
        private readonly Mock<IMediator> _mediator;

        public AccountsBuilderControllerTests()
        {
            _mediator = new Mock<IMediator>();
            _abController = new AccountsBuilderController(_mediator.Object);
        }

        public class Should_check_for_controller_restrictions : AccountsBuilderControllerTests
        {
            [Fact]
            public void Should_check_for_licenses_types()
            {
                _abController.HasAttribute(typeof(APLicensedAttribute)).ShouldBeTrue();
            }

            [Fact]
            public void Should_check_for_feature_toggle()
            {
                _abController.HasAttributeWithExpectedArgument(typeof(FeatureAttribute), FeatureToggle.AccountsProduction.Services).ShouldBeTrue();
            }
        }

        [Fact]
        public async Task Should_return_accounts_builder()
        {
            _mediator.Setup(m => m.Send(It.IsAny<GetFullReportQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(() => new AccountsBuilderFullDto());

            var response = await _abController.GetFullReportByPeriodId(Guid.NewGuid(), Guid.NewGuid());

            response.ShouldNotBeNull();
            response.ShouldBeAssignableTo<OkObjectResult>();
        }

        [Fact]
        public async Task Should_return_not_found_when_accounts_builder_not_found()
        {
            _mediator.Setup(m => m.Send(It.IsAny<GetFullReportQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(() => null);

            var response = await _abController.GetFullReportByPeriodId(Guid.NewGuid(), Guid.NewGuid());

            response.ShouldBeAssignableTo<NotFoundResult>();
        }

        [Fact]
        public async Task Should_return_unauthorized_for_invalid_tenant_exception()
        {
            _mediator.Setup(m => m.Send(It.IsAny<GetFullReportQuery>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new InvalidTenantException("TenantId invalid"));

            await Should.ThrowAsync<InvalidTenantException>(async () =>
                {
                    var result = await _abController.GetFullReportByPeriodId(Guid.NewGuid(), Guid.NewGuid());
                }
            );
        }

		[Fact]
		public void Should_check_for_confidential_client_access()
		{
			_abController.HasAttribute(typeof(ConfidentialClientAccessAttribute)).ShouldBeTrue();
		}
	}
}
