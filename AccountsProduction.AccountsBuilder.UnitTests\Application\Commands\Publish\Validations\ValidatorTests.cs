﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations
{
    public class ValidatorTests
    {
        [Fact]
        public void Should_return_ValidationIssue_when_AssetsAdjustment_properties_have_default_values()
        {
            var actual = new AssetsAdjustment().ValidateForNull(GetDummyValidationRuleConfig());

            actual.ShouldNotBeNull();
        }

        [Fact]
        public void Should_return_null_when_AssetsAdjustment_properties_dont_have_default_values()
        {
            var actual = new AssetsAdjustment
            {
                StraightLineBasis = 1,
                ReducingBalanceBasis = 2,
                AlternativeBasis = "text"
            }.ValidateForNull(GetDummyValidationRuleConfig());

            actual.ShouldBeNull();
        }

        private ValidationRuleConfig GetDummyValidationRuleConfig()
        {
            return new ValidationRuleConfig
            {
                Breadcrumb = "breadcrumb",
                Description = "description",
                Name = "name",
                Type = ValidationRuleType.Missing,
                Target = Target.SectionValidation,
                ErrorCategory = ErrorCategoryType.Advisory,
                DisplayName = "display name",
                ErrorCode = "error code"
            };
        }
    }
}
