﻿using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.NoteModels;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NotesRunner.NoteFrs1021aRunner
{
    public class NotesFrs1021aValidationRunner : NotesValidationRunner
    {
        private const int Account503 = 503;
        private const int Account504 = 504;
        private const int Account505 = 505;
        private const int Group631 = 631;
        private const int Group632 = 632;
        private const int Group430 = 430;
        private readonly IGroupAccountSubAccountIntervalRepository _repository;

        public NotesFrs1021aValidationRunner(IGroupAccountSubAccountIntervalRepository repository) : base(ReportStandardType.FRS102_1A)
        {
            _repository = repository;
        }

        protected override Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>> ValidationRules
        {
            get
            {
                var validationRules = base.ValidationRules;

                validationRules.Add(NotesFrs1021aValidations.IntangibleAssetsRevaluation, ValidateIntangibleAssetsRevaluation);
                validationRules.Add(NotesFrs1021aValidations.AdvancesCreditGuarantees, ValidateAdvancesCreditGuarantees);
                validationRules.Add(NotesFrs1021aValidations.TangibleFixedAssetsNotes, ValidateTangibleFixedAssetsNotes);
                validationRules.Add(NotesFrs1021aValidations.TangibleFixedAssetsHistoricAndAnalysisNotes, ValidateTangibleFixedAssetsHistoricAndAnalysisNotesCheck);

                return validationRules;
            }
        }

        private ValidationIssue ValidateIntangibleAssetsRevaluation(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var hasAccounts = accountsBuilder.TrialBalance.TrialBalances.Any(x => x.AccountCode == Account505 || x.AccountCode == Account504
                || x.AccountCode == Account503);

            if (hasAccounts) return null;

            return NotesFrs1021aValidations.RevaluationConfig.MapToValidationIssue();
        }

        private ValidationIssue ValidateAdvancesCreditGuarantees(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var accountRanges = _repository.GetCachedGroupAccountSubAccountIntervalList().Where(g => g.GroupNo.In(Group631, Group632));

            var hasAccounts = HasAccountsInSearchedGroups(accountsBuilder, accountRanges.ToList());

            if (!hasAccounts) return null;

            return Validator.ValidateForNull(accountsBuilder.Notes?.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended, NotesFrs1021aValidations.AdvancesCreditGuaranteesConfig);
        }

        private ValidationIssue ValidateTangibleFixedAssetsNotes(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var accountRanges = _repository.GetCachedGroupAccountSubAccountIntervalList().Where(g => g.GroupNo.In(Group430));

            var hasAccounts = HasAccountsInSearchedGroups(accountsBuilder, accountRanges.ToList());

            if (hasAccounts) return null;

            return NotesFrs1021aValidations.TangibleFixedAssestsNoteConfig.MapToValidationIssue();
        }

        private ValidationIssue ValidateTangibleFixedAssetsHistoricAndAnalysisNotesCheck(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder)
        {
            var accountRanges = _repository.GetCachedGroupAccountSubAccountIntervalList().Where(g => g.GroupNo.In(Group430));

            var hasAccounts = HasAccountsInSearchedGroups(accountsBuilder, accountRanges.ToList());

            if (!hasAccounts) return null;

            bool isHistoricalCostBreakdownComplete = HistoricalCostBreakdownIsComplete(accountsBuilder.Notes?.TangibleFixedAssetsNotes?.HistoricalCostBreakdown);
            bool isAnalysisOfCostOrValuationComplete = AnalysisOfCostOrValuationIsComplete(accountsBuilder.Notes?.TangibleFixedAssetsNotes?.AnalysisOfCostOrValuation);

            if (isHistoricalCostBreakdownComplete && !isAnalysisOfCostOrValuationComplete)
            {
                return NotesFrs1021aValidations.TangibleFixedAssestsAnalysisOfCostAndValuationNoteConfig.MapToValidationIssue();
            }
            else if (!isHistoricalCostBreakdownComplete && isAnalysisOfCostOrValuationComplete)
            {
                return NotesFrs1021aValidations.TangibleFixedAssestsHistoricalCostBreakdownNoteConfig.MapToValidationIssue();
            }
            else
            {
                return null;
            }
        }

        private bool HistoricalCostBreakdownIsComplete(HistoricalCostBreakdown historicalCostBreakdown)
        {
            return historicalCostBreakdown != null &&
                !string.IsNullOrEmpty(historicalCostBreakdown.RevaluedAssetClass) &&
                !string.IsNullOrEmpty(historicalCostBreakdown.RevaluedClassPronoun);
        }


        private bool AnalysisOfCostOrValuationIsComplete(AnalysisOfCostOrValuation analysisOfCostOrValuation)
        {
            return analysisOfCostOrValuation != null &&
                 (analysisOfCostOrValuation.TotalPlantAndMachineryEtc.HasValue ||
                 analysisOfCostOrValuation.TotalLandAndBuildings.HasValue) &&
                 (analysisOfCostOrValuation.CostLandAndBuildings.HasValue ||
                 analysisOfCostOrValuation.CostPlantAndMachineryEtc.HasValue ||
                 (analysisOfCostOrValuation.AnalysisOfCostOrValuationItems != null &&
                 analysisOfCostOrValuation.AnalysisOfCostOrValuationItems.Any()));
        }
    }
}