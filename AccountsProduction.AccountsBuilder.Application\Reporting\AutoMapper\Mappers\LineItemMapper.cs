﻿using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Iris.AccountsProduction.AccountsBuilder.Messages.ProfitShare;
using System.Reflection;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper.Mappers
{
    public class LineItemMapper : Profile
    {
        public LineItemMapper()
        {
            CreateMap<List<ProfitAndLossMessage>, List<LineItem>>()
                .ConvertUsing<ProfitAndLossDtoToListOfLineItemConverter>();
            CreateMap<List<BalanceSheetMessage>, List<LineItem>>()
                .ConvertUsing<BalanceSheetsDtoToListOfLineItemConverter>();
            CreateMap<List<ProfitShareMessage>, List<LineItem>>()
                .ConvertUsing<ProfitShareMessageListToListOfLineItemConverters>();
            CreateMap<List<OtherMessage>, List<LineItem>>()
            .ConvertUsing<OtherMessageToListOfLineItemConverter>();
        }

        private sealed class OtherMessageToListOfLineItemConverter : ITypeConverter<List<OtherMessage>, List<LineItem>>
        {
            public List<LineItem> Convert(List<OtherMessage> source, List<LineItem> destination,
                ResolutionContext context)
            {
                return source.Extract();
            }
        }

        private sealed class ProfitAndLossDtoToListOfLineItemConverter : ITypeConverter<List<ProfitAndLossMessage>, List<LineItem>>
        {
            public List<LineItem> Convert(List<ProfitAndLossMessage> source, List<LineItem> destination,
                ResolutionContext context)
            {
                return source.Extract();
            }
        }

        private sealed class BalanceSheetsDtoToListOfLineItemConverter : ITypeConverter<List<BalanceSheetMessage>, List<LineItem>>
        {
            public List<LineItem> Convert(List<BalanceSheetMessage> source, List<LineItem> destination,
                ResolutionContext context)
            {
                return source.Extract();
            }
        }

        private sealed class ProfitShareMessageListToListOfLineItemConverters : ITypeConverter<List<ProfitShareMessage>, List<LineItem>>
        {
            public List<LineItem> Convert(List<ProfitShareMessage> source, List<LineItem> destination,
                ResolutionContext context)
            {
                return source.Extract();
            }
        }
    }


    public static class LineItemExtractor
    {
        private const string ProfitShareCategoryName = "profitShare";
        private const string ProfitShareAccountCode = "9999";
        public static List<LineItem> Extract(this List<ProfitShareMessage> source)
        {
            return source.Select(psData => new LineItem
            {
                AccountPeriodId = psData.AccountPeriodId,
                Category = ProfitShareCategoryName,
                AccountCode = ProfitShareAccountCode,
                SubAccountCode = null,
                AccountDescription = null,
                CurrentValue = psData.CumulativeAmount,
                InvolvementId = psData.InvolvementId
            }).ToList();
        }

        public static List<LineItem> Extract<T>(this List<T> source)
        {
            if (!source.IsPopulated())
            {
                return new List<LineItem>();
            }

            var firstItem = source[0];

            var financialDataCategoryProperties = firstItem?.GetType().GetProperties()
                .Where(s => s.PropertyType == typeof(FinancialDataCategoryMessage))!.ToList();

            var periodIdPropertyInfo = firstItem?.GetType().GetProperties().Single(s => s.Name == "PeriodId");

            return BuildResult(source, financialDataCategoryProperties, periodIdPropertyInfo);
        }

        private static List<LineItem> BuildResult<T>(List<T> source, List<PropertyInfo>? financialDataCategoryProperties, PropertyInfo? periodIdPropertyInfo)
        {
            var result = new List<LineItem>();

            foreach (var item in source)
            {
                Guid periodIdValue;
                var periodIdValueObject = periodIdPropertyInfo?.GetValue(item, null);
                if (periodIdValueObject is Guid guidValue)
                {
                    periodIdValue = guidValue;
                }
                else
                {
                    throw new InvalidOperationException($"Expected PeriodId to be of type Guid, but got {periodIdValueObject?.GetType().Name}");
                }

                result.AddRange(BuildLineItemsFromItem(item, financialDataCategoryProperties, periodIdValue));
            }

            return result;
        }

        private static List<LineItem> BuildLineItemsFromItem<T>(T item, List<PropertyInfo>? financialDataCategoryProperties, Guid periodIdValue)
        {
            // Ensure financialDataCategoryProperties is not null before iterating
            if (financialDataCategoryProperties == null)
            {
                return new List<LineItem>();
            }

            var lineItems = financialDataCategoryProperties
                .Select(property => item?.GetType().GetProperty(property.Name))
                .Where(categoryPropertyInfo => categoryPropertyInfo != null)
                .Select(categoryPropertyInfo => new
                {
                    categoryValue = categoryPropertyInfo?.GetValue(item, null) as FinancialDataCategoryMessage,
                    propertyName = categoryPropertyInfo?.Name
                })
                .Where(data => data.categoryValue != null && data.categoryValue.DrilldownData.IsPopulated())
                .SelectMany(data => BuildLineItemsFromDrilldownData(data.categoryValue!.DrilldownData, periodIdValue, data.propertyName!))
                .ToList();

            return lineItems;
        }

        private static List<LineItem> BuildLineItemsFromDrilldownData(List<FinancialDataDrilldownMessage> drilldownData, Guid periodIdValue, string propertyName)
        {
            var lineItems = drilldownData.Select(drillDown => new LineItem
            {
                AccountPeriodId = periodIdValue,
                Category = propertyName,
                AccountCode = drillDown.AccountCode.ToString(),
                SubAccountCode = drillDown.SubAccountCode.ToString(),
                AccountDescription = drillDown.Description,
                CurrentValue = drillDown.Amount,
                InvolvementId = drillDown.DirectorCode,
                SectorId = drillDown.SectorId
            }).ToList();

            return lineItems;
        }
    }
}