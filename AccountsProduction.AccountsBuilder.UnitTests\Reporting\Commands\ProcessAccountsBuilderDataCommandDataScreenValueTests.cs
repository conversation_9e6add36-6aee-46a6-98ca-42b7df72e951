﻿using AccountsProduction.AccountsBuilder.Reporting.Application;
using AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.UnitTests.Reporting.StaticData;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.Commands
{
    public class ProcessAccountsBuilderDataCommandDataScreenValueTests
    {
        private readonly Mock<ILogger<BaseSaveUpdateStrategy>> _loggerMock;
        private readonly Mock<IAccountsProductionReportingDbContext> _dbContextMock;
        private readonly Mock<IMediator> _mediatorMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly BaseSaveUpdateStrategy _strategy;

        public ProcessAccountsBuilderDataCommandDataScreenValueTests()
        {
            _loggerMock = new Mock<ILogger<BaseSaveUpdateStrategy>>();
            _dbContextMock = new Mock<IAccountsProductionReportingDbContext>();
            _mediatorMock = new Mock<IMediator>();
            _mapperMock = new Mock<IMapper>();
            _strategy = new Mock<BaseSaveUpdateStrategy>(_mediatorMock.Object, _loggerMock.Object, _dbContextMock.Object, _mapperMock.Object).Object;
        }

        [Fact]
        public void SaveAndUpdateDataScreenValues_should_return_updated_lists()
        {
            var data = new AccountsBuilderReportingMessageDto
            {
                DataScreenValue = TestData.GetDataScreenValuesMessage()
            };

            var result = _strategy.SaveAndUpdateNoteDataScreenValues(data);

            var expected = 4;

            Assert.Equal(result.NotesOther.Count, expected);
            Assert.Equal(result.NotesProfitAndLoss.Count, expected);
            Assert.Equal(result.NotesAccountingPolicies.Count, expected);
            Assert.Equal(result.NotesBalanceSheet.Count, expected);
            Assert.Equal(result.Reports.Count, expected);
        }

        [Fact]
        public void AddSeniorStatutoryAuditorToReports_should_add_auditor_when_signature_is_present()
        {
            var data = new AccountsBuilderReportingMessageDto
            {
                DataScreenValue = TestData.GetDataScreenValuesMessage(),
                Signatures = TestData.GetSignatureWithAuditAccountantName()
            };
            var datascreensLists = _strategy.SaveAndUpdateNoteDataScreenValues(data);
            var reports = _strategy.AddSeniorStatutoryAuditorToReports(data, datascreensLists.Reports);
            var expectedReportsCount = 5;
            Assert.Equal(reports.Count, expectedReportsCount);
            Assert.Equal("SeniorStatutoryAuditor", reports[4].NoteType);
            Assert.Equal("Test Name", reports[4].NoteText);
        }

        [Fact]
        public void AddSeniorStatutoryAuditorToReports_should_not_add_auditor_when_signature_is_not_present()
        {
            var data = new AccountsBuilderReportingMessageDto
            {
                DataScreenValue = TestData.GetDataScreenValuesMessage(),
                Signatures = TestData.GetSignatureWithNoAuditAccountantName()
            };
            var datascreensLists = _strategy.SaveAndUpdateNoteDataScreenValues(data);
            var reports = _strategy.AddSeniorStatutoryAuditorToReports(data, datascreensLists.Reports);
            var expectedReportsCount = 4;
            Assert.Equal(reports.Count, expectedReportsCount);
        }

        [Fact]
        public async Task ShouldProcessShareCapitalMovementCorrectlyIfPresent()
        {
            var data = Data.GetProcessAccountsBuilderDataCommand("FRS102");
            var message = data.Message;
            message.OtherData =
            [
                new OtherMessage
                {
                    Period = DateTime.Now,
                    PeriodId = message.PeriodId,
                    ShareCapitalMovements = new FinancialDataCategoryMessage()
                    {
                        Value = "30",
                        DrilldownData =
                        [
                            new() {
                                AccountCode = 2,
                                Amount = 2,
                                SubAccountCode = 2,
                                Description = "Test",
                                DirectorCode = 2,
                                SectorCreatedDate = DateTime.Now,
                                SectorId = Guid.NewGuid()
                            }
                        ]
                    }
                }
            ];

            var exception = await Record.ExceptionAsync(async () => await _strategy.SendSaveAndUpdateDataEvents(data.Message, new CancellationToken()));
            Assert.Null(exception);
        }
    }
}
