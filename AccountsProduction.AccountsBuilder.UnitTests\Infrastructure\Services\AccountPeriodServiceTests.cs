﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Infrastructure.Services;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using Shouldly;
using System.Net;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Infrastructure.Services
{
    public class AccountPeriodServiceTests
    {
        private readonly Mock<ILogger<AccountPeriodService>> _logger;
        private readonly Mock<UserContext> _userContext;
        private readonly Mock<IEnvVariableProvider> _envVariableProvider;
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private EntitySetupDto _entitySetupResponse;

        public AccountPeriodServiceTests()
        {
            _logger = new Mock<ILogger<AccountPeriodService>>();
            _envVariableProvider = new Mock<IEnvVariableProvider>();
            _userContext = new Mock<UserContext>();
            _userContext.Setup(x => x.TenantId).Returns(TestHelpers.Guids.GuidOne.ToString());
            _userContext.Setup(x => x.CorrelationId).Returns(Guid.NewGuid().ToString());

            _entitySetupResponse = new EntitySetupDto
            {
                ReportingStandard = "FRS105",
                EntitySize = "Small",
                IndependentReviewType = "Accountants",
                TradingStatus = "Trading"
            };
        }

        [Fact]
        public async Task Should_get_entity_setup()
        {
            var responseMessage = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(_entitySetupResponse, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase }))
            };
            var accountPeriodService = SetupMockData(responseMessage);
            var response = await accountPeriodService.GetEntitySetupAsync(TestHelpers.Guids.GuidOne, TestHelpers.Guids.GuidTwo);

            response.ShouldNotBeNull();
            response.ShouldBeOfType(typeof(EntitySetupDto));
            response.ReportingStandard.ShouldBe("FRS105");
            response.EntitySize.ShouldBe("Small");
            response.IndependentReviewType.ShouldBe("Accountants");
            response.TradingStatus.ShouldBe("Trading");
        }

        [Theory]
        [InlineData(HttpStatusCode.NotFound)]
        [InlineData(HttpStatusCode.InternalServerError)]
        public async Task Should_throw_exception_when_entitysetup_http_request_status_is_not_success(HttpStatusCode httpStatusCode)
        {
            var responseMessage = new HttpResponseMessage
            {
                StatusCode = httpStatusCode
            };

            var accountPeriodService = SetupMockData(responseMessage);

            await Should.ThrowAsync<HttpRequestException>(async () => { await accountPeriodService.GetEntitySetupAsync(TestHelpers.Guids.GuidOne, TestHelpers.Guids.GuidTwo); });
        }


        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task Should_get_client_onboarded_status(bool isClientOnboarded)
        {
            var clientOnboardedDto = new ClientOnboardedDto
            {
                ClientId = TestHelpers.Guids.GuidOne,
                IsOnboarded = isClientOnboarded
            };

            var responseMessage = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(clientOnboardedDto, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase }))
            };
            var accountPeriodService = SetupMockData(responseMessage);

            var response = await accountPeriodService.IsClientOnboarded(TestHelpers.Guids.GuidOne);

            response.ShouldBe(isClientOnboarded);
        }

        [Theory]
        [InlineData(HttpStatusCode.NotFound)]
        [InlineData(HttpStatusCode.InternalServerError)]
        public async Task Should_throw_exception_when_clientonboarded_http_request_status_is_not_success(HttpStatusCode httpStatusCode)
        {
            var responseMessage = new HttpResponseMessage
            {
                StatusCode = httpStatusCode
            };

            var accountPeriodService = SetupMockData(responseMessage);

            await Should.ThrowAsync<HttpRequestException>(async () => { await accountPeriodService.IsClientOnboarded(TestHelpers.Guids.GuidOne); });
        }

        [Fact]
        public async Task Should_return_clientresponse()
        {
            var clientId = TestHelpers.Guids.GuidOne;

            var expectedResponse = new ClientResponse()
            {
                Id = clientId,
                Name = "Test Client",
                BusinessType = "Limited",
                LimitedCompanyType = "Private",
                RegisteredNo = "********",
            };

            var responseMessage = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedResponse, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase }))
            };

            var accountPeriodService = SetupMockData(responseMessage);
            var result = await accountPeriodService.GetClientAsync(clientId);

            Assert.NotNull(result);
            Assert.Equal(expectedResponse.Id, result.Id);
            Assert.Equal(expectedResponse.Name, result.Name);
            Assert.Equal(expectedResponse.BusinessType, result.BusinessType);
            Assert.Equal(expectedResponse.LimitedCompanyType, result.LimitedCompanyType);
            Assert.Equal(expectedResponse.RegisteredNo, result.RegisteredNo);
        }


        private IAccountPeriodService SetupMockData(HttpResponseMessage responseMessage)
        {
            _envVariableProvider.Setup(obj => obj.AwsAccessKey).Returns("accesskey");
            _envVariableProvider.Setup(obj => obj.AwsRegion).Returns("eu-west-2");
            _envVariableProvider.Setup(obj => obj.AwsSecretKey).Returns("secretkey");
            _envVariableProvider.Setup(obj => obj.AwsSessionToken).Returns("session");
            _envVariableProvider.Setup(provider => provider.AccountPeriodApiScheme).Returns("https");
            _envVariableProvider.Setup(provider => provider.AccountPeriodApiHost).Returns("api.elements-development.iris.co.uk/accountsproduction-accountperiod/v1");
            _envVariableProvider.Setup(provider => provider.AccountPeriodApiKey).Returns("test");
            _envVariableProvider.Setup(provider => provider.AccountPeriodApiId).Returns("test");

            var mockFactory = new Mock<IHttpClientFactory>();
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(responseMessage)
                .Verifiable();
            var client = new HttpClient(_mockHttpMessageHandler.Object);
            mockFactory.Setup(_ => _.CreateClient(It.IsAny<string>())).Returns(client);

            var accountPeriodService = new AccountPeriodService(_envVariableProvider.Object, client, _userContext.Object, _logger.Object);
            return accountPeriodService;
        }

    }
}
