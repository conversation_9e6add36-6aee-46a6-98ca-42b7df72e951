﻿using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NonFinancialRunner
{
    public class NonFinancialValidationRunner : BaseNonFinancialValidationRunner
    {
        protected override Dictionary<string, Func<Domain.AccountsBuilderModels.AccountsBuilder, ValidationIssue>> ValidationRules
        {
            get
            {
                var validationRules = base.ValidationRules;

                validationRules.Add(NonFinancialValidations.CompanyNumber,
                    accountsBuilder => Validator.ValidateForNull(accountsBuilder.NonFinancialData.RegisteredNo, NonFinancialValidations.CompanyNumberRuleConfig));

                return validationRules;
            }
        }
    }
}