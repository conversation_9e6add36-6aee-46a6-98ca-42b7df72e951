﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.PracticeDetails;
using AccountsProduction.AccountsBuilder.Domain;
using Amazon.DynamoDBv2.Model;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.AggregationStrategies
{
    public class PracticeDetailsDataEventStrategyTests
    {
        private readonly Mock<IAccountsBuilderRepository> _repository;
        private readonly Mock<ILogger<PracticeDetailsDataEventStrategy>> _logger;
        private readonly IMapper _mapper;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = Guid.NewGuid();
        private readonly Guid _processId = TestHelpers.Guids.GuidThree;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;

        public PracticeDetailsDataEventStrategyTests()
        {
            var mapperConfig = new MapperConfiguration(cfg =>
                {
                    cfg.AddProfile<PracticeDetailsMapper>();
                }
            );

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();

            _repository = new Mock<IAccountsBuilderRepository>();
            _logger = new Mock<ILogger<PracticeDetailsDataEventStrategy>>();
        }

        [Fact]
        public async Task Should_update_accounts_production_data()
        {

            _repository.Setup(repository => repository.GetByTenantId(_tenantId, CancellationToken.None))
                .ReturnsAsync(new List<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(){
                    new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId,
                    _periodId, new EntitySetup() { PracticeAddress = "0f5fbc2a-ff4e-4ad3-9b1f-82cbbd2fbb38" })
                });

            var requestMessage = GetRequestMessage();
            var strategy = new PracticeDetailsDataEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(
                repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Once);
        }

        [Fact]
        public async Task Should_update_accounts_production_data_when_practice_address_is_null_and_message_contains_primary_address()
        {

            _repository.Setup(repository => repository.GetByTenantId(_tenantId, CancellationToken.None))
                .ReturnsAsync(new List<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(){
                    new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId,
                    _periodId, new EntitySetup() { PracticeAddress = string.Empty })
                });

            var requestMessage = GetRequestMessage();
            var strategy = new PracticeDetailsDataEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(
                repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Once);
        }

        [Fact]
        public async Task Should_not_update_accounts_production_data_if_concurreny_occures()
        {
            var accountsBuilder = new AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder(_tenantId.ToString(), _clientId, _periodId, new EntitySetup() { PracticeAddress = "0f5fbc2a-ff4e-4ad3-9b1f-82cbbd2fbb38" });
            _repository.Setup(repository => repository.GetByTenantId(_tenantId, CancellationToken.None))
                .ReturnsAsync(new List<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(){
                    accountsBuilder
                });

            _repository.Setup(repository => repository.Get(It.IsAny<Guid>(), It.IsAny<Guid>(), CancellationToken.None))
                .ReturnsAsync(accountsBuilder);

            _repository.Setup(repository => repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None))
                .Callback(() => throw new ConditionalCheckFailedException("test"));

            var requestMessage = GetRequestMessage();
            var strategy = new PracticeDetailsDataEventStrategy(_repository.Object, _logger.Object, _mapper);

            await strategy.Process(requestMessage, _tenantId, _clientId, _periodId, _processId, CancellationToken.None);

            _repository.Verify(
                repository =>
                    repository.Save(It.IsAny<AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder>(), CancellationToken.None),
                Times.Exactly(4));

        }

        private string GetRequestMessage()
        {
            var message = JsonSerializer.Serialize(new PracticeDetailMessage
            {
                PracticeDetailId = Guid.Parse("0f5fbc2a-ff4e-4ad3-9b1f-82cbbd2fbb38"),
                ActingNames = new ActingNameMessage()
                {
                    ReferredType = Referred.Singular,
                    SupervisoryBody = SupervisoryBody.CIPFA,
                },
                Name = "test",
                Address = new AddressMessage()
                {
                    Address1 = "address1",
                    Address2 = "address2",
                    City = "city1",
                    Country = "country1",
                    County = "county1",
                    Postcode = "123 456"
                },
                IsPrimaryPracticeOffice = true

            }, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });

            return message;
        }
    }
}
