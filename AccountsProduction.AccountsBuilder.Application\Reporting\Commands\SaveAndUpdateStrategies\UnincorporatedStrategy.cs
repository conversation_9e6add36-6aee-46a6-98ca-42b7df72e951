﻿using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies
{

    public class UnincorporatedStrategy : BaseSaveUpdateStrategy
    {
        private readonly ILogger<UnincorporatedStrategy> _logger;
        private readonly IAccountsProductionReportingDbContext _accountsProductionReportingDbContext;

        public UnincorporatedStrategy(IMediator mediator, ILogger<UnincorporatedStrategy> logger, IAccountsProductionReportingDbContext accountsProductionReportingDbContext, IMapper mapper) : base(mediator, logger, accountsProductionReportingDbContext, mapper)
        {
            _logger = logger;
            _accountsProductionReportingDbContext = accountsProductionReportingDbContext;
        }

        public override string Name => ReportType.UNINCORPORATED;

        public override async Task SendSaveAndUpdateDataEvents(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
        {
            _logger.LogInformation("{ProcessTime} Start Unincorporated events for Client {Client} and Period {@Period}", DateTime.UtcNow, data.ClientId, data.PeriodId);

            await base.SendSaveAndUpdateDataEvents(data, cancellationToken);

            _logger.LogInformation("{ProcessTime} Processed SendSaveAndUpdateDataEvents for Unincorporated", DateTime.UtcNow);

            await SaveAndUpdateLineItems(data, cancellationToken);

            _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateLineItems for Unincorporated", DateTime.UtcNow);

            await SaveAndUpdateProfitAndLoss(data, _accountsProductionReportingDbContext.ProfitAndLossesNonCorp, cancellationToken);

            _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateProfitAndLoss for Unincorporated", DateTime.UtcNow);

            await SaveAndUpdateBalanceSheetForNonCorp(data, cancellationToken);

            _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateBalanceSheet and End of Unincorporated events", DateTime.UtcNow);

        }

        private async Task SaveAndUpdateBalanceSheetForNonCorp(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
        {
            if (!data.BalanceSheetData.IsPopulated()) return;

            await SaveAndUpdateBalanceSheet(data, _accountsProductionReportingDbContext.BalanceSheetsGeneral, "BalanceSheet General", cancellationToken);
            await SaveAndUpdateBalanceSheet(data, _accountsProductionReportingDbContext.BalanceSheetsNonCorp, "BalanceSheet NonCorp", cancellationToken);
        }
    }
}
