﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Address;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.NonFinancialData;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;

namespace AccountsProduction.AccountsBuilder.Infrastructure.Services
{
    public class ClientDataService : IClientDataService
    {
        private readonly HttpClient _httpClient;
        private readonly IEnvVariableProvider _envVariableProvider;
        private readonly UserContext _userContext;
        private readonly ILogger<ClientDataService> _logger;

        public ClientDataService(HttpClient client,
            IEnvVariableProvider envVariableProvider,
            UserContext userContext,
            ILogger<ClientDataService> logger)
        {
            _envVariableProvider = envVariableProvider;
            _userContext = userContext;
            _logger = logger;
            _httpClient = client;
            _httpClient.SetupIrisClient(_envVariableProvider.ClientApiKey, _envVariableProvider.ClientApiId, _envVariableProvider.ClientApiScheme, _envVariableProvider.ClientApiHost);
        }

        public async Task<NonFinancialDataDto> GetNonFinancialDataFromClient(Guid clientIdentifier, CancellationToken cancellationToken = default)
        {
            var clientUrl = string.Concat(_httpClient.BaseAddress.ToString(), _envVariableProvider.ClientApiUrl,
                "uuid/", clientIdentifier, "/awsiam");

            _logger.LogInformation("Client url: {clientUrl}", clientUrl);

            var request = _httpClient.CreateGetSignedRequest(clientUrl, _envVariableProvider);
            request.AddRequestHeaders(_userContext);

            _logger.LogInformation("Client request: {request}", request);
            var response = await _httpClient.SendAsync(request, cancellationToken);
            response.EnsureSuccessStatusCode();

            await using var responseStream = await response.Content.ReadAsStreamAsync(cancellationToken);
            return await JsonSerializer.DeserializeAsync<NonFinancialDataDto>(responseStream, new JsonSerializerOptions(), cancellationToken);
        }

        public async Task<List<ClientInvolvementDto>> GetClientInvolvements(Guid clientIdentifier, CancellationToken cancellationToken = default)
        {
            var clientUrl = string.Concat(_httpClient.BaseAddress.ToString(),
                _envVariableProvider.ClientInvolvementApiUrl, "uuid/", clientIdentifier, "/awsiam");

            var request = _httpClient.CreateGetSignedRequest(clientUrl, _envVariableProvider);
            request.AddRequestHeaders(_userContext);

            var response = await _httpClient.SendAsync(request, cancellationToken);
            _logger.LogInformation("Client involvement response code: {responseCode} for URL of: {clientUrl}", response.StatusCode, clientUrl);

            response.EnsureSuccessStatusCode();
            await using var responseStream = await response.Content.ReadAsStreamAsync(cancellationToken);

            var responseDto = await JsonSerializer.DeserializeAsync<List<ClientInvolvementDto>>(responseStream,
                new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase }, cancellationToken);
            return responseDto;
        }

        public async Task<List<AddressResponseDto>> GetClientAddress(Guid clientIdentifier, CancellationToken cancellationToken = default)
        {
            var clientAddressUrl = string.Concat(_httpClient.BaseAddress.ToString(),
                _envVariableProvider.ClientAddressApiUrl, "uuid/", clientIdentifier, "/awsiam");

            var request = _httpClient.CreateGetSignedRequest(clientAddressUrl, _envVariableProvider);
            request.AddRequestHeaders(_userContext);

            var addressResponse = await _httpClient.SendAsync(request, cancellationToken);

            if (addressResponse.StatusCode == HttpStatusCode.NotFound)
            {
                _logger.LogWarning("Client address request returned 404.");
                return new List<AddressResponseDto>();
            }

            _logger.LogInformation("Client address response code: {responseCode} for URL of: {clientUrl}", addressResponse.StatusCode, clientAddressUrl);

            addressResponse.EnsureSuccessStatusCode();

            await using var responseStream = await addressResponse.Content.ReadAsStreamAsync(cancellationToken);

            var result = await JsonSerializer.DeserializeAsync<List<AddressResponseDto>>(responseStream,
                new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase }, cancellationToken);
            return result;
        }

    }
}
