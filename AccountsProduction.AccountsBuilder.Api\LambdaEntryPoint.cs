﻿using Amazon.Lambda.APIGatewayEvents;
using Amazon.Lambda.Core;
using Iris.Elements.Logging.Serilog.AspNet;
using Microsoft.AspNetCore.Http.Features;
using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.Api
{
    /// <summary>
    /// This class extends from APIGatewayProxyFunction which contains the method FunctionHandlerAsync which is the 
    /// actual Lambda function entry point. The Lambda handler field should be set to
    /// 
    /// svc::iriswebservices/accountsproduction.accountsbuilder.func.LambdaEntryPoint::FunctionHandlerAsync
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class LambdaEntryPoint :

        // The base class must be set to match the AWS service invoking the Lambda function. If not Amazon.Lambda.AspNetCoreServer
        // will fail to convert the incoming request correctly into a valid ASP.NET Core request.
        //
        // API Gateway REST API                         -> Amazon.Lambda.AspNetCoreServer.APIGatewayProxyFunction
        // API Gateway HTTP API payload version 1.0     -> Amazon.Lambda.AspNetCoreServer.APIGatewayProxyFunction
        // API Gateway HTTP API payload version 2.0     -> Amazon.Lambda.AspNetCoreServer.APIGatewayHttpApiV2ProxyFunction
        // Application Load Balancer                    -> Amazon.Lambda.AspNetCoreServer.ApplicationLoadBalancerFunction
        // 
        // Note: When using the AWS::Serverless::Function resource with an event type of "HttpApi" then payload version 2.0
        // will be the default and you must make Amazon.Lambda.AspNetCoreServer.APIGatewayHttpApiV2ProxyFunction the base class.

        Amazon.Lambda.AspNetCoreServer.APIGatewayProxyFunction
    {
        /// <summary>
        /// The builder has configuration, logging and Amazon API Gateway already configured. The startup class
        /// needs to be configured in this method using the UseStartup{TStartup}() method.
        /// </summary>
        /// <param name="builder"></param>
        protected override void Init(IWebHostBuilder builder)
        {
            builder.UseStartup<Startup>();
        }

        /// <summary>
        /// Use this override to customize the services registered with the IHostBuilder. 
        /// It is recommended not to call ConfigureWebHostDefaults to configure the IWebHostBuilder inside this method.
        /// Instead customize the IWebHostBuilder in the Init(IWebHostBuilder) overload.
        /// </summary>
        /// <param name="builder"></param>
        protected override void Init(IHostBuilder builder)
        {
            builder.AddSerilog();
        }

        /// <summary>
        /// This method is called after marshalling the incoming Lambda request into ASP.NET Core's IHttpRequestFeature.
        /// Modifies the PathBase of the incoming request to include api gateway and stage, i.e. /tax-individual/{stage}.
        /// PathBase is subtracted from the Path so underlying controller routing matches the requested Path.
        /// </summary>
        /// <param name="aspNetCoreRequestFeature"></param>
        /// <param name="lambdaRequest"></param>
        /// <param name="lambdaContext"></param>
        protected override void PostMarshallRequestFeature(IHttpRequestFeature aspNetCoreRequestFeature, APIGatewayProxyRequest lambdaRequest, ILambdaContext lambdaContext)
        {
            aspNetCoreRequestFeature.PathBase =
               $"/accountsproduction-accountsbuilder/{lambdaRequest?.RequestContext?.Stage}";

            if (aspNetCoreRequestFeature.Path.StartsWith(aspNetCoreRequestFeature.PathBase))
            {
                aspNetCoreRequestFeature.Path =
                    aspNetCoreRequestFeature.Path[aspNetCoreRequestFeature.PathBase.Length..];
            }

            lambdaContext.Logger.LogLine(
                $"Path: {aspNetCoreRequestFeature.Path}, PathBase: {aspNetCoreRequestFeature.PathBase}");
        }
    }
}
