﻿using AccountsProduction.AccountsBuilder.Domain.Reporting.Other;
using AccountsProduction.AccountsBuilder.Reporting.Domain;
using AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet;
using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using AccountsProduction.AccountsBuilder.Reporting.Domain.ProfitAndLoss;
using Microsoft.EntityFrameworkCore;

namespace AccountsProduction.AccountsBuilder.Reporting.Application
{
    public interface IAccountsProductionReportingDbContext
    {
        DbSet<Tenant> Tenants { get; set; }

        DbSet<Client> Clients { get; set; }

        DbSet<ReportingPeriod> ReportingPeriods { get; set; }

        DbSet<ProfitAndLossFRS105> ProfitAndLossesFRS105 { get; set; }

        DbSet<ProfitAndLossCompaniesAct> ProfitAndLossesCompaniesActs { get; set; }

        DbSet<ProfitAndLossNonCorp> ProfitAndLossesNonCorp { get; set; }

        DbSet<BalanceSheetFRS105> BalanceSheetsFRS105 { get; set; }

        DbSet<BalanceSheetGeneral> BalanceSheetsGeneral { get; set; }

        DbSet<BalanceSheetLLP> BalanceSheetsLLP { get; set; }

        DbSet<BalanceSheetCH> BalanceSheetsCH { get; set; }

        DbSet<BalanceSheetNonCorp> BalanceSheetsNonCorp { get; set; }

        DbSet<BalanceSheetNonCorp> BalanceSheetsIFRS { get; set; }

        DbSet<Member> Members { get; set; }

        DbSet<Signature> Signatures { get; set; }

        DbSet<NoteAccountingPolicies> NotesAccountingPolicies { get; set; }

        DbSet<NoteProfitAndLoss> NotesProfitAndLoss { get; set; }

        DbSet<NoteBalanceSheet> NotesBalanceSheet { get; set; }

        DbSet<NoteOther> NotesOther { get; set; }

        DbSet<Reports> Reports { get; set; }

        DbSet<MultiColumnToken> MultiColumnToken { get; set; }

        DbSet<DplSummaryCalcs> DplSummaryCalcs { get; set; }

        DbSet<LineItem> LineItems { get; set; }

        Task<int> SaveChangesAsync(CancellationToken cancellationToken);

    }
}
