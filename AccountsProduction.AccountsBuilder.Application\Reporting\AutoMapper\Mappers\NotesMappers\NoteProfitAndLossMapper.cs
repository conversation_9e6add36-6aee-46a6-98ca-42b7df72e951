﻿using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper.Mappers.NotesMappers
{
    public class NoteProfitAndLossMapper : Profile
    {
        public NoteProfitAndLossMapper()
        {
            CreateMap<NotesResponseDataMessage, List<NoteProfitAndLoss>>()
                .ConvertUsing<NotesDtoToListOfNoteProfitAndLossConverter>();
        }

        public class NotesDtoToListOfNoteProfitAndLossConverter : ITypeConverter<NotesResponseDataMessage, List<NoteProfitAndLoss>>
        {
            public List<NoteProfitAndLoss> Convert(NotesResponseDataMessage source, List<NoteProfitAndLoss> destination, ResolutionContext context)
            {
                var noteProfitAndLosses = ExtractAverageNumberOfEmployees(source);
                noteProfitAndLosses.AddRange(ExtractOperatingProfitLoss(source));
                return noteProfitAndLosses;
            }

            private List<NoteProfitAndLoss> ExtractAverageNumberOfEmployees(NotesResponseDataMessage source)
            {
                var listOfNotes = new List<NoteProfitAndLoss>();

                listOfNotes.PopulateWhenNotePresent("AverageNumberOfEmployees", source.AverageNumberOfEmployees?.CurrentPeriod);

                listOfNotes.PopulateWhenNotePresent("PreviousAverageNumberOfEmployees", source.AverageNumberOfEmployees?.PreviousPeriod);

                return listOfNotes;
            }

            private List<NoteProfitAndLoss> ExtractOperatingProfitLoss(NotesResponseDataMessage source)
            {
                var listOfNotes = new List<NoteProfitAndLoss>();

                if (source.OperatingProfitLoss == null)
                {
                    return listOfNotes;
                }

                listOfNotes.Add(new NoteProfitAndLoss
                {
                    NoteType = "OperatingProfitNoteYesNo",
                    NoteText = source.OperatingProfitLoss.IsEnabled ? "Yes" : "No"
                });

                if (!source.OperatingProfitLoss.Items.IsPopulated())
                {
                    return listOfNotes;
                }

                foreach (var operatingProfitLossItem in source.OperatingProfitLoss.Items)
                {
                    listOfNotes.PopulateWhenNotePresent($"OperatingProfitExtraItem{operatingProfitLossItem.Index}", operatingProfitLossItem.Description, operatingProfitLossItem.Value);
                }

                return listOfNotes;
            }

        }
    }
}
