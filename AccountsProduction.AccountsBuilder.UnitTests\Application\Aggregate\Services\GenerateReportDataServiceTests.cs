﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Address;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.PracticeDetails;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.TrialBalance;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Infrastructure.Services;
using Iris.Platform.Eventbus.Client.Dotnet.Client;
using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.EventBusClient;
using Iris.Platform.Eventbus.Client.Dotnet.Common.Models.Message;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using System.Net;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Aggregate.Services
{
    public class GenerateReportDataServiceTests
    {
        private readonly Mock<ILogger<GenerateReportDataService>> _logger;
        private readonly Mock<IAccountPeriodService> _accountPeriodService;
        private readonly Mock<ITrialBalanceService> _trialBalanceService;
        private readonly Mock<UserContext> _userContext;
        private readonly Mock<IAccountsProductionService> _accountsProductionService;
        private readonly Mock<IEventBusClient> _eventBusClient;
        private readonly Mock<IClientDataService> _clientDataService;
        private readonly Guid _clientId = TestHelpers.Guids.GuidOne;
        private readonly Guid _periodId = TestHelpers.Guids.GuidTwo;
        private readonly Guid _tenantId = TestHelpers.Guids.GuidFour;
        
        private List<PracticeDetailMessage> data = new List<PracticeDetailMessage>();

        public GenerateReportDataServiceTests()
        {
            _userContext = new Mock<UserContext>();
            _accountsProductionService = new Mock<IAccountsProductionService>();
            _accountPeriodService = new Mock<IAccountPeriodService>();
            _trialBalanceService = new Mock<ITrialBalanceService>();
            _eventBusClient = new Mock<IEventBusClient>();
            _clientDataService = new Mock<IClientDataService>();
            _logger = new Mock<ILogger<GenerateReportDataService>>();

            InitData();
        }
        private void InitData()
        {
            data = new List<PracticeDetailMessage>()
            {
                new PracticeDetailMessage
                {
                    PracticeDetailId = Guid.NewGuid(),
                    Name = "test",
                    IsPrimaryPracticeOffice = false
                },
                new PracticeDetailMessage
                {
                    PracticeDetailId = Guid.NewGuid(),
                    Name = "primary",
                    IsPrimaryPracticeOffice = true
                }
            };

            _userContext.Setup(q => q.TenantId).Returns(_tenantId.ToString());
            _accountPeriodService.Setup(x => x.GetClientAsync(It.IsAny<Guid>())).ReturnsAsync(new ClientResponse
            {
                Id = Guid.NewGuid()
            });
            _trialBalanceService.Setup(x => x.GetTrialBalanceCalculate(It.IsAny<Guid>(), It.IsAny<Guid>())).ReturnsAsync(new TrialBalanceDto
            {
                AccountsChartId = 1,
                AccountsChartIdentifier = "ELTD",
                TrialBalances = new List<PeriodTrialBalanceDto>()
            });

            var result = new PublishWithResponse<List<PracticeDetailMessage>> { Status = HttpStatusCode.OK, Payload = new List<PracticeDetailMessage>(data) };
            _eventBusClient.Setup(x => x.PublishWithResponse<string, List<PracticeDetailMessage>>(It.IsAny<EventBusMessage<string>>(), It.IsAny<int>())).ReturnsAsync(result);


            _accountsProductionService.Setup(c => c.GetInvolvements(It.IsAny<Guid>(), It.IsAny<bool>()))
                .ReturnsAsync(new List<InvolvementDto>
                {
                    new InvolvementDto
                    {
                        Id = 1
                    }
                });


            _clientDataService.Setup(c => c.GetClientAddress(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<AddressResponseDto>
                {
                        new AddressResponseDto
                        {
                            Document = new AddressDocumentResponseDto
                            {
                                Document = "test"
                            }
                        }
                });
        }

        [Fact]
        public async Task Should_return_data_with_practice_address()
        {
            _accountPeriodService.Setup(x => x.GetEntitySetupAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).ReturnsAsync(new EntitySetupDto
            {
                ReportingStandard = "FRS105",
                EntitySize = "Micro",
                IndependentReviewType = "Accountants",
                TradingStatus = "Trading",
                Terminology = "Companies Act",
                PracticeAddress = data.First().PracticeDetailId.ToString()
            });

            var clientId = TestHelpers.Guids.GuidOne;
            var periodId = TestHelpers.Guids.GuidTwo;

            var serivce = new GenerateReportDataService(_userContext.Object, _logger.Object, _accountPeriodService.Object, _clientDataService.Object, _accountsProductionService.Object, _eventBusClient.Object, _trialBalanceService.Object);

            var result = await serivce.GetReportData(clientId, periodId);

            result.ShouldNotBeNull();
            result.PracticeDetailsDto.ShouldNotBeNull();
            result.PracticeDetailsDto.Name.ShouldBe("test");
        }

        [Fact]
        public async Task Should_return_data_without_practice_address()
        {
            _accountPeriodService.Setup(x => x.GetEntitySetupAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).ReturnsAsync(new EntitySetupDto
            {
                ReportingStandard = "FRS105",
                EntitySize = "Micro",
                IndependentReviewType = "Accountants",
                TradingStatus = "Trading",
                Terminology = "Companies Act",
                PracticeAddress = null
            });

            var clientId = TestHelpers.Guids.GuidOne;
            var periodId = TestHelpers.Guids.GuidTwo;

            var serivce = new GenerateReportDataService(_userContext.Object, _logger.Object, _accountPeriodService.Object, _clientDataService.Object, _accountsProductionService.Object, _eventBusClient.Object, _trialBalanceService.Object);

            var result = await serivce.GetReportData(clientId, periodId);

            result.ShouldNotBeNull();
            result.PracticeDetailsDto.ShouldNotBeNull();
            result.PracticeDetailsDto.Name.ShouldBe("primary");
        }
    }
}
