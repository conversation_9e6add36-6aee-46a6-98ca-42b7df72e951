﻿namespace AccountsProduction.AccountsBuilder.Domain.NoteModels
{
    public abstract class FRS102And1ABaseNotesData : IFRS102NotesData, IFRS1021ANotesData
    {
        public AverageNumberOfEmployees? AverageNumberOfEmployees { get; set; }
        public string? OffBalanceSheetArrangements { get; set; }
        public string? GuaranteesAndOtherFinancialCommitments { get; set; }
        public MembersLiabilityText? MembersLiabilityText { get; set; }
        public AdditionalNote? AdditionalNote1 { get; set; }
        public AdditionalNote? AdditionalNote2 { get; set; }
        public string? ControllingPartyNote { get; set; }
        public string? RelatedPartyTransactions { get; set; }
        public string? LoansAndOtherDebtsDueToMembers { get; set; }
        public OperatingProfitLoss? OperatingProfitLoss { get; set; }
        public string? IntangibleAssetsRevaluation { get; set; }
        public AdvancesCreditAndGuaranteesGrantedToDirectors? AdvancesCreditAndGuaranteesGrantedToDirectorsExtended { get; set; }
        public TangibleFixedAssetsNotes? TangibleFixedAssetsNotes { get; set; }
    }
}
