﻿using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Domain.Reporting.Other;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies;

public abstract class FRS102BaseStrategy : BaseSaveUpdateStrategy
{
    public abstract string ReportTypeName { get; }

    private readonly ILogger<FRS102BaseStrategy> _logger;
    private readonly IAccountsProductionReportingDbContext _accountsProductionReportingDbContext;
    private readonly IMapper _mapper;

    protected FRS102BaseStrategy(
        IMediator mediator,
        ILogger<FRS102BaseStrategy> logger,
        IAccountsProductionReportingDbContext accountsProductionReportingDbContext,
        IMapper mapper) : base(mediator, logger, accountsProductionReportingDbContext, mapper)
    {
        _logger = logger;
        _accountsProductionReportingDbContext = accountsProductionReportingDbContext;
        _mapper = mapper;
    }

    public override string Name => ReportTypeName;

    public async Task SendFRS102BaseSaveAndUpdateDataEvents(AccountsBuilderReportingMessageDto data, string reportType, CancellationToken cancellationToken)
    {
        await base.SendSaveAndUpdateDataEvents(data, cancellationToken);

        await SaveAndUpdateOtherDplCalc(data, cancellationToken);
        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateOthers for {reportType}", DateTime.UtcNow, reportType);

        await SaveAndUpdateLineItems(data, cancellationToken);
        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateLineItems", DateTime.UtcNow);

        await SaveAndUpdateProfitAndLoss(data, _accountsProductionReportingDbContext.ProfitAndLossesCompaniesActs, cancellationToken);
        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateProfitAndLoss for {reportType}", DateTime.UtcNow, reportType);

        await SaveAndUpdateBalanceSheet(data, cancellationToken);
        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateBalanceSheet for {reportType}", DateTime.UtcNow, reportType);

        await SaveAndUpdateNotes(data, cancellationToken);
        _logger.LogInformation("{ProcessTime} Processed SaveAndUpdateNotes for {reportType}", DateTime.UtcNow, reportType);
    }

    protected virtual async Task SaveAndUpdateOtherDplCalc(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Saving other data screen values.");

        if (!data.OtherData.Any()) return;

        if (data.ReportingPeriods == null) { throw new InvalidOperationException(); }

        RemoveExistingOtherDplCalc(null);

        await ExtractAndStoreOtherDplCalc(data, cancellationToken);
    }

    private void RemoveExistingOtherDplCalc(ICollection<DplSummaryCalcs>? calcs)
    {
        if (calcs != null)
        {
            foreach (var calc in calcs)
            {
                var existingItems = _accountsProductionReportingDbContext.DplSummaryCalcs
                                    .Where(x => x.ClientId == calc.ClientId && x.AccountPeriodId == calc.AccountPeriodId)
                                    .ToList();
                if (existingItems.Any())
                {
                    _accountsProductionReportingDbContext.DplSummaryCalcs.RemoveRange(existingItems);
                }
            }
        }
        else
        {
            var dplSummaryCalcs = ReportingPeriod?.DplSummaryCalcs;
            if (dplSummaryCalcs != null && dplSummaryCalcs.Any())
                _accountsProductionReportingDbContext.DplSummaryCalcs.RemoveRange(dplSummaryCalcs);
        }
    }

    private async Task ExtractAndStoreOtherDplCalc(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        var dplSummaryCalcs = GetOtherDplCalcs(data);
        if (!dplSummaryCalcs.Any())
        {
            _logger.LogInformation("DPLSummaryCalcs are empty!");
            return;
        }

        RemoveExistingOtherDplCalc(dplSummaryCalcs);

        _logger.LogInformation("Storing {Count} DLPSummaryCalcs.", dplSummaryCalcs.Count);
        await _accountsProductionReportingDbContext.DplSummaryCalcs.AddRangeAsync(dplSummaryCalcs, cancellationToken);
    }

    private List<DplSummaryCalcs> GetOtherDplCalcs(AccountsBuilderReportingMessageDto data)
    {
        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);
        var previousPeriod = data.ReportingPeriods.MinBy(x => x.EndDate);

        foreach (var item in data.OtherData)
        {
            if (item.Period.Date == currentPeriod?.EndDate.Date)
            {
                item.PeriodId = currentPeriod.Id;
            }
            else
            {
                item.PeriodId = previousPeriod.Id;
            }
        }

        var dplSummaryCalcs = new List<DplSummaryCalcs>();
        var mappedDplSummaryCalcs = MapTotOtherDplCalc(data);
        dplSummaryCalcs.AddRange(mappedDplSummaryCalcs);
        dplSummaryCalcs.ForEach(x => { x.ClientId = data.ClientId; });
        _logger.LogInformation("dplSummaryCalcs items count: {Count}", dplSummaryCalcs.Count);
        return dplSummaryCalcs;
    }

    protected virtual List<DplSummaryCalcs> MapTotOtherDplCalc(AccountsBuilderReportingMessageDto data)
    {
        var uniqueOtherData = data.OtherData.DistinctBy(o => o.PeriodId).ToList();
        var dplSummaryCalcs = _mapper.Map<List<DplSummaryCalcs>>(uniqueOtherData);
        _logger.LogInformation("Mapped {Count} Line Items", dplSummaryCalcs.Count);
        return dplSummaryCalcs;
    }

    protected async Task SaveAndUpdateBalanceSheet(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        if (!data.BalanceSheetData.IsPopulated()) return;

        await SaveAndUpdateBalanceSheet(data, _accountsProductionReportingDbContext.BalanceSheetsGeneral, "BalanceSheet General", cancellationToken);
        await SaveAndUpdateBalanceSheet(data, _accountsProductionReportingDbContext.BalanceSheetsLLP, "BalanceSheet LLP", cancellationToken);
    }

    protected async Task SaveAndUpdateNotes(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        var dataScreenNoteLists = SaveAndUpdateNoteDataScreenValues(data);

        var noteOther = await MapToNoteOther(data);
        noteOther.AddRange(dataScreenNoteLists.NotesOther);

        var noteProfitAndLoss = await MapToNoteProfitAndLoss(data);
        noteProfitAndLoss.AddRange(dataScreenNoteLists.NotesProfitAndLoss);

        var noteBalanceSheet = await MapToNoteBalanceSheet(data);
        noteBalanceSheet.AddRange(dataScreenNoteLists.NotesBalanceSheet);

        var noteAccountingPolicies = await MapToAccountingPolicies(data);
        noteAccountingPolicies.AddRange(dataScreenNoteLists.NotesAccountingPolicies);

        var reports = dataScreenNoteLists.Reports;
        reports = AddSeniorStatutoryAuditorToReports(data, reports);

        CleanupAllExistingNotes();

        if (noteOther.IsPopulated())
        {
            _logger.LogInformation("Storing {noteOtherCount} NotesOther", noteOther.Count);
            await _accountsProductionReportingDbContext.NotesOther.AddRangeAsync(noteOther, cancellationToken);
        }

        if (noteProfitAndLoss.IsPopulated())
        {
            _logger.LogInformation("Storing {noteProfitAndLossCount} NotesProfitAndLoss.", noteProfitAndLoss.Count);
            await _accountsProductionReportingDbContext.NotesProfitAndLoss.AddRangeAsync(noteProfitAndLoss, cancellationToken);
        }

        if (noteBalanceSheet.IsPopulated())
        {
            _logger.LogInformation("Storing {noteBalanceSheetCount} NotesBalanceSheet", noteBalanceSheet.Count);
            await _accountsProductionReportingDbContext.NotesBalanceSheet.AddRangeAsync(noteBalanceSheet, cancellationToken);
        }

        if (noteAccountingPolicies.IsPopulated())
        {
            _logger.LogInformation("Storing {noteAccountingPoliciesCount} NotesProfitAndLoss", noteAccountingPolicies.Count);
            await _accountsProductionReportingDbContext.NotesAccountingPolicies.AddRangeAsync(noteAccountingPolicies, cancellationToken);
        }

        if (reports.IsPopulated())
        {
            _logger.LogInformation("Storing {reportsCount} Reports", reports.Count);
            await _accountsProductionReportingDbContext.Reports.AddRangeAsync(reports, cancellationToken);
        }
    }
}
