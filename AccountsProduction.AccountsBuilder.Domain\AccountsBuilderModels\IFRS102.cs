﻿using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels;
using AccountsProduction.AccountsBuilder.Domain.NoteModels;

namespace AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels
{
    public interface IFRS102 : IAccountsBuilderReportData, IPartnerProfitShareReportData
    {
        public AccountingPolicies AccountingPolicies { get; set; }
        public Notes Notes { get; set; }
    }
}