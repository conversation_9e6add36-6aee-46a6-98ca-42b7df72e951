﻿using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.Common
{
    public static class ProfitAndLossMessageExtension
    {
        public static Guid PeriodId { get; set; }
        public static DateTime Period { get; set; }
        public static decimal TurnoverNumber(this ProfitAndLossMessage palm) => (palm?.Turnover?.Value).ToDecimal();
        public static decimal OtherIncomeNumber(this ProfitAndLossMessage palm) => (palm?.OtherIncome?.Value).ToDecimal();
        public static decimal CostOfRawMaterialsAndConsumablesNumber(this ProfitAndLossMessage palm) => (palm?.CostOfRawMaterialsAndConsumables?.Value).ToDecimal();
        public static decimal StaffCostsNumber(this ProfitAndLossMessage palm) => (palm?.StaffCosts?.Value).ToDecimal();
        public static decimal DepreciationAndOtherAmountsWrittenOffAssetsNumber(this ProfitAndLossMessage palm) => (palm?.DepreciationAndOtherAmountsWrittenOffAssets?.Value).ToDecimal();
        public static decimal OtherChargesNumber(this ProfitAndLossMessage palm) => (palm?.OtherCharges?.Value).ToDecimal();
        public static decimal TaxNumber(this ProfitAndLossMessage palm) => (palm?.Tax?.Value).ToDecimal();
        public static decimal SalesNumber(this ProfitAndLossMessage palm) => (palm?.Sales?.Value).ToDecimal();
        public static decimal CostOfSalesNumber(this ProfitAndLossMessage palm) => (palm?.CostOfSales?.Value).ToDecimal();
        public static decimal ExpensesNumber(this ProfitAndLossMessage palm) => (palm?.Expenses?.Value).ToDecimal();
        public static decimal FinanceCostsNumber(this ProfitAndLossMessage palm) => (palm?.FinanceCosts?.Value).ToDecimal();
        public static decimal PartnerAppropriationsNumber(this ProfitAndLossMessage palm) => (palm?.PartnerAppropriations?.Value).ToDecimal();
        public static decimal DepreciationNumber(this ProfitAndLossMessage palm) => (palm?.Depreciation?.Value).ToDecimal();
        public static decimal GrossProfitLossNumber(this ProfitAndLossMessage palm) => (palm?.GrossProfitLoss?.Value).ToDecimal();
        public static decimal DistributionExpensesNumber(this ProfitAndLossMessage palm) => (palm?.DistributionExpenses?.Value).ToDecimal();
        public static decimal AdministrativeExpensesNumber(this ProfitAndLossMessage palm) => (palm?.AdministrativeExpenses?.Value).ToDecimal();
        public static decimal OtherOperatingIncomeNumber(this ProfitAndLossMessage palm) => (palm?.OtherOperatingIncome?.Value).ToDecimal();
        public static decimal GainLossOnRevaluation1Number(this ProfitAndLossMessage palm) => (palm?.GainLossOnRevaluation1?.Value).ToDecimal();
        public static decimal OperatingProfitLossNumber(this ProfitAndLossMessage palm) => (palm?.OperatingProfitLoss?.Value).ToDecimal();
        public static decimal ExceptionalItemsNumber(this ProfitAndLossMessage palm) => (palm?.ExceptionalItems?.Value).ToDecimal();
        public static decimal IncomeFromSharesInGroupUndertakingsNumber(this ProfitAndLossMessage palm) => (palm?.IncomeFromSharesInGroupUndertakings?.Value).ToDecimal();
        public static decimal IncomeFromInterestInAssociatedUndertakingsNumber(this ProfitAndLossMessage palm) => (palm?.IncomeFromInterestInAssociatedUndertakings?.Value).ToDecimal();
        public static decimal IncomeFromOtherParticipatingInterestsNumber(this ProfitAndLossMessage palm) => (palm?.IncomeFromOtherParticipatingInterests?.Value).ToDecimal();
        public static decimal IncomeFromFixedAssetInvestmentsNumber(this ProfitAndLossMessage palm) => (palm?.IncomeFromFixedAssetInvestments?.Value).ToDecimal();
        public static decimal InterestReceivableAndSimilarIncomeNumber(this ProfitAndLossMessage palm) => (palm?.InterestReceivableAndSimilarIncome?.Value).ToDecimal();
        public static decimal OtherFinanceIncomeNumber(this ProfitAndLossMessage palm) => (palm?.OtherFinanceIncome?.Value).ToDecimal();
        public static decimal AmountsWrittenOffInvestmentsNumber(this ProfitAndLossMessage palm) => (palm?.AmountsWrittenOffInvestments?.Value).ToDecimal();
        public static decimal GainLossOnRevaluation2Number(this ProfitAndLossMessage palm) => (palm?.GainLossOnRevaluation2?.Value).ToDecimal();
        public static decimal InterestPayableAndSimilarExpensesNumber(this ProfitAndLossMessage palm) => (palm?.InterestPayableAndSimilarExpenses?.Value).ToDecimal();
        public static decimal OtherFinanceCostsNumber(this ProfitAndLossMessage palm) => (palm?.OtherFinanceCosts?.Value).ToDecimal();
        public static decimal ProfitLossOnOrdinaryActivitiesBeforeTaxationNumber(this ProfitAndLossMessage palm) => (palm?.ProfitLossOnOrdinaryActivitiesBeforeTaxation?.Value).ToDecimal();
        public static decimal TaxationNumber(this ProfitAndLossMessage palm) => (palm?.Taxation?.Value).ToDecimal();
        public static decimal ProfitLossForTheFinancialYearNumber(this ProfitAndLossMessage palm) => (palm?.ProfitLossForTheFinancialYear?.Value).ToDecimal();
        public static decimal ProfitLossAvailableForDiscretionaryDivisionNumber(this ProfitAndLossMessage palm) => (palm?.ProfitLossAvailableForDiscretionaryDivision?.Value).ToDecimal();
        public static decimal NonControllingInterestsNumber(this ProfitAndLossMessage palm) => (palm?.NonControllingInterests?.Value).ToDecimal();
        public static decimal MembersRemunerationAsExpenseNumber(this ProfitAndLossMessage palm) => (palm?.MembersRemunerationAsExpense?.Value).ToDecimal();
    }
}