﻿namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations
{
    public static class ValidationCodes
    {
        public const string CompanyNumber = "ERR1010101";
        public const string BusinessName = "ERR1010102";
        public const string BusinessType = "ERR1010103";
        public const string AddressLine1 = "ERR1010104";
        public const string CityTown = "ERR1010105";
        public const string Postcode = "ERR1010106";
        public const string Director = "ERR1010207";
        public const string DirectorActive = "ERR1010208";
        public const string DesignatedMember = "ERR1010309";
        public const string DesignatedMemberActive = "ERR1010310";
        public const string Proprietor = "ERR1010411";
        public const string Partner = "ERR1010512";
        public const string PartnerActive = "ERR1010513";
        public const string UnallocatedProfit = "ERR1010514";
        public const string AverageNumberOfEmployeesCurrentPeriod = "ERR1020101";
        public const string AverageNumberOfEmployeesPreviousPeriod = "ERR1020102";
        public const string AdvancesCreditAndGuaranteesGrantedForDirectors = "ERR1020203";
        public const string IntangibleAssets = "WRN2030101";
        public const string TangibleFixedAssets = "WRN2030201";
        public const string TangibleFixedAssetsLandAndBuildings = "WRN2030202";
        public const string TangibleFixedAssetsPlantAndMachineries = "WRN2030206";
        public const string BalanceSheet = "WRN2040001";
        public const string IntangibleAssetsRevaluation = "WRN2050001";
        public const string TangibleFixedAssetsNotes = "WRN2050002";
        public const string IntangibleAssetsGoodwill = "ERR1030102";
        public const string IntangibleAssetsPatentsAndLicenses = "ERR1030103";
        public const string IntangibleAssetsDevelopmentCosts = "ERR1030104";
        public const string IntangibleAssetsComputerSoftware = "ERR1030105";
        public const string TangibleFixedAssetsAnalysisOfCostAndValuationNotesNotComplete = "ERR1030106";
        public const string TangibleFixedAssetsHistoricalCostBreakdownNotesNotComplete = "ERR1030107";
        public const string FreeholdProperty = "ERR1030203";
        public const string ShortLeaseholdProperty = "ERR1030204";
        public const string LongLeaseholdProperty = "ERR1030205";
        public const string ImprovementsToProperty = "ERR1030207";
        public const string PlantAndMachinery = "ERR1030208";
        public const string FixturesAndFittings = "ERR1030209";
        public const string MotorVehicles = "ERR1030210";
        public const string ComputerEquipment = "ERR1030211";
        public const string MembersTransations = "ERR1030212";
    }
}
