﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NonFinancialRunner
{
    public static class NonFinancialValidations
    {
        public const string BusinessName = "BusinessName";
        public const string CompanyNumber = "CompanyNumber";
        public const string BusinessType = "BusinessType";
        public const string AddressLineOne = "AddressLineOne";
        public const string CityTown = "CityTown";
        public const string Postcode = "Postcode";

        public static readonly ValidationRuleConfig CompanyNameRuleConfig = new ValidationRuleConfig
        {
            Description = "A Company Name has not been entered.",
            Name = BusinessName,
            Breadcrumb = ClientManagementBreadcrumbs.InformationTab.ToString(),
            DisplayName = "Business name",
            ErrorCategory = ErrorCategoryType.Mandatory,
            Type = ValidationRuleType.Missing,
            Target = Target.IssueLog,
            ErrorCode = ValidationCodes.BusinessName
        };

        public static readonly ValidationRuleConfig CompanyNumberRuleConfig = new ValidationRuleConfig
        {
            Description = "A valid registered number has not been entered. Companies House requires a valid registered number in the valid format (usually 8 characters).",
            Name = CompanyNumber,
            Breadcrumb = ClientManagementBreadcrumbs.InformationTab.ToString(),
            DisplayName = "Company number",
            ErrorCategory = ErrorCategoryType.Mandatory,
            Type = ValidationRuleType.Missing,
            Target = Target.IssueLog,
            ErrorCode = ValidationCodes.CompanyNumber
        };

        public static readonly ValidationRuleConfig BusinessTypeRuleConfig = new ValidationRuleConfig
        {
            Description = "A Company Type has not been entered.",
            Name = BusinessType,
            Breadcrumb = ClientManagementBreadcrumbs.InformationTab.ToString(),
            DisplayName = "Business Type",
            ErrorCategory = ErrorCategoryType.Mandatory,
            Type = ValidationRuleType.Missing,
            Target = Target.IssueLog,
            ErrorCode = ValidationCodes.BusinessType
        };

        public static readonly ValidationRuleConfig AddressLineOneRuleConfig = new ValidationRuleConfig
        {
            Description = "A valid registered office address has not been entered.",
            Name = AddressLineOne,
            Breadcrumb = ClientManagementBreadcrumbs.InformationTab.ToString(),
            DisplayName = "Address line 1",
            ErrorCategory = ErrorCategoryType.Mandatory,
            Type = ValidationRuleType.Missing,
            Target = Target.IssueLog,
            ErrorCode = ValidationCodes.AddressLine1
        };

        public static readonly ValidationRuleConfig CityRuleConfig = new ValidationRuleConfig
        {
            Description = "A valid city/town has not been entered.",
            Name = CityTown,
            Breadcrumb = ClientManagementBreadcrumbs.InformationTab.ToString(),
            DisplayName = "City/Town",
            ErrorCategory = ErrorCategoryType.Mandatory,
            Type = ValidationRuleType.Missing,
            Target = Target.IssueLog,
            ErrorCode = ValidationCodes.CityTown
        };

        public static readonly ValidationRuleConfig PostcodeRuleConfig = new ValidationRuleConfig
        {
            Description = "A valid postcode has not been entered.",
            Name = Postcode,
            Breadcrumb = ClientManagementBreadcrumbs.InformationTab.ToString(),
            DisplayName = "Postcode",
            ErrorCategory = ErrorCategoryType.Mandatory,
            Type = ValidationRuleType.Missing,
            Target = Target.IssueLog,
            ErrorCode = ValidationCodes.Postcode
        };
    }
}