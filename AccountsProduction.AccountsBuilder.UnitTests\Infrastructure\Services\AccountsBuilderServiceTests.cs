﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.Client;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.EntitySetup;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using AccountsProduction.AccountsBuilder.Infrastructure.Services;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using System.Net;
using System.Text.Json;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Infrastructure.Services
{
    public class AccountsBuilderServiceTests
    {
        private readonly Mock<ILogger<AccountsBuilderService>> _logger;
        private readonly Mock<UserContext> _userContext;
        private readonly Mock<IEnvVariableProvider> _envVariableProvider;
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private EntitySetupDto _entitySetupResponse;

        public AccountsBuilderServiceTests()
        {
            _logger = new Mock<ILogger<AccountsBuilderService>>();
            _envVariableProvider = new Mock<IEnvVariableProvider>();
            _userContext = new Mock<UserContext>();
            _userContext.Setup(x => x.TenantId).Returns(TestHelpers.Guids.GuidOne.ToString());
            _userContext.Setup(x => x.CorrelationId).Returns(Guid.NewGuid().ToString());

            _entitySetupResponse = new EntitySetupDto
            {
                ReportingStandard = "FRS105",
                EntitySize = "Small",
                IndependentReviewType = "Accountants",
                TradingStatus = "Trading"
            };
        }


        [Fact]
        public async Task Should_post_builder_data()
        {
            var clientId = TestHelpers.Guids.GuidOne;
            var periodId = TestHelpers.Guids.GuidTwo;

            var expectedResponse = new ClientResponse()
            {
                Id = clientId,
                Name = "Test Client",
                BusinessType = "Limited",
                LimitedCompanyType = "Private",
                RegisteredNo = "********",
            };

            var responseMessage = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonSerializer.Serialize(expectedResponse, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase }))
            };

            var accountPeriodService = SetupMockData(responseMessage);
            await accountPeriodService.PostAccountsBuilderData(clientId, periodId, new FRS1021AAndFRS102SharedReportingMessage());
        }


        private IAccountsBuilderService SetupMockData(HttpResponseMessage responseMessage)
        {
            _envVariableProvider.Setup(obj => obj.AwsAccessKey).Returns("accesskey");
            _envVariableProvider.Setup(obj => obj.AwsRegion).Returns("eu-west-2");
            _envVariableProvider.Setup(obj => obj.AwsSecretKey).Returns("secretkey");
            _envVariableProvider.Setup(obj => obj.AwsSessionToken).Returns("session");
            _envVariableProvider.Setup(provider => provider.AccountsBuilderApiScheme).Returns("https");
            _envVariableProvider.Setup(provider => provider.AccountsBuilderApiHost).Returns("api.elements-development.iris.co.uk/accountsproduction-accountperiod/v1");
            _envVariableProvider.Setup(provider => provider.AccountsBuilderApiKey).Returns("test");
            _envVariableProvider.Setup(provider => provider.AccountsBuilderApiId).Returns("test");

            var mockFactory = new Mock<IHttpClientFactory>();
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(responseMessage)
                .Verifiable();
            var client = new HttpClient(_mockHttpMessageHandler.Object);
            mockFactory.Setup(_ => _.CreateClient(It.IsAny<string>())).Returns(client);

            var accountsBuilderService = new AccountsBuilderService(_envVariableProvider.Object, client, _userContext.Object, _logger.Object);
            return accountsBuilderService;
        }

    }
}
