﻿using AccountsProduction.AccountsBuilder.Reporting.Application.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Domain;
using AutoMapper;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper.Mappers
{
    public class MemberMapper : Profile
    {
        public MemberMapper()
        {
            CreateMap<InvolvementDto, Member>()
                .ForMember(m => m.InvolvementUUID, m => m.MapFrom(i => i.InvolvementClientGuid))
                .ForMember(m => m.PersonFirstName, m => m.MapFrom(i => i.InvolvementFirstName))
                .ForMember(m => m.PersonSurname, m => m.MapFrom(i => i.InvolvementSurname))
                .ForMember(m => m.PersonTitle, m => m.MapFrom(i => i.InvolvementTitle))
                .ForMember(m => m.MemberType, m => m.MapFrom(i => i.InvolvementType))
                .ForMember(m => m.MemberSubtype, m => m.MapFrom(i => i.InvolvedClientType))
                .ForMember(m => m.EntityName, m => m.MapFrom(i => i.InvolvementClientName))
                .ForMember(m => m.ActiveFrom, m => m.MapFrom(i => i.StartDate))
                .ForMember(m => m.ActiveTo, m => m.MapFrom(i => i.EndDate))
                .ForMember(m => m.DateOfDeath, m => m.MapFrom(i => i.InvolvedDateOfDeath))
                .ForMember(m => m.InvolvementId, m => m.MapFrom(i => (int)i.InvolvementId))
                .ForMember(m => m.PDOCode, m => m.MapFrom(i => i.PdoCode))
                .ForMember(m => m.IsDeleted, m => m.MapFrom(i => i.IsDeleted));
        }
    }
}
