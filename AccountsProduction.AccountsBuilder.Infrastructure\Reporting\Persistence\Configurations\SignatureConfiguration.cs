﻿using AccountsProduction.AccountsBuilder.Reporting.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace AccountsProduction.AccountsBuilder.Reporting.Infrastructure.Persistence.Configurations
{
    public class SignatureConfiguration : IEntityTypeConfiguration<Signature>
    {
        public void Configure(EntityTypeBuilder<Signature> builder)
        {
            builder.ToTable("Signature", "public");

            builder.Property(e => e.Id).ValueGeneratedOnAdd();

            builder.Property(e => e.SignatureType).HasColumnType("character varying");
            builder.Property(e => e.SignatoryTitle).HasColumnType("character varying");
            builder.Property(e => e.SignatoryFirstName).HasColumnType("character varying");
            builder.Property(e => e.SignatorySurname).HasColumnType("character varying");
            builder.Property(e => e.SignatureDate).IsRequired(false).HasColumnType("date");
            builder.Property(e => e.DisplayOrder).HasColumnType("integer");
            builder.Property(e => e.InvolvementUUID).IsRequired(true);
            builder.Property(e => e.InvolvementType).HasColumnType("character varying").IsRequired(true);

            builder.HasOne(d => d.ReportingPeriod)
                .WithMany(p => p!.Signature)
                .HasForeignKey(d => new { d.ClientId, d.AccountPeriodId })
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("signature_fk");
        }
    }
}
