﻿namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.RoundingOptions
{
    public class RoundingOptionsResponse : RoundingOptionsData
    {
        public List<RoundingOptionsData>? PreviousPeriods { get; set; } = null;


        public static IEnumerable<RoundingOptionsData> Flatten(RoundingOptionsResponse roundingOptions)
        {
            var parentRoundingOptions = new List<RoundingOptionsData>() { roundingOptions }
                .Concat(roundingOptions.PreviousPeriods ?? new List<RoundingOptionsData>());
            return parentRoundingOptions;
        }
    }

    public class RoundingOptionsData
    {
        public Guid AccountPeriodId { get; set; }
        public bool UseAdvancedRounding { get; set; }
        public int ProfitLossRoundingAccount { get; set; }
        public int? ProfitLossRoundingSubAccount { get; set; }
        public int BalanceSheetRoundingAccount { get; set; }
        public int? BalanceSheetRoundingSubAccount { get; set; }
        public string ProfitLossRoundingAccountDescription { get; set; } = string.Empty;
        public string BalanceSheetRoundingAccountDescription { get; set; } = string.Empty;
    }
}