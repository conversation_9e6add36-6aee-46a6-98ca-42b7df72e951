﻿using AccountsProduction.AccountsBuilder.Application.Common.Dto.ReportingDomain.ReportTypes;
using AccountsProduction.AccountsBuilder.Application.Common.Helper;
using AccountsProduction.AccountsBuilder.Application.Common.Interfaces;
using Flurl;
using Iris.Platform.WebApi.Infrastructure.Middleware;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AccountsProduction.AccountsBuilder.Infrastructure.Services
{
    public class AccountsBuilderService : IAccountsBuilderService
    {
        private readonly HttpClient _httpClient;
        private readonly UserContext _userContext;
        private readonly ILogger<AccountsBuilderService> _logger;
        private readonly IEnvVariableProvider _variableProvider;

        public AccountsBuilderService(IEnvVariableProvider variableProvider,
            HttpClient httpClient,
            UserContext userContext,
            ILogger<AccountsBuilderService> logger)
        {
            _userContext = userContext;
            _logger = logger;
            _variableProvider = variableProvider;
            _httpClient = httpClient;
            _httpClient.SetupIrisClient(_variableProvider.AccountsBuilderApiKey, _variableProvider.AccountsBuilderApiId, _variableProvider.AccountsBuilderApiScheme, _variableProvider.AccountsBuilderApiHost);

        }

        public async Task PostAccountsBuilderData(Guid clientId, Guid periodId, BaseReportingMessage message)
        {
            var accountBuilderUrl = GetFullUrl(string.Format(Constants.AccountsBuilderApi.ImportDataEndpoint, clientId, periodId));

            var stringContent = new StringContent(JsonConvert.SerializeObject(message), System.Text.Encoding.UTF8, "application/json");
            var request = _httpClient.CreatePostSignedRequest(stringContent, accountBuilderUrl, _variableProvider);
            request.AddRequestHeaders(_userContext);

            _logger.LogInformation("Account Builder Import Data request");
            var response = await _httpClient.SendAsync(request);
            _logger.LogInformation("Account Builder Import Data response received");

            response.EnsureSuccessStatusCode();
        }

        private string GetFullUrl(string path)
        {
            return _httpClient.BaseAddress!.ToString().AppendPathSegment(path).ToString();
        }

    }
}
