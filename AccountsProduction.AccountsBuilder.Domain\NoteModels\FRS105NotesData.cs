﻿namespace AccountsProduction.AccountsBuilder.Domain.NoteModels
{
    public class FRS105NotesData : IFRS105NotesData
    {
        AverageNumberOfEmployees IFRS105NotesData.AverageNumberOfEmployees { get; set; }

        string IFRS105NotesData.OffBalanceSheetArrangements { get; set; }

        string IFRS105NotesData.AdvancesCreditAndGuaranteesGrantedToDirectors { get; set; }

        string IFRS105NotesData.GuaranteesAndOtherFinancialCommitments { get; set; }

        MembersLiabilityText IFRS105NotesData.MembersLiabilityText { get; set; }

        AdditionalNote IFRS105NotesData.AdditionalNote1 { get; set; }

        AdditionalNote IFRS105NotesData.AdditionalNote2 { get; set; }

        string IFRS105NotesData.ControllingPartyNote { get; set; }

        string IFRS105NotesData.RelatedPartyTransactions { get; set; }
    }
}
