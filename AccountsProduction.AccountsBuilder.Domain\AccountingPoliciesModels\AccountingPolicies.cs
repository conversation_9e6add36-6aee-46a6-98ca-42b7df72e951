﻿using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels.TangibleFixedAsset;
using Amazon.DynamoDBv2.DataModel;
using Iris.Elements.DynamoDb.Converters;

namespace AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels
{
    public class AccountingPolicies
    {
        [DynamoDBProperty(typeof(GuidConverter))]
        public Guid ClientId { get; set; }

        [DynamoDBProperty(typeof(GuidConverter))]
        public Guid PeriodId { get; set; }

        [DynamoDBProperty(typeof(GuidConverter))]
        public Guid? PreviousPeriodId { get; set; }

        [DynamoDBProperty(typeof(GuidConverter))]
        public Guid TenantId { get; set; }

        [DynamoDBProperty(typeof(GuidConverter))]
        public Guid CorrelationId { get; set; }

        public bool? IsSuccessful { get; set; }

        public string Error { get; set; }

        [DynamoDBProperty(typeof(DateTimeConverter))]
        public DateTime EntityModificationTime { get; set; }

        public AccountingPoliciesData CurrentPeriodAccountingPolicies { get; set; }

        public AccountingPoliciesData PreviousPeriodAccountingPolicies { get; set; }


        public void UpdateModificationTime()
        {
            EntityModificationTime = DateTime.UtcNow;
        }

        [DynamoDBIgnore]
        public ExemptionsFinancialStatements ExemptionsFinancialStatements => GetExemptions();

        [DynamoDBIgnore]
        public string ChangesInAccountingPolicies => GetChangesInAccountingPolicies();

        [DynamoDBIgnore]
        public string FinancialInstrumentsAccountingPolicy => GetFinancialInstruments();

        [DynamoDBIgnore]
        public string MembersTransactionsWithTheLlpText => GetMembersTransactions();

        [DynamoDBIgnore]
        public string GovernmentGrantsAccountingPolicy => GetGovernmentGrants();

        [DynamoDBIgnore]
        public bool? PresentationCurrency => GetPresentationCurrency();

        [DynamoDBIgnore]
        public bool? ResearchAndDevelopment => GetResearchAndDevelopment();

        [DynamoDBIgnore]
        public bool? ForeignCurrencies => GetForeignCurrencies();

        [DynamoDBIgnore]
        public bool? GoodwillMaterial => GetGoodwillMaterial();

        [DynamoDBIgnore]
        public AssetsAdjustment IntangibleAssetsGoodwill => CurrentPeriodAccountingPolicies?.GetIntangibleAssetsGoodwill() ?? PreviousPeriodAccountingPolicies?.GetIntangibleAssetsGoodwill();

        [DynamoDBIgnore]
        public AssetsAdjustment IntangibleAssetsPatentsAndLicenses => CurrentPeriodAccountingPolicies?.GetIntangibleAssetsPatentsAndLicenses() ?? PreviousPeriodAccountingPolicies?.GetIntangibleAssetsPatentsAndLicenses();

        [DynamoDBIgnore]
        public AssetsAdjustment IntangibleAssetsDevelopmentCosts => CurrentPeriodAccountingPolicies?.GetIntangibleAssetsDevelopmentCosts() ?? PreviousPeriodAccountingPolicies?.GetIntangibleAssetsDevelopmentCosts();

        [DynamoDBIgnore]
        public AssetsAdjustment IntangibleAssetsComputerSoftware => CurrentPeriodAccountingPolicies?.GetIntangibleAssetsComputerSoftware() ?? PreviousPeriodAccountingPolicies?.GetIntangibleAssetsComputerSoftware();

        [DynamoDBIgnore]
        public AssetsAdjustment TangibleAssetsFreeholdProperty => CurrentPeriodAccountingPolicies?.GetTangibleAssetsFreeholdProperty() ?? PreviousPeriodAccountingPolicies?.GetTangibleAssetsFreeholdProperty();

        [DynamoDBIgnore]
        public AssetsAdjustment TangibleAssetsLongLeaseholdProperty => CurrentPeriodAccountingPolicies?.GetTangibleAssetsLongLeaseholdProperty() ?? PreviousPeriodAccountingPolicies?.GetTangibleAssetsLongLeaseholdProperty();

        [DynamoDBIgnore]
        public AssetsAdjustment TangibleAssetsShortLeaseholdProperty => CurrentPeriodAccountingPolicies?.GetTangibleAssetsShortLeaseholdProperty() ?? PreviousPeriodAccountingPolicies?.GetTangibleAssetsShortLeaseholdProperty();

        [DynamoDBIgnore]
        public AssetsAdjustment TangibleAssetsImprovementsToProperty => CurrentPeriodAccountingPolicies?.GetTangibleAssetsImprovementsToProperty() ?? PreviousPeriodAccountingPolicies?.GetTangibleAssetsImprovementsToProperty();

        [DynamoDBIgnore]
        public string PlantAndMachineryClassNameCustomization => CurrentPeriodAccountingPolicies?.GetPlantAndMachineryClassNameCustomization() ?? PreviousPeriodAccountingPolicies?.GetPlantAndMachineryClassNameCustomization();

        [DynamoDBIgnore]
        public string LandAndBuildingsClassNameCustomization => CurrentPeriodAccountingPolicies?.GetLandAndBuildingsClassNameCustomization() ?? PreviousPeriodAccountingPolicies?.GetLandAndBuildingsClassNameCustomization();

        [DynamoDBIgnore]
        public AssetsAdjustment TangibleAssetsPlantAndMachinery => CurrentPeriodAccountingPolicies?.GetTangibleAssetsPlantAndMachinery() ?? PreviousPeriodAccountingPolicies?.GetTangibleAssetsPlantAndMachinery();

        [DynamoDBIgnore]
        public AssetsAdjustment TangibleAssetsFixturesAndFittings => CurrentPeriodAccountingPolicies?.GetTangibleAssetsFixturesAndFittings() ?? PreviousPeriodAccountingPolicies?.GetTangibleAssetsFixturesAndFittings();

        [DynamoDBIgnore]
        public AssetsAdjustment TangibleAssetsMotorVehicles => CurrentPeriodAccountingPolicies?.GetTangibleAssetsMotorVehicles() ?? PreviousPeriodAccountingPolicies?.GetTangibleAssetsMotorVehicles();

        [DynamoDBIgnore]
        public AssetsAdjustment TangibleAssetsComputerEquipment => CurrentPeriodAccountingPolicies?.GetTangibleAssetsComputerEquipment() ?? PreviousPeriodAccountingPolicies?.GetTangibleAssetsComputerEquipment();

        [DynamoDBIgnore]
        public TangibleFixedAssets TangibleFixedAssets => GetTangibleFixedAssets();

        [DynamoDBIgnore]
        public IntangibleAssets IntangibleAssets => GetIntangibleAssets();

        private TangibleFixedAssets GetTangibleFixedAssets()
        {
            return new TangibleFixedAssets
            {
                PlantAndMachinery = new PlantAndMachineries
                {
                    ClassNameCustomization = PlantAndMachineryClassNameCustomization,
                    PlantAndMachinery = TangibleAssetsPlantAndMachinery,
                    ComputerEquipment = TangibleAssetsComputerEquipment,
                    FixturesAndFittings = TangibleAssetsFixturesAndFittings,
                    ImprovementsToProperty = TangibleAssetsImprovementsToProperty,
                    MotorVehicles = TangibleAssetsMotorVehicles
                },
                LandAndBuildings = new LandAndBuildings
                {
                    ClassNameCustomization = LandAndBuildingsClassNameCustomization,
                    FreeholdProperty = TangibleAssetsFreeholdProperty,
                    ShortLeaseholdProperty = TangibleAssetsShortLeaseholdProperty,
                    LongLeaseholdProperty = TangibleAssetsLongLeaseholdProperty
                }
            };
        }

        private IntangibleAssets GetIntangibleAssets()
        {
            return new IntangibleAssets
            {
                Goodwill = IntangibleAssetsGoodwill,
                DevelopmentCosts = IntangibleAssetsDevelopmentCosts,
                PatentsAndLicenses = IntangibleAssetsPatentsAndLicenses,
                ComputerSoftware = IntangibleAssetsComputerSoftware
            };
        }

        private bool GetGoodwillMaterial()
        {
            return CurrentPeriodAccountingPolicies?.GoodwillMaterial ?? (PreviousPeriodAccountingPolicies?.GoodwillMaterial ?? false);
        }

        private ExemptionsFinancialStatements GetExemptions()
        {
            return CurrentPeriodAccountingPolicies != null ? CurrentPeriodAccountingPolicies.ExemptionsFinancialStatements : PreviousPeriodAccountingPolicies?.ExemptionsFinancialStatements;
        }

        private bool? GetPresentationCurrency()
        {
            return CurrentPeriodAccountingPolicies != null ? CurrentPeriodAccountingPolicies.PresentationCurrency : PreviousPeriodAccountingPolicies?.PresentationCurrency;
        }

        private bool? GetResearchAndDevelopment()
        {
            return CurrentPeriodAccountingPolicies != null ? CurrentPeriodAccountingPolicies.ResearchAndDevelopment : PreviousPeriodAccountingPolicies?.ResearchAndDevelopment;
        }

        private bool? GetForeignCurrencies()
        {
            return CurrentPeriodAccountingPolicies != null ? CurrentPeriodAccountingPolicies.ForeignCurrencies : PreviousPeriodAccountingPolicies?.ForeignCurrencies;
        }

        private string GetGovernmentGrants()
        {
            return CurrentPeriodAccountingPolicies != null
                ? CurrentPeriodAccountingPolicies.GovernmentGrantsAccountingPolicy
                : PreviousPeriodAccountingPolicies?.GovernmentGrantsAccountingPolicy;
        }

        private string GetMembersTransactions()
        {
            return CurrentPeriodAccountingPolicies != null
                ? CurrentPeriodAccountingPolicies.MembersTransactionsWithTheLlpText
                : PreviousPeriodAccountingPolicies?.MembersTransactionsWithTheLlpText;
        }

        private string GetChangesInAccountingPolicies()
        {
            return CurrentPeriodAccountingPolicies?.ChangesInAccountingPolicies;
        }

        private string GetFinancialInstruments()
        {
            return CurrentPeriodAccountingPolicies != null
                ? CurrentPeriodAccountingPolicies.FinancialInstrumentsAccountingPolicy
                : PreviousPeriodAccountingPolicies?.FinancialInstrumentsAccountingPolicy;
        }
    }
}
