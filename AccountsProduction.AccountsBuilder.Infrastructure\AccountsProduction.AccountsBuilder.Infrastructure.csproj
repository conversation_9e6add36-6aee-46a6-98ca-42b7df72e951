﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<SonarQubeTestProject>false</SonarQubeTestProject>
		<PublishReadyToRun>true</PublishReadyToRun>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Helper\**" />
		<Compile Remove="Services\FeatureService\**" />
		<Compile Remove="Utils\**" />
		<EmbeddedResource Remove="Helper\**" />
		<EmbeddedResource Remove="Services\FeatureService\**" />
		<EmbeddedResource Remove="Utils\**" />
		<None Remove="Helper\**" />
		<None Remove="Services\FeatureService\**" />
		<None Remove="Utils\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Amazon.Lambda.APIGatewayEvents" Version="2.7.1" />
		<PackageReference Include="AWSSDK.RDS" Version="3.7.410.12" />
		<PackageReference Include="Dapper" Version="2.1.66" />
		<PackageReference Include="Dapper.FluentMap" Version="2.0.0" />
		<PackageReference Include="Iris.AccountsProduction.Common.Toolkit" Version="2.0.0.259" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.1" />
		<PackageReference Include="System.Text.Encodings.Web" Version="9.0.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\AccountsProduction.AccountsBuilder.Application\AccountsProduction.AccountsBuilder.Application.csproj" />
		<ProjectReference Include="..\AccountsProduction.AccountsBuilder.Domain\AccountsProduction.AccountsBuilder.Domain.csproj" />
	</ItemGroup>

</Project>
