﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AutoMapper;
using AccountsProduction.AccountsBuilder.Domain.DataScreens;
using Xunit;
using Iris.AccountsProduction.AccountsBuilder.Messages.ScreenValues;
using Shouldly;
using ScreenValues = Iris.AccountsProduction.AccountsBuilder.Messages.ScreenValues.ScreenValues;
using ScreenField = Iris.AccountsProduction.AccountsBuilder.Messages.ScreenValues.ScreenField;


namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Common
{
    public  class DataScreensValueMapperTests
    {
        private readonly IMapper _mapper;
        public DataScreensValueMapperTests()
        {
            var mapperConfig = new MapperConfiguration(cfg =>
            {
               cfg.AddProfile<DataScreensValueMapper>();
            });

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();
        }

        [Fact]
        public void Should_convert_screen_values_response_message_to_data_screen_value()
        {
            var screenValuesResponseMessage = new ScreenValuesResponseMessage
            {
                TenantId = Guid.NewGuid(),
                ClientId = Guid.NewGuid(),
                PeriodId = Guid.NewGuid(),
                PreviousPeriodId = Guid.NewGuid(),
                CorrelationId = Guid.NewGuid(),
                CurrentPeriod = new List<ScreenValues>
                {
                    new ScreenValues
                    {
                        ScreenId = Guid.NewGuid().ToString(),
                        ScreenFields = new List<ScreenField>
                        {
                            new ScreenField
                            {
                                Value = "test"
                            }
                        }
                    }
                },
                PreviousPeriod = new List<ScreenValues>
                {
                    new ScreenValues
                    {
                        ScreenId = Guid.NewGuid().ToString(),
                        ScreenFields = new List<ScreenField>
                        {
                            new ScreenField
                            {
                                Value = "test"
                            }
                        }
                    }
                },
                Error = "test",
                IsSuccessful = true
            };

            var result = _mapper.Map<ScreenValuesResponseMessage,DataScreenValue>(screenValuesResponseMessage);
            result.ShouldBeOfType(typeof(DataScreenValue));
            result.TenantId.ShouldBe(screenValuesResponseMessage.TenantId);
            result.ClientId.ShouldBe(screenValuesResponseMessage.ClientId);
            result.PeriodId.ShouldBe(screenValuesResponseMessage.PeriodId);
            result.PreviousPeriodId.ShouldBe(screenValuesResponseMessage.PreviousPeriodId);
            result.CorrelationId.ShouldBe(screenValuesResponseMessage.CorrelationId);
            result.CurrentPeriod.Count.ShouldBe(screenValuesResponseMessage.CurrentPeriod.Count);
            result.PreviousPeriod.Count.ShouldBe(screenValuesResponseMessage.PreviousPeriod.Count);
            result.Error.ShouldBe(screenValuesResponseMessage.Error);
            result.IsSuccessful.ShouldBe(screenValuesResponseMessage.IsSuccessful);
        }

        [Fact]
        public void Should_convert_domain_screen_values_to_message_screen_values()
        {
            var domainScreenValues = new AccountsBuilder.Domain.DataScreens.PeriodScreenValue
            {
                ScreenId = Guid.NewGuid().ToString(),
                ScreenFields = new List<AccountsBuilder.Domain.DataScreens.ScreenField>
                {
                    new AccountsBuilder.Domain.DataScreens.ScreenField
                    {
                        Value = "test"
                    }
                }
            };

            var result = _mapper.Map<AccountsBuilder.Domain.DataScreens.PeriodScreenValue, ScreenValues>(domainScreenValues);
            result.ShouldBeOfType(typeof(ScreenValues));
            result.ScreenId.ShouldBe(domainScreenValues.ScreenId);
            result.ScreenFields.Count.ShouldBe(domainScreenValues.ScreenFields.Count);
            result.ScreenFields.First().Value.ShouldBe(domainScreenValues.ScreenFields.First().Value);
        }
    }
}
