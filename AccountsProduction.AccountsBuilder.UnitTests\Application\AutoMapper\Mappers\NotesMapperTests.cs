﻿using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Domain.NoteModels;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.Notes;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.AutoMapper.Mappers
{
    public class NotesMapperTests
    {
        private readonly IMapper _mapper;

        public NotesMapperTests()
        {
            var mapperConfig = new MapperConfiguration(cfg => { cfg.AddProfile<NotesMapper>(); });

            mapperConfig.AssertConfigurationIsValid();
            _mapper = mapperConfig.CreateMapper();
        }

        [Theory]
        [InlineData(ReportStandardType.FRS105)]
        [InlineData(ReportStandardType.FRS102_1A)]
        [InlineData(ReportStandardType.UNINCORPORATED)]
        [InlineData(default)]
        public void Should_map_correct_notes_when_current_period_notes_exists(string reportType)
        {
            var note = new NotesResponseMessage
            {
                PreviousPeriodId = TestHelpers.Guids.GuidOne,
                PeriodId = TestHelpers.Guids.GuidTwo,
                ClientId = TestHelpers.Guids.GuidThree,
                TenantId = TestHelpers.Guids.GuidFour,
                CorrelationId = TestHelpers.Guids.GuidFour,
                CurrentPeriodNotes = new NotesResponseDataMessage
                {
                    AverageNumberOfEmployees = new AverageNumberOfEmployeesMessage
                    {
                        CurrentPeriod = 1,
                        PreviousPeriod = 2
                    },
                    OffBalanceSheetArrangements = "text1",
                    AdvancesCreditAndGuaranteesGrantedToDirectors = "text2",
                    GuaranteesAndOtherFinancialCommitments = "text3",
                    RelatedPartyTransactions = "text4",
                    LoansAndOtherDebtsDueToMembers = "text44",
                    MembersLiabilityText = new MembersLiabilityTextMessage { NoteTitle = "Members Liability Note Title", NoteText = "Members Liability Note Text" },
                    AdditionalNote1 = new AdditionalNoteMessage { NoteTitle = "text5", NoteText = "text6" },
                    AdditionalNote2 = new AdditionalNoteMessage { NoteTitle = "text7", NoteText = "text8" },
                    ControllingPartyNote = "text9",
                    IntangibleAssetsRevaluation = "text10",
                    OperatingProfitLoss = new OperatingProfitLossMessage
                    {
                        IsEnabled = true,
                        Items = new List<OperatingProfitLossItemMessage> {
                            new OperatingProfitLossItemMessage { Index = 1, Description = "Test1", Value = 201 },
                            new OperatingProfitLossItemMessage { Index = 2, Description = "Test2", Value = 202.34M },
                            new OperatingProfitLossItemMessage { Index = 3, Description = "Test3", Value = -203.67M },
                        }
                    },
                    AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = new AdvancesCreditAndGuaranteesGrantedToDirectorsMessage
                    {
                        Guarantees = "Guarantees",
                        Items = new List<AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage>
                        {
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage
                            {
                                Index = 1,
                                InvolvementClientGuid = TestHelpers.Guids.GuidFive,
                                DirectorName = "Director1",
                                BalanceOutstandingAtStartOfYear = 1000M,
                                AmountsAdvanced = 100M,
                                AmountsRepaid = 0M,
                                AmountsWrittenOff = 0M,
                                AmountsWaived = 0M,
                                AdvanceCreditConditions = "Director1 AdvanceCreditConditions",
                                BalanceOutstandingAtEndOfYear = 1100M
                            },
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage
                            {
                                Index = 2,
                                InvolvementClientGuid = TestHelpers.Guids.GuidSix,
                                DirectorName = "Director2",
                                BalanceOutstandingAtStartOfYear = 2000M,
                                AmountsAdvanced = 200M,
                                AmountsRepaid = 0M,
                                AmountsWrittenOff = 0M,
                                AmountsWaived = 0M,
                                AdvanceCreditConditions = "Director2 AdvanceCreditConditions",
                                BalanceOutstandingAtEndOfYear = 2200M
                            },
                        }
                    },
                    TangibleFixedAssetsNotes = new TangibleFixedAssetsNotesMessage
                    {
                        ValuationInCurrentReportingPeriod = new ValuationInCurrentReportingPeriodMessage
                        {
                            ValuationDetails = "Valuation Details long message",
                            IndependentValuerInvolved = true,
                            RevaluationBasis = "Revaluation Basis  short",
                            DateOfRevaluation = new DateTime(2019, 05, 09, 09, 15, 00)
                        },
                        HistoricalCostBreakdown = new HistoricalCostBreakdownMessage
                        {
                            RevaluedAssetClass = "Revalued Asset Class",
                            RevaluedClassPronoun = "Revalued Class Pronoun",
                            CurrentReportingPeriodAccumulatedDepreciation = 1.33m,
                            CurrentReportingPeriodCost = 1.45m,
                        },
                        AnalysisOfCostOrValuation = new AnalysisOfCostOrValuationMessage
                        {
                            AnalysisOfCostOrValuationItems = new System.Collections.Generic.List<AnalysisOfCostOrValuationItemMessage>
                            {
                                new AnalysisOfCostOrValuationItemMessage
                                {
                                    Index = 3,
                                    Year = 2020,
                                    LandAndBuildings = 1.1m,
                                    PlantAndMachineryEtc = 2.2m
                                },
                                new AnalysisOfCostOrValuationItemMessage
                                {
                                    Index = 4,
                                    Year = 2021,
                                    LandAndBuildings = 3.3m,
                                    PlantAndMachineryEtc = 4.4m
                                },
                                new AnalysisOfCostOrValuationItemMessage
                                {
                                    Index = 5,
                                    Year = 2022,
                                    LandAndBuildings = -3.3m,
                                    PlantAndMachineryEtc = -4.4m
                                }
                            },
                            CostLandAndBuildings = 5.5m,
                            CostPlantAndMachineryEtc = 6.6m,
                            TotalLandAndBuildings = 11,
                            TotalPlantAndMachineryEtc = 12
                        }
                    }
                },
                IsSuccessful = false,
                Error = "error"

            };

            var mappedNote = _mapper.Map<Notes>(note, opt => opt.Items["ReportType"] = reportType);

            mappedNote.ClientId.ShouldBe(TestHelpers.Guids.GuidThree);
            mappedNote.PeriodId.ShouldBe(TestHelpers.Guids.GuidTwo);
            mappedNote.TenantId.ShouldBe(TestHelpers.Guids.GuidFour);
            mappedNote.PreviousPeriodId.ShouldBe(TestHelpers.Guids.GuidOne);
            mappedNote.CorrelationId.ShouldBe(TestHelpers.Guids.GuidFour);
            mappedNote.IsSuccessful.ShouldBe(false);
            mappedNote.Error.ShouldNotBeNull("error");
            switch (reportType)
            {
                case ReportStandardType.FRS105:
                    mappedNote.AverageNumberOfEmployees.ShouldNotBeNull();
                    mappedNote.AverageNumberOfEmployees.PreviousPeriod.ShouldBe(2);
                    mappedNote.AverageNumberOfEmployees.CurrentPeriod.ShouldBe(1);
                    mappedNote.OffBalanceSheetArrangements.ShouldBe("text1");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectors.ShouldBe("text2");
                    mappedNote.GuaranteesAndOtherFinancialCommitments.ShouldBe("text3");
                    mappedNote.RelatedPartyTransactions.ShouldBe("text4");

                    mappedNote.MembersLiabilityText.NoteText.ShouldBe("Members Liability Note Text");
                    mappedNote.MembersLiabilityText.NoteTitle.ShouldBe("Members Liability Note Title");

                    mappedNote.AdditionalNote1.NoteTitle.ShouldBe("text5");
                    mappedNote.AdditionalNote1.NoteText.ShouldBe("text6");
                    mappedNote.AdditionalNote2.NoteTitle.ShouldBe("text7");
                    mappedNote.AdditionalNote2.NoteText.ShouldBe("text8");
                    mappedNote.ControllingPartyNote.ShouldBe("text9");
                    mappedNote.IntangibleAssetsRevaluation.ShouldBeNull();
                    mappedNote.OperatingProfitLoss.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.ShouldBeNull();
                    break;
                case ReportStandardType.FRS102_1A:
                case ReportStandardType.FRS102:
                    mappedNote.AverageNumberOfEmployees.ShouldNotBeNull();
                    mappedNote.AverageNumberOfEmployees.PreviousPeriod.ShouldBe(2);
                    mappedNote.AverageNumberOfEmployees.CurrentPeriod.ShouldBe(1);
                    mappedNote.OffBalanceSheetArrangements.ShouldBe("text1");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectors.ShouldBeNull();
                    mappedNote.GuaranteesAndOtherFinancialCommitments.ShouldBe("text3");
                    mappedNote.RelatedPartyTransactions.ShouldBe("text4");
                    mappedNote.LoansAndOtherDebtsDueToMembers.ShouldBe("text44");

                    mappedNote.MembersLiabilityText.NoteText.ShouldBe("Members Liability Note Text");
                    mappedNote.MembersLiabilityText.NoteTitle.ShouldBe("Members Liability Note Title");

                    mappedNote.AdditionalNote1.NoteTitle.ShouldBe("text5");
                    mappedNote.AdditionalNote1.NoteText.ShouldBe("text6");
                    mappedNote.AdditionalNote2.NoteTitle.ShouldBe("text7");
                    mappedNote.AdditionalNote2.NoteText.ShouldBe("text8");
                    mappedNote.ControllingPartyNote.ShouldBe("text9");
                    mappedNote.IntangibleAssetsRevaluation.ShouldBe("text10");
                    mappedNote.OperatingProfitLoss.IsEnabled.ShouldBe(true);
                    mappedNote.OperatingProfitLoss.Items[0].Index.ShouldBe(1);
                    mappedNote.OperatingProfitLoss.Items[0].Description.ShouldBe("Test1");
                    mappedNote.OperatingProfitLoss.Items[0].Value.ShouldBe(201);
                    mappedNote.OperatingProfitLoss.Items[1].Index.ShouldBe(2);
                    mappedNote.OperatingProfitLoss.Items[1].Description.ShouldBe("Test2");
                    mappedNote.OperatingProfitLoss.Items[1].Value.ShouldBe(202.34M);
                    mappedNote.OperatingProfitLoss.Items[2].Index.ShouldBe(3);
                    mappedNote.OperatingProfitLoss.Items[2].Description.ShouldBe("Test3");
                    mappedNote.OperatingProfitLoss.Items[2].Value.ShouldBe(-203.67M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].Index.ShouldBe(1);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidFive);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].DirectorName.ShouldBe("Director1");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtStartOfYear.ShouldBe(1000M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsAdvanced.ShouldBe(100M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsRepaid.ShouldBe(0M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWaived.ShouldBe(0M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWrittenOff.ShouldBe(0M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtEndOfYear.ShouldBe(1100M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AdvanceCreditConditions.ShouldBe("Director1 AdvanceCreditConditions");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].Index.ShouldBe(2);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidSix);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].DirectorName.ShouldBe("Director2");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].BalanceOutstandingAtStartOfYear.ShouldBe(2000M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsAdvanced.ShouldBe(200M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsRepaid.ShouldBe(0M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsWaived.ShouldBe(0M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsWrittenOff.ShouldBe(0M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].BalanceOutstandingAtEndOfYear.ShouldBe(2200M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AdvanceCreditConditions.ShouldBe("Director2 AdvanceCreditConditions");
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.ValuationDetails.ShouldBe("Valuation Details long message");
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.IndependentValuerInvolved.ShouldBeTrue();
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.RevaluationBasis.ShouldBe("Revaluation Basis  short");
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.DateOfRevaluation.ShouldBe(new DateTime(2019, 05, 09, 09, 15, 00));
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.RevaluedAssetClass.ShouldBe("Revalued Asset Class");
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.RevaluedClassPronoun.ShouldBe("Revalued Class Pronoun");
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.CurrentReportingPeriodAccumulatedDepreciation.ShouldBe(1.33m);
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.CurrentReportingPeriodCost.ShouldBe(1.45m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems.Count.ShouldBe(3);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].LandAndBuildings.ShouldBe(1.1m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].PlantAndMachineryEtc.ShouldBe(2.2m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].Year.ShouldBe(2020);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].Index.ShouldBe(3);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].LandAndBuildings.ShouldBe(3.3m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].PlantAndMachineryEtc.ShouldBe(4.4m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].Year.ShouldBe(2021);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].Index.ShouldBe(4);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].LandAndBuildings.ShouldBe(-3.3m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].PlantAndMachineryEtc.ShouldBe(-4.4m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].Year.ShouldBe(2022);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].Index.ShouldBe(5);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.CostLandAndBuildings.ShouldBe(5.5m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.CostPlantAndMachineryEtc.ShouldBe(6.6m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.TotalLandAndBuildings.ShouldBe(11);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.TotalPlantAndMachineryEtc.ShouldBe(12);
                    break;
                case ReportStandardType.UNINCORPORATED:
                    mappedNote.AverageNumberOfEmployees.ShouldNotBeNull();
                    mappedNote.AverageNumberOfEmployees.PreviousPeriod.ShouldBeNull();
                    mappedNote.AverageNumberOfEmployees.CurrentPeriod.ShouldBeNull();
                    mappedNote.OffBalanceSheetArrangements.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectors.ShouldBeNull();
                    mappedNote.GuaranteesAndOtherFinancialCommitments.ShouldBeNull();
                    mappedNote.RelatedPartyTransactions.ShouldBeNull();

                    mappedNote.MembersLiabilityText.NoteText.ShouldBe("Members Liability Note Text");
                    mappedNote.MembersLiabilityText.NoteTitle.ShouldBe("Members Liability Note Title");

                    mappedNote.AdditionalNote1.NoteTitle.ShouldBe("text5");
                    mappedNote.AdditionalNote1.NoteText.ShouldBe("text6");
                    mappedNote.AdditionalNote2.NoteTitle.ShouldBe("text7");
                    mappedNote.AdditionalNote2.NoteText.ShouldBe("text8");
                    mappedNote.ControllingPartyNote.ShouldBeNull();
                    mappedNote.IntangibleAssetsRevaluation.ShouldBeNull();
                    mappedNote.OperatingProfitLoss.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.ShouldBeNull();
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.ValuationDetails.ShouldBe("Valuation Details long message");
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.IndependentValuerInvolved.ShouldBeTrue();
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.RevaluationBasis.ShouldBe("Revaluation Basis  short");
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.DateOfRevaluation.ShouldBe(new DateTime(2019, 05, 09, 09, 15, 00));
                    break;
                default:
                    mappedNote.AverageNumberOfEmployees.ShouldNotBeNull();
                    mappedNote.AverageNumberOfEmployees.PreviousPeriod.ShouldBe(2);
                    mappedNote.AverageNumberOfEmployees.CurrentPeriod.ShouldBe(1);
                    mappedNote.OffBalanceSheetArrangements.ShouldBe("text1");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectors.ShouldBe("text2");
                    mappedNote.GuaranteesAndOtherFinancialCommitments.ShouldBe("text3");
                    mappedNote.RelatedPartyTransactions.ShouldBe("text4");
                    mappedNote.LoansAndOtherDebtsDueToMembers.ShouldBe("text44");

                    mappedNote.MembersLiabilityText.NoteText.ShouldBe("Members Liability Note Text");
                    mappedNote.MembersLiabilityText.NoteTitle.ShouldBe("Members Liability Note Title");

                    mappedNote.AdditionalNote1.NoteTitle.ShouldBe("text5");
                    mappedNote.AdditionalNote1.NoteText.ShouldBe("text6");
                    mappedNote.AdditionalNote2.NoteTitle.ShouldBe("text7");
                    mappedNote.AdditionalNote2.NoteText.ShouldBe("text8");
                    mappedNote.ControllingPartyNote.ShouldBe("text9");
                    mappedNote.IntangibleAssetsRevaluation.ShouldBe("text10");
                    mappedNote.OperatingProfitLoss.IsEnabled.ShouldBe(true);
                    mappedNote.OperatingProfitLoss.Items[0].Index.ShouldBe(1);
                    mappedNote.OperatingProfitLoss.Items[0].Description.ShouldBe("Test1");
                    mappedNote.OperatingProfitLoss.Items[0].Value.ShouldBe(201);
                    mappedNote.OperatingProfitLoss.Items[1].Index.ShouldBe(2);
                    mappedNote.OperatingProfitLoss.Items[1].Description.ShouldBe("Test2");
                    mappedNote.OperatingProfitLoss.Items[1].Value.ShouldBe(202.34M);
                    mappedNote.OperatingProfitLoss.Items[2].Index.ShouldBe(3);
                    mappedNote.OperatingProfitLoss.Items[2].Description.ShouldBe("Test3");
                    mappedNote.OperatingProfitLoss.Items[2].Value.ShouldBe(-203.67M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].Index.ShouldBe(1);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidFive);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].DirectorName.ShouldBe("Director1");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtStartOfYear.ShouldBe(1000M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsAdvanced.ShouldBe(100M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsRepaid.ShouldBe(0M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWaived.ShouldBe(0M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWrittenOff.ShouldBe(0M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtEndOfYear.ShouldBe(1100M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AdvanceCreditConditions.ShouldBe("Director1 AdvanceCreditConditions");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].Index.ShouldBe(2);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidSix);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].DirectorName.ShouldBe("Director2");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].BalanceOutstandingAtStartOfYear.ShouldBe(2000M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsAdvanced.ShouldBe(200M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsRepaid.ShouldBe(0M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsWaived.ShouldBe(0M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsWrittenOff.ShouldBe(0M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].BalanceOutstandingAtEndOfYear.ShouldBe(2200M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AdvanceCreditConditions.ShouldBe("Director2 AdvanceCreditConditions");
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.ValuationDetails.ShouldBe("Valuation Details long message");
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.IndependentValuerInvolved.ShouldBeTrue();
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.RevaluationBasis.ShouldBe("Revaluation Basis  short");
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.DateOfRevaluation.ShouldBe(new DateTime(2019, 05, 09, 09, 15, 00));
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.RevaluedAssetClass.ShouldBe("Revalued Asset Class");
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.RevaluedClassPronoun.ShouldBe("Revalued Class Pronoun");
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.CurrentReportingPeriodAccumulatedDepreciation.ShouldBe(1.33m);
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.CurrentReportingPeriodCost.ShouldBe(1.45m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems.Count.ShouldBe(3);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].LandAndBuildings.ShouldBe(1.1m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].PlantAndMachineryEtc.ShouldBe(2.2m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].Year.ShouldBe(2020);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].Index.ShouldBe(3);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].LandAndBuildings.ShouldBe(3.3m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].PlantAndMachineryEtc.ShouldBe(4.4m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].Year.ShouldBe(2021);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].Index.ShouldBe(4);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].LandAndBuildings.ShouldBe(-3.3m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].PlantAndMachineryEtc.ShouldBe(-4.4m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].Year.ShouldBe(2022);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[2].Index.ShouldBe(5);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.CostLandAndBuildings.ShouldBe(5.5m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.CostPlantAndMachineryEtc.ShouldBe(6.6m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.TotalLandAndBuildings.ShouldBe(11);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.TotalPlantAndMachineryEtc.ShouldBe(12);
                    break;
            }
        }

        [Theory]
        [InlineData(ReportStandardType.FRS105)]
        [InlineData(ReportStandardType.FRS102_1A)]
        [InlineData(ReportStandardType.FRS102)]
        [InlineData(ReportStandardType.UNINCORPORATED)]
        [InlineData(default)]
        public void Should_map_correct_notes_when_previous_period_notes_exists(string reportType)
        {
            var note = new NotesResponseMessage
            {
                PreviousPeriodId = TestHelpers.Guids.GuidOne,
                PeriodId = TestHelpers.Guids.GuidTwo,
                ClientId = TestHelpers.Guids.GuidThree,
                TenantId = TestHelpers.Guids.GuidFour,
                CorrelationId = TestHelpers.Guids.GuidFour,
                PreviousPeriodNotes = new NotesResponseDataMessage
                {
                    AverageNumberOfEmployees = new AverageNumberOfEmployeesMessage
                    {
                        CurrentPeriod = 1,
                        PreviousPeriod = 2
                    },
                    OffBalanceSheetArrangements = "text1",
                    AdvancesCreditAndGuaranteesGrantedToDirectors = "text2",
                    GuaranteesAndOtherFinancialCommitments = "text3",
                    RelatedPartyTransactions = "text4",
                    LoansAndOtherDebtsDueToMembers = "text44",
                    MembersLiabilityText = new MembersLiabilityTextMessage { NoteTitle = "Members Liability Note Title", NoteText = "Members Liability Note Text" },
                    AdditionalNote1 = new AdditionalNoteMessage { NoteTitle = "text5", NoteText = "text6" },
                    AdditionalNote2 = new AdditionalNoteMessage { NoteTitle = "text7", NoteText = "text8" },
                    ControllingPartyNote = "text9",
                    IntangibleAssetsRevaluation = "text10",
                    OperatingProfitLoss = new OperatingProfitLossMessage
                    {
                        IsEnabled = true,
                        Items = new List<OperatingProfitLossItemMessage> {
                            new OperatingProfitLossItemMessage { Index = 1, Description = "Test1", Value = -201.56M },
                    }
                    },
                    AdvancesCreditAndGuaranteesGrantedToDirectorsExtended = new AdvancesCreditAndGuaranteesGrantedToDirectorsMessage
                    {
                        Guarantees = "Guarantees",
                        Items = new List<AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage>
                        {
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage
                            {
                                Index = 1,
                                InvolvementClientGuid = TestHelpers.Guids.GuidFive,
                                DirectorName = "Director1",
                                BalanceOutstandingAtStartOfYear = 1000M,
                                AmountsAdvanced = 100M,
                                AmountsRepaid = 0M,
                                AmountsWrittenOff = 0M,
                                AmountsWaived = 0M,
                                AdvanceCreditConditions = "Director1 AdvanceCreditConditions",
                                BalanceOutstandingAtEndOfYear = 1100M
                            },
                            new AdvancesCreditAndGuaranteesGrantedToDirectorItemMessage
                            {
                                Index = 2,
                                InvolvementClientGuid = TestHelpers.Guids.GuidSix,
                                DirectorName = "Director2",
                                BalanceOutstandingAtStartOfYear = 2000M,
                                AmountsAdvanced = 200M,
                                AmountsRepaid = 0M,
                                AmountsWrittenOff = 0M,
                                AmountsWaived = 0M,
                                AdvanceCreditConditions = "Director2 AdvanceCreditConditions",
                                BalanceOutstandingAtEndOfYear = 2200M
                            },
                        }
                    },
                    TangibleFixedAssetsNotes = new TangibleFixedAssetsNotesMessage
                    {
                        ValuationInCurrentReportingPeriod = new ValuationInCurrentReportingPeriodMessage
                        {
                            ValuationDetails = "Valuation Details long message",
                            IndependentValuerInvolved = true,
                            RevaluationBasis = "Revaluation Basis  short",
                            DateOfRevaluation = new DateTime(2019, 05, 09, 09, 15, 00)
                        },
                        HistoricalCostBreakdown = new HistoricalCostBreakdownMessage
                        {
                            RevaluedAssetClass = "Revalued Asset Class",
                            RevaluedClassPronoun = "Revalued Class Pronoun",
                            CurrentReportingPeriodAccumulatedDepreciation = 1.33m,
                            CurrentReportingPeriodCost = 1.45m,
                        },
                        AnalysisOfCostOrValuation = new AnalysisOfCostOrValuationMessage
                        {
                            AnalysisOfCostOrValuationItems = new List<AnalysisOfCostOrValuationItemMessage>
                            {
                                new AnalysisOfCostOrValuationItemMessage
                                {
                                    Index = 3,
                                    Year = 2019,
                                    LandAndBuildings = 1.1m,
                                    PlantAndMachineryEtc = 2.2m
                                },
                                new AnalysisOfCostOrValuationItemMessage
                                {
                                    Index = 4,
                                    Year = 2020,
                                    LandAndBuildings = 3.3m,
                                    PlantAndMachineryEtc = 4.4m
                                }
                            },
                            CostLandAndBuildings = 5.5m,
                            CostPlantAndMachineryEtc = 6.6m,
                            TotalLandAndBuildings = 11,
                            TotalPlantAndMachineryEtc = 12
                        }
                    },
                },
                IsSuccessful = false,
                Error = "error"
            };

            var mappedNote = _mapper.Map<Notes>(note, opt => opt.Items["ReportType"] = reportType);
            mappedNote.ClientId.ShouldBe(TestHelpers.Guids.GuidThree);
            mappedNote.PeriodId.ShouldBe(TestHelpers.Guids.GuidTwo);
            mappedNote.TenantId.ShouldBe(TestHelpers.Guids.GuidFour);
            mappedNote.PreviousPeriodId.ShouldBe(TestHelpers.Guids.GuidOne);
            mappedNote.CorrelationId.ShouldBe(TestHelpers.Guids.GuidFour);
            mappedNote.IsSuccessful.ShouldBe(false);
            mappedNote.Error.ShouldNotBeNull("error");

            switch (reportType)
            {
                case ReportStandardType.FRS105:
                    mappedNote.AverageNumberOfEmployees.ShouldNotBeNull();
                    mappedNote.AverageNumberOfEmployees.PreviousPeriod.ShouldBe(1);
                    mappedNote.AverageNumberOfEmployees.CurrentPeriod.ShouldBeNull();
                    mappedNote.OffBalanceSheetArrangements.ShouldBe("text1");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectors.ShouldBeNull();
                    mappedNote.GuaranteesAndOtherFinancialCommitments.ShouldBe("text3");
                    mappedNote.RelatedPartyTransactions.ShouldBe("text4");

                    mappedNote.MembersLiabilityText.NoteText.ShouldBe("Members Liability Note Text");
                    mappedNote.MembersLiabilityText.NoteTitle.ShouldBe("Members Liability Note Title");

                    mappedNote.AdditionalNote1.NoteTitle.ShouldBe("text5");
                    mappedNote.AdditionalNote1.NoteText.ShouldBe("text6");
                    mappedNote.AdditionalNote2.NoteTitle.ShouldBe("text7");
                    mappedNote.AdditionalNote2.NoteText.ShouldBe("text8");
                    mappedNote.ControllingPartyNote.ShouldBe("text9");
                    mappedNote.IntangibleAssetsRevaluation.ShouldBeNull();
                    mappedNote.OperatingProfitLoss.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.ShouldBeNull();
                    break;
                case ReportStandardType.FRS102_1A:
                case ReportStandardType.FRS102:
                    mappedNote.AverageNumberOfEmployees.ShouldNotBeNull();
                    mappedNote.AverageNumberOfEmployees.PreviousPeriod.ShouldBe(1);
                    mappedNote.AverageNumberOfEmployees.CurrentPeriod.ShouldBeNull();
                    mappedNote.OffBalanceSheetArrangements.ShouldBe("text1");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectors.ShouldBeNull();
                    mappedNote.GuaranteesAndOtherFinancialCommitments.ShouldBe("text3");
                    mappedNote.RelatedPartyTransactions.ShouldBe("text4");
                    mappedNote.LoansAndOtherDebtsDueToMembers.ShouldBe("text44");

                    mappedNote.MembersLiabilityText.NoteText.ShouldBe("Members Liability Note Text");
                    mappedNote.MembersLiabilityText.NoteTitle.ShouldBe("Members Liability Note Title");

                    mappedNote.AdditionalNote1.NoteTitle.ShouldBe("text5");
                    mappedNote.AdditionalNote1.NoteText.ShouldBe("text6");
                    mappedNote.AdditionalNote2.NoteTitle.ShouldBe("text7");
                    mappedNote.AdditionalNote2.NoteText.ShouldBe("text8");
                    mappedNote.ControllingPartyNote.ShouldBe("text9");
                    mappedNote.IntangibleAssetsRevaluation.ShouldBe("text10");
                    mappedNote.OperatingProfitLoss.IsEnabled.ShouldBe(true);
                    mappedNote.OperatingProfitLoss.Items[0].Index.ShouldBe(1);
                    mappedNote.OperatingProfitLoss.Items[0].Description.ShouldBe("Test1");
                    mappedNote.OperatingProfitLoss.Items[0].Value.ShouldBe(-201.56M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].Index.ShouldBe(1);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidFive);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].DirectorName.ShouldBe("Director1");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtStartOfYear.ShouldBe(1100M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsAdvanced.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsRepaid.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWaived.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWrittenOff.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtEndOfYear.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AdvanceCreditConditions.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].Index.ShouldBe(2);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidSix);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].DirectorName.ShouldBe("Director2");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].BalanceOutstandingAtStartOfYear.ShouldBe(2200M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsAdvanced.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsRepaid.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsWaived.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsWrittenOff.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].BalanceOutstandingAtEndOfYear.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AdvanceCreditConditions.ShouldBeNull();
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.ValuationDetails.ShouldBe("Valuation Details long message");
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.IndependentValuerInvolved.ShouldBeTrue();
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.RevaluationBasis.ShouldBe("Revaluation Basis  short");
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.DateOfRevaluation.ShouldBe(DateTime.MinValue);
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.RevaluedAssetClass.ShouldBe("Revalued Asset Class");
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.RevaluedClassPronoun.ShouldBe("Revalued Class Pronoun");
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.CurrentReportingPeriodAccumulatedDepreciation.ShouldBe((1.33m));
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.CurrentReportingPeriodCost.ShouldBe(1.45m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems.Count.ShouldBe(2);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].LandAndBuildings.ShouldBe(1.1m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].PlantAndMachineryEtc.ShouldBe(2.2m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].Year.ShouldBe(2019);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].Index.ShouldBe(3);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].LandAndBuildings.ShouldBe(3.3m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].PlantAndMachineryEtc.ShouldBe(4.4m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].Year.ShouldBe(2020);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].Index.ShouldBe(4);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.CostLandAndBuildings.ShouldBe(5.5m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.CostPlantAndMachineryEtc.ShouldBe(6.6m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.TotalLandAndBuildings.ShouldBe(11);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.TotalPlantAndMachineryEtc.ShouldBe(12);
                    break;
                case ReportStandardType.UNINCORPORATED:
                    mappedNote.AverageNumberOfEmployees.ShouldNotBeNull();
                    mappedNote.AverageNumberOfEmployees.PreviousPeriod.ShouldBeNull();
                    mappedNote.AverageNumberOfEmployees.CurrentPeriod.ShouldBeNull();
                    mappedNote.OffBalanceSheetArrangements.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectors.ShouldBeNull();
                    mappedNote.GuaranteesAndOtherFinancialCommitments.ShouldBeNull();
                    mappedNote.RelatedPartyTransactions.ShouldBeNull();

                    mappedNote.MembersLiabilityText.NoteText.ShouldBe("Members Liability Note Text");
                    mappedNote.MembersLiabilityText.NoteTitle.ShouldBe("Members Liability Note Title");

                    mappedNote.AdditionalNote1.NoteTitle.ShouldBe("text5");
                    mappedNote.AdditionalNote1.NoteText.ShouldBe("text6");
                    mappedNote.AdditionalNote2.NoteTitle.ShouldBe("text7");
                    mappedNote.AdditionalNote2.NoteText.ShouldBe("text8");
                    mappedNote.ControllingPartyNote.ShouldBeNull();
                    mappedNote.IntangibleAssetsRevaluation.ShouldBeNull();
                    mappedNote.OperatingProfitLoss.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.ShouldBeNull();

                    break;
                default:
                    mappedNote.AverageNumberOfEmployees.ShouldNotBeNull();
                    mappedNote.AverageNumberOfEmployees.PreviousPeriod.ShouldBe(1);
                    mappedNote.AverageNumberOfEmployees.CurrentPeriod.ShouldBeNull();
                    mappedNote.OffBalanceSheetArrangements.ShouldBe("text1");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectors.ShouldBeNull();
                    mappedNote.GuaranteesAndOtherFinancialCommitments.ShouldBe("text3");
                    mappedNote.RelatedPartyTransactions.ShouldBe("text4");

                    mappedNote.MembersLiabilityText.NoteText.ShouldBe("Members Liability Note Text");
                    mappedNote.MembersLiabilityText.NoteTitle.ShouldBe("Members Liability Note Title");

                    mappedNote.AdditionalNote1.NoteTitle.ShouldBe("text5");
                    mappedNote.AdditionalNote1.NoteText.ShouldBe("text6");
                    mappedNote.AdditionalNote2.NoteTitle.ShouldBe("text7");
                    mappedNote.AdditionalNote2.NoteText.ShouldBe("text8");
                    mappedNote.ControllingPartyNote.ShouldBe("text9");
                    mappedNote.IntangibleAssetsRevaluation.ShouldBe("text10");
                    mappedNote.OperatingProfitLoss.IsEnabled.ShouldBe(true);
                    mappedNote.OperatingProfitLoss.Items[0].Index.ShouldBe(1);
                    mappedNote.OperatingProfitLoss.Items[0].Description.ShouldBe("Test1");
                    mappedNote.OperatingProfitLoss.Items[0].Value.ShouldBe(-201.56M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].Index.ShouldBe(1);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidFive);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].DirectorName.ShouldBe("Director1");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtStartOfYear.ShouldBe(1100M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsAdvanced.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsRepaid.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWaived.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AmountsWrittenOff.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].BalanceOutstandingAtEndOfYear.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[0].AdvanceCreditConditions.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].Index.ShouldBe(2);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].InvolvementClientGuid.ShouldBe(TestHelpers.Guids.GuidSix);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].DirectorName.ShouldBe("Director2");
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].BalanceOutstandingAtStartOfYear.ShouldBe(2200M);
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsAdvanced.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsRepaid.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsWaived.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AmountsWrittenOff.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].BalanceOutstandingAtEndOfYear.ShouldBeNull();
                    mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.Items[1].AdvanceCreditConditions.ShouldBeNull();
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.ValuationDetails.ShouldBe("Valuation Details long message");
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.IndependentValuerInvolved.ShouldBeTrue();
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.RevaluationBasis.ShouldBe("Revaluation Basis  short");
                    mappedNote.TangibleFixedAssetsNotes.ValuationInCurrentReportingPeriod.DateOfRevaluation.ShouldBe(DateTime.MinValue);
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.RevaluedAssetClass.ShouldBe("Revalued Asset Class");
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.RevaluedClassPronoun.ShouldBe("Revalued Class Pronoun");
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.CurrentReportingPeriodAccumulatedDepreciation.ShouldBe(1.33m);
                    mappedNote.TangibleFixedAssetsNotes.HistoricalCostBreakdown.CurrentReportingPeriodCost.ShouldBe(1.45m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems.Count.ShouldBe(2);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].LandAndBuildings.ShouldBe(1.1m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].PlantAndMachineryEtc.ShouldBe(2.2m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].Year.ShouldBe(2019);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[0].Index.ShouldBe(3);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].LandAndBuildings.ShouldBe(3.3m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].PlantAndMachineryEtc.ShouldBe(4.4m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].Year.ShouldBe(2020);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.AnalysisOfCostOrValuationItems[1].Index.ShouldBe(4);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.CostLandAndBuildings.ShouldBe(5.5m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.CostPlantAndMachineryEtc.ShouldBe(6.6m);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.TotalLandAndBuildings.ShouldBe(11);
                    mappedNote.TangibleFixedAssetsNotes.AnalysisOfCostOrValuation.TotalPlantAndMachineryEtc.ShouldBe(12);
                    break;
            }
        }

        [Fact]
        public void Should_map_no_notes_when_no_notes_exists()
        {
            var note = new NotesResponseMessage
            {
                PreviousPeriodId = TestHelpers.Guids.GuidOne,
                PeriodId = TestHelpers.Guids.GuidTwo,
                ClientId = TestHelpers.Guids.GuidThree,
                TenantId = TestHelpers.Guids.GuidFour,
                CorrelationId = TestHelpers.Guids.GuidFour,
                IsSuccessful = false,
                Error = "error"
            };

            var mappedNote = _mapper.Map<Notes>(note);
            mappedNote.ClientId.ShouldBe(TestHelpers.Guids.GuidThree);
            mappedNote.PeriodId.ShouldBe(TestHelpers.Guids.GuidTwo);
            mappedNote.TenantId.ShouldBe(TestHelpers.Guids.GuidFour);
            mappedNote.PreviousPeriodId.ShouldBe(TestHelpers.Guids.GuidOne);
            mappedNote.CorrelationId.ShouldBe(TestHelpers.Guids.GuidFour);
            mappedNote.IsSuccessful.ShouldBe(false);
            mappedNote.Error.ShouldNotBeNull("error");
            mappedNote.AverageNumberOfEmployees.ShouldNotBeNull();
            mappedNote.AverageNumberOfEmployees.CurrentPeriod.ShouldBeNull();
            mappedNote.AverageNumberOfEmployees.PreviousPeriod.ShouldBeNull();
            mappedNote.OffBalanceSheetArrangements.ShouldBeNull();
            mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectors.ShouldBeNull();
            mappedNote.GuaranteesAndOtherFinancialCommitments.ShouldBeNull();
            mappedNote.RelatedPartyTransactions.ShouldBeNull();

            mappedNote.MembersLiabilityText.NoteText.ShouldBeNull();
            mappedNote.MembersLiabilityText.NoteTitle.ShouldBeNull();

            mappedNote.AdditionalNote1.NoteTitle.ShouldBeNull();
            mappedNote.AdditionalNote1.NoteText.ShouldBeNull();
            mappedNote.AdditionalNote2.NoteTitle.ShouldBeNull();
            mappedNote.AdditionalNote2.NoteText.ShouldBeNull();
            mappedNote.ControllingPartyNote.ShouldBeNull();
            mappedNote.IntangibleAssetsRevaluation.ShouldBeNull();
            mappedNote.OperatingProfitLoss.ShouldBeNull();
            mappedNote.AdvancesCreditAndGuaranteesGrantedToDirectorsExtended.ShouldBeNull();
        }
    }
}
