﻿using AccountsProduction.AccountsBuilder.Application.Commands.GenerateReport;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder;
using FluentValidation.TestHelper;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.GenerateReport
{
    public class GenerateReportCommandValidatorTests
    {
        private readonly GenerateReportCommandValidator _validator;

        public GenerateReportCommandValidatorTests()
        {
            _validator = new GenerateReportCommandValidator();
        }

        [Fact]
        public async Task Should_fail_when_empty_period()
        {
            var model = new GenerateReportCommand
            {
                ClientId = TestHelpers.Guids.GuidOne,
                ReportingStandard = new ReportingStandardDto
                {
                    Type = ReportStandardType.FRS105
                },
            };

            var result = await _validator.TestValidateAsync(model);

            result.Errors.Count.ShouldBe(1);
            result.Errors.Any(x => x.PropertyName == nameof(GenerateReportCommand.PeriodId)).ShouldBeTrue();
        }

        [Fact]
        public async Task Should_pass_when_period_has_value()
        {
            var model = new GenerateReportCommand
            {
                PeriodId = TestHelpers.Guids.GuidOne,
                ReportingStandard = new ReportingStandardDto
                {
                    Type = ReportStandardType.FRS105
                },
            };

            var result = await _validator.TestValidateAsync(model);

            result.Errors.Count.ShouldBe(0);
        }

        [Fact]
        public async Task Should_fail_when_invalid_reportType()
        {
            var model = new GenerateReportCommand
            {
                ClientId = TestHelpers.Guids.GuidOne,
                PeriodId = TestHelpers.Guids.GuidTwo,
                ReportingStandard = new ReportingStandardDto
                {
                    Type = "invalid"
                }
            };

            var result = await _validator.TestValidateAsync(model);

            result.Errors.Count.ShouldBe(1);
            result.Errors.First().ErrorMessage.ShouldBe("Report type is invalid!");
        }

    }
}