﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.AspNetCore": "Warning",
        "Amazon.Lambda": "Warning",
        "AWSSDK": "Warning"
      }
    },
    "Properties": {
      "Application": "accountsproduction-accountsbuilder-func"
    }
  },
  "SVC_NAME": "accountsproduction-accountsbuilder-func",
  "ASPNETCORE_ENVIRONMENT": "Development",
  "Logging__LogLevel__Default": "Debug",
  "AWS__Region": "eu-west-2",
  "AWS_DEFAULT_REGION": "eu-west-2",
  "AWS_REGION": "eu-west-2",
  "AWS_ACCOUNT_ID": "************",

  "AWS_ACCESS_KEY_ID": "********************",
  "AWS_SECRET_ACCESS_KEY": "4IB46pqNdtg6Yc00gdoMGRtZZEdAhx2xhoqGJMRm",
  "AWS_SESSION_TOKEN": "IQoJb3JpZ2luX2VjEDUaCWV1LXdlc3QtMiJGMEQCIBsjiQ4n2kkgIzTJF9E3SyFlvleTmZEnnSBVuVmmI7ICAiAVX3kLi63AzHkFJaJCHIqcLotLBVg/+Hy0LGExE61jDSqjAgju//////////8BEAQaDDI0NTYzMzkzNDgxMiIM11c0EonBNOGs2QXXKvcBkISUHvEpLyTFlhTQD/buNpuYe35+WC9wtquGhvO9AsMLymdkj0I4Twq/CWQ+Twl5IDRfKnjat/zHibJPWi8uKDlxYXddnzu7V+rjycRzKhne4MBbru7RnkuLUXmKo5tlEQLC4nPw+DTZ+5iYdDWK4y+UYpYsoz4GG/TTBddmSP+fUzwwqmcFqUAg9Y57+KngDN0dN1OER9V6yrsMI2NldhfRYFYH2t3ZVbdP1ryhwYFwrMe5Ys5XMjYw4mV/k/xM4TLjJF3D/p4FY9fvJr9vTMu4BkoBAI01DFYSNlLcPa9bkln00fvcx+d/aRKOxOtStvEVB6KXZDC71sHBBjqeASAAvjHtFyDjLeCy9mGh0akT44D4tarM4c+15AZ8kAe4020SOTum4eoxXoCH5eyGBQLnCkvAgm4tveXL1bdDvOhnP56LaugvZ0ThW4ScK4rbBmQ2ePVRrB3wGXhu+ouTg42WUJrI5TjWovKwpKYSqMbiJkDkPtOThT+r198Mg9q6D5a926IcRAvbeClcZoF14df8L1ksCLHYaYWTZ99T",
  "AWS_SVC_ROLE_ARN": "arn:aws:sts::************:assumed-role/Developer/tempsession",

  "AWS_Profile": "Development",
  "AWS__Profile": "Development",
  "ACCOUNT_PERIOD_API_HOST": "vpce-0243baf16b4d45d9b-orv6s6vv.execute-api.eu-west-2.vpce.amazonaws.com/v1",
  "ACCOUNT_PERIOD_API_ID": "2fjj3yrota",
  "ACCOUNT_PERIOD_API_KEY": "7LfTggYWY72nuOdBBrIRrgc1UhwqMxT1Gqy7groh",
  "ACCOUNT_PERIOD_API_SCHEME": "https",
  "DB_CONNECTION": "Server=localhost;Port=5432;Database=hubdb;User Id=********;Password=********;",
  "REPORTINGCORE_DB_CONNECTION": "Server=localhost;Port=5432;Database=coredbtest;User Id=********;Password=********;",
  "ACCOUNTS_PRODUCTION_DB_DATABASE_NAME": "accountsproduction_hub_db",
  "ACCOUNTS_PRODUCTION_DB_HOST": "accountsproduction-hub-dbs-instance-development.cluster-ckvo9ttwlplu.eu-west-2.rds.amazonaws.com",
  "ACCOUNTS_PRODUCTION_DB_PORT": "5432",
  "ACCOUNTS_PRODUCTION_DB_USERNAME": "accountsproduction_hub_db_user",
  "ACCOUNTSBUILDER_API_HOST": "vpce-0243baf16b4d45d9b-orv6s6vv.execute-api.eu-west-2.vpce.amazonaws.com/v1",
  "ACCOUNTSBUILDER_API_ID": "bnc83xp1ji",
  "ACCOUNTSBUILDER_API_KEY": "aG6VrQzlAm3cS7Z8Y3kOXa9Mn25GSTwq6ABCvOPE",
  "ACCOUNTSBUILDER_API_SCHEME": "https",
  "ACCOUNTSBUILDER_REPORTING_QUEUE": "https://sqs.eu-west-2.amazonaws.com/************/accountsproduction-accountsbuilder-reporting-sqs",
  "ACCOUNTSPRODUCTION_ACCOUNTPERIOD_REQUEST_TOPIC": "arn:aws:sns:eu-west-2:************:accountsproduction-accountperiod-request-topic",
  "ACCOUNTSPRODUCTION_ACCOUNTSBUILDER_INPUT_TOPIC": "arn:aws:sns:eu-west-2:************:accountsproduction-accountsbuilder-input-topic",
  "ACCOUNTSPRODUCTION_API_HOST": "vpce-0243baf16b4d45d9b-orv6s6vv.execute-api.eu-west-2.vpce.amazonaws.com/v1",
  "ACCOUNTSPRODUCTION_API_ID": "x2i0zl16zk",
  "ACCOUNTSPRODUCTION_API_KEY": "kU0tUSxSwU5z0Zu5ujNZb6dsH0e1efC455kQoTc5",
  "ACCOUNTSPRODUCTION_API_SCHEME": "https",
  "BUILD_NUMBER": "%build.number%",
  "CLIENT_ADDRESS_API_URL": "address/client/",
  "CLIENT_API_HOST": "vpce-0243baf16b4d45d9b-orv6s6vv.execute-api.eu-west-2.vpce.amazonaws.com/v1",
  "CLIENT_API_ID": "jsv7iud33f",
  "CLIENT_API_KEY": "SyAnWvAoQn3YALnLVuRUj1ZBfCtjNfZI1KxYEz7N",
  "CLIENT_API_SCHEME": "https",
  "CLIENT_API_URL": "client/",
  "CLIENT_INVOLVEMENT_API_URL": "involvement/client/",
  "CONCURRENCY_RETRY_COUNT": "3",
  "ENTITY_INVOLVEMENT_API_URL": "involvement/entity/",
  "REPORTING_TRIALBALANCE_REQUEST_TOPIC": "arn:aws:sns:eu-west-2:************:reporting-trialbalance-processing-request-topic",
  "TRIAL_BALANCE_API_HOST": "vpce-0243baf16b4d45d9b-orv6s6vv.execute-api.eu-west-2.vpce.amazonaws.com/v1",
  "TRIAL_BALANCE_API_ID": "2y1qwvx271",
  "TRIAL_BALANCE_API_KEY": "WxKJXO31oC1iS26QV9oee4ETfKPACF8W33GdEXMp",
  "TRIAL_BALANCE_API_SCHEME": "https",
  "EVENTBUS_AWS_ACCOUNT_ID": "************",
  "EVENT_BUS_MAIN_QUEUE_URL": "https://sqs.eu-west-2.amazonaws.com/************/platform-eventbus-main-queue",
  "EVENTBUS_LARGE_PAYLOADS_S3_BUCKET_NAME": "platform-eventbus-large-payloads",

  "applicationUrl": "https://localhost:5001;http://localhost:5000"

}