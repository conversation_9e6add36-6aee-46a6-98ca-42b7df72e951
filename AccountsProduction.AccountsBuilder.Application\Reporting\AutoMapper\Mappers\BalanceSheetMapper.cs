﻿using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper.Mappers
{
    public class BalanceSheetMapper : Profile
    {
        public BalanceSheetMapper()
        {
            CreateMap<BalanceSheetMessage, BalanceSheetFRS105>()
                .ForMember(x => x.CalledUpShareCapitalNotPaid, opt => opt.MapFrom(dto => dto.CalledUpShareCapitalNotPaidNumber()))
                .ForMember(x => x.FixedAssets, opt => opt.MapFrom(dto => dto.FixedAssetsNumber()))
                .ForMember(x => x.CurrentAssets, opt => opt.MapFrom(dto => dto.CurrentAssetsNumber()))
                .ForMember(x => x.PrepaymentsAndAccruedIncome, opt => opt.MapFrom(dto => dto.PrepaymentsAndAccruedIncomeNumber()))
                .ForMember(x => x.CreditorsAmountsFallingDueWithinOneYear, opt => opt.MapFrom(dto => dto.CreditorsAmountsFallingDueWithinOneYearNumber()))
                .ForMember(x => x.NetCurrentAssetsOrLiabilities, opt => opt.MapFrom(dto => dto.NetCurrentAssetsOrLiabilitiesNumber()))
                .ForMember(x => x.TotalAssetsLessCurrentLiabilities, opt => opt.MapFrom(dto => dto.TotalAssetsLessCurrentLiabilitiesNumber()))
                .ForMember(x => x.CreditorsAmountsFallingAfterMoreThanOneYear, opt => opt.MapFrom(dto => dto.CreditorsAmountsFallingAfterMoreThanOneYearNumber()))
                .ForMember(x => x.ProvisionsForLiabilities, opt => opt.MapFrom(dto => dto.ProvisionsForLiabilitiesNumber()))
                .ForMember(x => x.AccrualsAndDeferredIncome, opt => opt.MapFrom(dto => dto.AccrualsAndDeferredIncomeNumber()))
                .ForMember(x => x.NetAssets, opt => opt.MapFrom(dto => dto.NetAssetsNumber()))
                .ForMember(x => x.CapitalAndReserves, opt => opt.MapFrom(dto => dto.CapitalAndReservesNumber()))
                .ForMember(x => x.AccountPeriodId, expression => expression.Ignore())
                .ForMember(x => x.ClientId, expression => expression.Ignore());

            CreateMap<BalanceSheetMessage, BalanceSheetGeneral>()
                .ForMember(x => x.CalledUpShareCapitalNotPaid, opt => opt.MapFrom(dto => dto.CalledUpShareCapitalNotPaidNumber()))
                .ForMember(x => x.Goodwill, opt => opt.MapFrom(dto => dto.GoodwillNumber()))
                .ForMember(x => x.IntangibleAssets, opt => opt.MapFrom(dto => dto.IntangibleAssetsNumber()))
                .ForMember(x => x.TangibleFixedAssets, opt => opt.MapFrom(dto => dto.TangibleFixedAssetsNumber()))
                .ForMember(x => x.FixedAssetInvestments, opt => opt.MapFrom(dto => dto.FixedAssetInvestmentsNumber()))
                .ForMember(x => x.CurrentAssetInvestments, opt => opt.MapFrom(dto => dto.CurrentAssetInvestmentsNumber()))
                .ForMember(x => x.InvestmentProperty, opt => opt.MapFrom(dto => dto.InvestmentPropertyNumber()))
                .ForMember(x => x.Stock, opt => opt.MapFrom(dto => dto.StockNumber()))
                .ForMember(x => x.Debtors, opt => opt.MapFrom(dto => dto.DebtorsNumber()))
                .ForMember(x => x.CashAtBankAndInHand, opt => opt.MapFrom(dto => dto.CashAtBankAndInHandNumber()))
                .ForMember(x => x.PrepaymentsAndAccruedIncome, opt => opt.MapFrom(dto => dto.PrepaymentsAndAccruedIncomeNumber()))
                .ForMember(x => x.CreditorsAmountsFallingDueWithinOneYear, opt => opt.MapFrom(dto => dto.CreditorsAmountsFallingDueWithinOneYearNumber()))
                .ForMember(x => x.CreditorsAmountsFallingAfterMoreThanOneYear, opt => opt.MapFrom(dto => dto.CreditorsAmountsFallingAfterMoreThanOneYearNumber()))
                .ForMember(x => x.ProvisionsForLiabilities, opt => opt.MapFrom(dto => dto.ProvisionsForLiabilitiesNumber()))
                .ForMember(x => x.PensionSchemeAssetsLiabilities, opt => opt.MapFrom(dto => dto.PensionSchemeAssetsLiabilitiesNumber()))
                .ForMember(x => x.HealthcareObligatons, opt => opt.MapFrom(dto => dto.HealthcareObligatonsNumber()))
                .ForMember(x => x.AccrualsAndDeferredIncome, opt => opt.MapFrom(dto => dto.AccrualsAndDeferredIncomeNumber()))
                .ForMember(x => x.CalledUpShareCapital, opt => opt.MapFrom(dto => dto.CalledUpShareCapitalNumber()))
                .ForMember(x => x.SharePremiumReserve, opt => opt.MapFrom(dto => dto.SharePremiumReserveNumber()))
                .ForMember(x => x.RevaluationReserve, opt => opt.MapFrom(dto => dto.RevaluationReserveNumber()))
                .ForMember(x => x.CapitalRedemptionReserve, opt => opt.MapFrom(dto => dto.CapitalRedemptionReserveNumber()))
                .ForMember(x => x.OtherReserves1, opt => opt.MapFrom(dto => dto.OtherReserves1Number()))
                .ForMember(x => x.OtherReserves2, opt => opt.MapFrom(dto => dto.OtherReserves2Number()))
                .ForMember(x => x.FairValueReserve, opt => opt.MapFrom(dto => dto.FairValueReserveNumber()))
                .ForMember(x => x.ProfitAndLossReserve, opt => opt.MapFrom(dto => dto.ProfitAndLossReserveNumber()))
                .ForMember(x => x.NonControllingInterests, opt => opt.MapFrom(dto => dto.NonControllingInterestsNumber()))
                .ForMember(x => x.HerdBasis, opt => opt.MapFrom(dto => dto.HerdBasisNumber()))
                .ForMember(x => x.AccountPeriodId, expression => expression.Ignore())
                .ForMember(x => x.ClientId, expression => expression.Ignore());

            CreateMap<BalanceSheetMessage, BalanceSheetLLP>()
                .ForMember(x => x.TotalMembersInterests, opt => opt.MapFrom(dto => dto.TotalMembersInterestsNumber()))
                .ForMember(x => x.MembersOtherInterests, opt => opt.MapFrom(dto => dto.MembersOtherInterestsNumber()))
                .ForMember(x => x.MembersCapital, opt => opt.MapFrom(dto => dto.MembersCapitalNumber()))
                .ForMember(x => x.OtherDebtsDueToMembers, opt => opt.MapFrom(dto => dto.OtherDebtsDueToMembersNumber()))
                .ForMember(x => x.LoansAndOtherDebtsDueToMembers, opt => opt.MapFrom(dto => dto.LoansAndOtherDebtsDueToMembersNumber()))
                .ForMember(x => x.AccountPeriodId, expression => expression.Ignore())
                .ForMember(x => x.ClientId, expression => expression.Ignore());

            CreateMap<BalanceSheetMessage, BalanceSheetCH>()
                .ForMember(x => x.AccountPeriodId, expression => expression.Ignore())
                .ForMember(x => x.ClientId, expression => expression.Ignore());

            CreateMap<BalanceSheetMessage, BalanceSheetNonCorp>()
                .ForMember(x => x.CapitalAccount, opt => opt.MapFrom(dto => dto.CapitalAccountNumber()))
                .ForMember(x => x.PartnersCapitalAccounts, opt => opt.MapFrom(dto => dto.PartnersCapitalAccountsNumber()))
                .ForMember(x => x.PartnersCurrentAccounts, opt => opt.MapFrom(dto => dto.PartnersCurrentAccountsNumber()))
                .ForMember(x => x.OtherReserves, opt => opt.MapFrom(dto => dto.OtherReservesNumber()))
                .ForMember(x => x.AccountPeriodId, expression => expression.Ignore())
                .ForMember(x => x.ClientId, expression => expression.Ignore());

            CreateMap<BalanceSheetMessage, BalanceSheetIFRS>()
                .ForMember(x => x.AccountPeriodId, expression => expression.Ignore())
                .ForMember(x => x.ClientId, expression => expression.Ignore());
        }
    }
}
