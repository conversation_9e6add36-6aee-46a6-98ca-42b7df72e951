﻿namespace AccountsProduction.AccountsBuilder.UnitTests
{
    internal static class TestHelpers
    {
        internal static class Guids
        {
            public static Guid GuidOne => new("********-1111-1111-1111-********1111");
            public static Guid GuidTwo => new("*************-2222-2222-************");
            public static Guid GuidThree => new("*************-3333-3333-************");
            public static Guid GuidFour => new("*************-4444-4444-************");
            public static Guid GuidFive => new("*************-5555-5555-************");
            public static Guid GuidSix => new("*************-6666-6666-************");
            public static Guid GuidSeven => new("*************-7777-7777-************");
            public static Guid GuidEight => new("*************-8888-8888-************");
            public static Guid GuidNine => new("*************-9999-9999-************");
        }

        public static bool HasAttributeWithExpectedArgument<T, TArgument>(this T controller, Type attributeType, TArgument expectedArgument)
        {
            var attributesData = controller.GetType().GetCustomAttributesData().Where(a => a.AttributeType == attributeType).ToArray();

            return attributesData.Any(attribute =>
                 attribute.ConstructorArguments.Any(arg =>
                     arg.ArgumentType == typeof(TArgument) && EqualityComparer<TArgument>.Default.Equals((TArgument)arg.Value, expectedArgument)));
        }

        public static bool HasAttribute<T>(this T controller, Type attributeType)
        {
            return controller.GetType().GetCustomAttributesData().Any(a => a.AttributeType == attributeType);
        }
    }
}