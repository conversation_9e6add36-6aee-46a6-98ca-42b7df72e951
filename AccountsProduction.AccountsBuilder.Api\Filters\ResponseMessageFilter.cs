﻿using Iris.Elements.Http.Api.Models.Response;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Diagnostics.CodeAnalysis;
using System.Net;

namespace AccountsProduction.AccountsBuilder.Api.Filters
{
    [ExcludeFromCodeCoverage]
    public class ResponseMessageFilter : IAsyncResultFilter
    {
        public async Task OnResultExecutionAsync(ResultExecutingContext context, ResultExecutionDelegate next)
        {
            if (context.Result is ObjectResult result)
            {
                var successResponse = new SuccessResponse<object>
                {
                    Data = result.Value
                };

                context.Result = new ObjectResult(successResponse) { StatusCode = (int)HttpStatusCode.OK };

                if (result is BadRequestObjectResult)
                {
                    var errorResponse = new ErrorResponse<object>();
                    context.Result = new ObjectResult(errorResponse) { StatusCode = (int)HttpStatusCode.BadRequest };
                }
            }

            await next();
        }
    }
}