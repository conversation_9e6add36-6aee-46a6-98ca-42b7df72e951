﻿using AccountsProduction.AccountsBuilder.Application.Common;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs
{
    public static class BalanceSheetBreadcrumbs
    {
        private const string AccountsBuilderName = "Accounts Builder";
        private const string BalanceSheetName = "Balance sheet";

        public static Breadcrumb BalanceSheetRule =>
            new Breadcrumb
            {
                Value = AccountsBuilderName,
                Child = new Breadcrumb
                {
                    Value = BreadcrumbSection.Frs1021A,
                    Child = new Breadcrumb
                    {
                        Value = BalanceSheetName,
                        Child = null
                    }
                }
            };
    }
}