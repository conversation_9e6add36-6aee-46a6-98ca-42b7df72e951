using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Reporting.Application.Common;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies
{
    public class IFRSStrategy : IFRSBaseStrategy
    {
        public IFRSStrategy(
            IMediator mediator,
            ILogger<IFRSStrategy> logger,
            IAccountsProductionReportingDbContext accountsProductionReportingDbContext,
            IMapper mapper) : base(mediator, logger, accountsProductionReportingDbContext, mapper)
        {
        }

        public override string ReportTypeName => ReportType.IFRS;

        public override async Task SendSaveAndUpdateDataEvents(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
        {
            await SendIFRSBaseSaveAndUpdateDataEvents(data, ReportType.IFRS, cancellationToken);
        }
    }
}
