﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NotesRunner.NoteFrs1021aRunner
{
    public static class NotesFrs1021aValidations
    {
        public const string IntangibleAssetsRevaluation = "Intangible assets - Revaluation";
        public const string AdvancesCreditGuarantees = "AdvancesCreditGuarantees";
        public const string TangibleFixedAssetsNotes = "Tangible fixed assets notes";
        public const string TangibleFixedAssetsHistoricAndAnalysisNotes = "Tangible fixed assets analysis of cost and valuation and historical cost breakdown sections";

        public static readonly ValidationRuleConfig RevaluationConfig = new ValidationRuleConfig
        {
            Breadcrumb = NotesFrs1021aBreadcrumbs.IntangibleAssetsRevaluation.ToString(),
            Description = "Intangible assets - Revaluation should be hidden.",
            Name = IntangibleAssetsRevaluation,
            Type = ValidationRuleType.Missing,
            Target = Target.SectionValidation,
            ErrorCategory = ErrorCategoryType.Advisory,
            DisplayName = "Intangible assets - Revaluation Note",
            ErrorCode = ValidationCodes.IntangibleAssetsRevaluation
        };

        public static readonly ValidationRuleConfig TangibleFixedAssestsNoteConfig = new ValidationRuleConfig
        {
            Breadcrumb = NotesFrs1021aBreadcrumbs.TangibleFixedAssetsNotesSection.ToString(),
            Description = "Tangible fixed assets notes in current reporting period should be hidden.",
            Name = TangibleFixedAssetsNotes,
            Type = ValidationRuleType.Missing,
            Target = Target.SectionValidation,
            ErrorCategory = ErrorCategoryType.Advisory,
            DisplayName = "Tangible fixed assets notes",
            ErrorCode = ValidationCodes.TangibleFixedAssetsNotes
        };

        public static readonly ValidationRuleConfig TangibleFixedAssestsHistoricalCostBreakdownNoteConfig = new ValidationRuleConfig
        {
            Breadcrumb = NotesFrs1021aBreadcrumbs.TangibleFixedAssetsHistoricBreakdownNotesSection.ToString(),
            Description = "The historical cost breakdown section of the Tangible fixed assets note must be completed.",
            Name = TangibleFixedAssetsHistoricAndAnalysisNotes,
            Type = ValidationRuleType.Missing,
            Target = Target.IssueLog,
            ErrorCategory = ErrorCategoryType.Mandatory,
            DisplayName = "Tangible fixed assets note historical cost breakdown sections",
            ErrorCode = ValidationCodes.TangibleFixedAssetsHistoricalCostBreakdownNotesNotComplete
        };

        public static readonly ValidationRuleConfig TangibleFixedAssestsAnalysisOfCostAndValuationNoteConfig = new ValidationRuleConfig
        {
            Breadcrumb = NotesFrs1021aBreadcrumbs.TangibleFixedAssetsAnalysisOfCostAndValuationSection.ToString(),
            Description = "The analysis of cost or valuation section of the Tangible fixed assets note must be completed.",
            Name = TangibleFixedAssetsHistoricAndAnalysisNotes,
            Type = ValidationRuleType.Missing,
            Target = Target.IssueLog,
            ErrorCategory = ErrorCategoryType.Mandatory,
            DisplayName = "Tangible fixed assets note analysis of cost or valuation sections",
            ErrorCode = ValidationCodes.TangibleFixedAssetsAnalysisOfCostAndValuationNotesNotComplete
        };

        public static readonly ValidationRuleConfig AdvancesCreditGuaranteesConfig =
         new ValidationRuleConfig
         {
             Breadcrumb = NotesFrs1021aBreadcrumbs.AdvancesCreditGuarantees.ToString(),
             Description = "A note for Advances, credits and guarantees granted to Directors has not been entered.",
             Name = AdvancesCreditGuarantees,
             Type = ValidationRuleType.Missing,
             Target = Target.IssueLog,
             ErrorCategory = ErrorCategoryType.Mandatory,
             DisplayName = "Advances, credits and guarantees granted to Directors",
             ErrorCode = ValidationCodes.AdvancesCreditAndGuaranteesGrantedForDirectors
         };
    }
}