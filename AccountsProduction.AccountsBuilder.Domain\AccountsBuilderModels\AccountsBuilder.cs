﻿using AccountsProduction.AccountsBuilder.Domain.AccountingPoliciesModels;
using AccountsProduction.AccountsBuilder.Domain.DataScreens;
using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;
using AccountsProduction.AccountsBuilder.Domain.NoteModels;
using Amazon.DynamoDBv2.DataModel;
using Iris.AccountsProduction.Common.Toolkit.ExtensionMethods.Multitenancy;
using Iris.Elements.Domain.Seedwork;
using Iris.Elements.DynamoDb.Converters;
using Newtonsoft.Json.Linq;

namespace AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels
{
    public class AccountsBuilder : Entity, IMultitenantEntity, IFRS105, IFRS102, IFRS1021A, IFRS, IUnincorporated
    {
        [DynamoDBProperty(typeof(GuidConverter))]
        [DynamoDBHashKey]
        public Guid ClientId { get; set; }

        [DynamoDBProperty(typeof(GuidConverter))]
        public Guid PeriodId { get; set; }

        public Guid TenantId { get; set; }

        [DynamoDBGlobalSecondaryIndexHashKey("ProcessId-index")]
        public Guid ProcessId { get; set; }

        public Guid? LastSuccessfullProcessId { get; set; }

        [DynamoDBProperty(typeof(DateTimeConverter))]
        public DateTime EntityModificationTime { get; set; }

        public AccountPeriod AccountPeriod { get; set; }

        public TrialBalance TrialBalance { get; set; }

        public Signatory Signatory { get; set; }
        public EntitySetup EntitySetup { get; set; }

        public ReportingStandard ReportingStandard { get; set; }
        public LicenseData LicenseData { get; set; }

        public FinancialData FinancialData { get; set; }

        public NonFinancialData NonFinancialData { get; set; }

        public ProfitShareData ProfitShareData { get; set; }

        public InvolvementsData InvolvementsData { get; set; }

        public Notes Notes { get; set; }

        public DataScreenValue DataScreenValue { get; set; }

        public PracticeDetails PracticeDetails { get; set; }

        public AccountingPolicies AccountingPolicies { get; set; }

        [DynamoDBProperty(typeof(DateTimeConverter))]
        public DateTime CreatedTimeUtc { get; set; }

        [DynamoDBVersion] public int? VersionNumber { get; set; }

        public bool IsDirty { get; set; }

        public Status Status { get; set; }

        public string? ErrorCode { get; set; }

        [DynamoDBProperty(typeof(DateTimeConverter))]
        public DateTime LastSuccessfullTimeUtc { get; set; }

        public ValidationData ValidationData { get; set; }

        public AccountsBuilder()
        {
            CreatedTimeUtc = DateTime.UtcNow;
            EntityModificationTime = DateTime.UtcNow;
            ValidationData = new ValidationData();
            ReportingStandard = new ReportingStandard();
            LicenseData = new LicenseData();
            TrialBalance = new TrialBalance();
            ProfitShareData = new ProfitShareData();
            Signatory = new Signatory();
            FinancialData = new FinancialData();
            NonFinancialData = new NonFinancialData();
            InvolvementsData = new InvolvementsData();
            DataScreenValue = new DataScreenValue();
        }

        public AccountsBuilder(string tenantId, Guid clientId, Guid periodId, EntitySetup entitySetup, LicenseData licenseData, ReportingStandard reportingStandard)
        {
            if (clientId == Guid.Empty) throw new ArgumentNullException(nameof(clientId));
            if (periodId == Guid.Empty) throw new ArgumentNullException(nameof(periodId));
            if (!Guid.TryParse(tenantId, out var tenantGuid)) throw new ArgumentNullException(nameof(tenantId));

            TenantId = tenantGuid;
            ClientId = clientId;
            PeriodId = periodId;
            ReportingStandard = reportingStandard;
            EntitySetup = entitySetup;
            LicenseData = licenseData;
            EntityModificationTime = DateTime.UtcNow;
            ValidationData = new ValidationData();
            TrialBalance = new TrialBalance();
            CreatedTimeUtc = DateTime.UtcNow;
            ProfitShareData = new ProfitShareData();
            Signatory = new Signatory();
            FinancialData = new FinancialData();
            NonFinancialData = new NonFinancialData();
            InvolvementsData = new InvolvementsData();
            DataScreenValue = new DataScreenValue();
        }

        public AccountsBuilder(string tenantId, Guid clientId, Guid periodId, EntitySetup entitySetup)
        {
            if (clientId == Guid.Empty) throw new ArgumentNullException(nameof(clientId));
            if (periodId == Guid.Empty) throw new ArgumentNullException(nameof(periodId));
            if (!Guid.TryParse(tenantId, out var tenantGuid)) throw new ArgumentNullException(nameof(tenantId));

            TenantId = tenantGuid;
            ClientId = clientId;
            PeriodId = periodId;
            EntitySetup = entitySetup;
            EntityModificationTime = DateTime.UtcNow;
            ValidationData = new ValidationData();
            TrialBalance = new TrialBalance();
            CreatedTimeUtc = DateTime.UtcNow;
            ProfitShareData = new ProfitShareData();
            Signatory = new Signatory();
            FinancialData = new FinancialData();
            NonFinancialData = new NonFinancialData();
            InvolvementsData = new InvolvementsData();
            DataScreenValue = new DataScreenValue();
        }

        public AccountsBuilder(string tenantId, Guid clientId, Guid periodId)
        {
            if (clientId == Guid.Empty) throw new ArgumentNullException(nameof(clientId));
            if (periodId == Guid.Empty) throw new ArgumentNullException(nameof(periodId));
            if (!Guid.TryParse(tenantId, out var tenantGuid)) throw new ArgumentNullException(nameof(tenantId));

            TenantId = tenantGuid;
            ClientId = clientId;
            PeriodId = periodId;
            EntityModificationTime = DateTime.UtcNow;
            ValidationData = new ValidationData();
            TrialBalance = new TrialBalance();
            CreatedTimeUtc = DateTime.UtcNow;
            ProfitShareData = new ProfitShareData();
            Signatory = new Signatory();
            FinancialData = new FinancialData();
            NonFinancialData = new NonFinancialData();
            InvolvementsData = new InvolvementsData();
            DataScreenValue = new DataScreenValue();
            Notes = new Notes();
            DataScreenValue = new DataScreenValue();
            AccountingPolicies = new AccountingPolicies();
        }

        public void UpdateAccountPeriod(AccountPeriod accountPeriod)
        {
            AccountPeriod = accountPeriod;
            AccountPeriod?.UpdateModificationTime();
            SetDirty();
            UpdateEntityModificationTime();
        }

        public void UpdateLicenseData(LicenseData licenseData)
        {
            LicenseData = licenseData;
            SetDirty();
            UpdateEntityModificationTime();
        }
        public void UpdateReportingStandard(ReportingStandard reportingStandard)
        {
            ReportingStandard = reportingStandard;
            SetDirty();
            UpdateEntityModificationTime();
        }
        public void AddTrialBalance(TrialBalance trialBalance)
        {
            TrialBalance = trialBalance;
            TrialBalance?.UpdateModificationTime();
            SetDirty();
            UpdateEntityModificationTime();
        }

        public void AddSignatory(Signatory signatory)
        {
            Signatory = signatory;
            Signatory?.UpdateModificationTime();
            SetDirty();
            UpdateEntityModificationTime();
        }

        public void AddEntitySetup(EntitySetup entitySetup)
        {
            EntitySetup = entitySetup;
            EntitySetup?.UpdateModificationTime();
            SetDirty();
            UpdateEntityModificationTime();
        }

        public void UpdateFinancialData(FinancialData financialData)
        {
            FinancialData = financialData;
            FinancialData?.UpdateModificationTime();
            SetDirty();
            UpdateEntityModificationTime();
        }

        public void UpdateNonFinancialData(NonFinancialData nonFinancialData)
        {
            NonFinancialData = nonFinancialData;
            NonFinancialData?.UpdateModificationTime();
            SetDirty();
            UpdateEntityModificationTime();
        }

        public void UpdateNonFinancialData(JObject nonFinancialData)
        {
            NonFinancialData?.UpdateRecord(nonFinancialData);
            NonFinancialData?.UpdateModificationTime();
            SetDirty();
            UpdateEntityModificationTime();
        }

        public void AddInvolvements(InvolvementsData involvements)
        {
            InvolvementsData = involvements;
            SetDirty();
            UpdateEntityModificationTime();
        }

        public void AddNotes(Notes notes)
        {
            Notes = notes;
            Notes?.UpdateModificationTime();
            SetDirty();
            UpdateEntityModificationTime();
        }

        public void AddDataScreenValue(DataScreenValue dataScreenValue)
        {
            DataScreenValue = dataScreenValue;
            SetDirty();
            UpdateEntityModificationTime();
        }
        public void AddAccountingPolicies(AccountingPolicies accountingPolicies)
        {
            AccountingPolicies = accountingPolicies;
            AccountingPolicies?.UpdateModificationTime();
            SetDirty();
            UpdateEntityModificationTime();
        }

        public void AddDataScreenValues(DataScreenValue dataScreenValues)
        {
            DataScreenValue = dataScreenValues;
            SetDirty();
            UpdateEntityModificationTime();
        }

        public void AddPracticeDetails(PracticeDetails practiceDetails)
        {
            PracticeDetails = practiceDetails;
            SetDirty();
            UpdateEntityModificationTime();
        }

        public void AddPartnerProfitShares(ProfitShareData partnershipProfitShare)
        {
            ProfitShareData = partnershipProfitShare;
            ProfitShareData?.UpdateModificationTime();
            SetDirty();
            UpdateEntityModificationTime();
        }

        public void UpdateEntityModificationTime(DateTime dateTime)
        {
            EntityModificationTime = dateTime;
        }

        public void UpdateEntityModificationTime()
        {
            UpdateEntityModificationTime(DateTime.UtcNow);
        }

        public void UpdateLastSuccessfullTime(DateTime dateTime)
        {
            LastSuccessfullTimeUtc = dateTime;
        }

        public void UpdateLastSuccessfullTime()
        {
            UpdateLastSuccessfullTime(DateTime.UtcNow);
        }

        public void UpdateErrorCode(string? errorCode)
        {
            ErrorCode = errorCode;
        }

        public void SetStatusSuccessful()
        {
            Status = Status.Successful;
        }

        public void SetStatusFail()
        {
            Status = Status.Fail;
        }

        public void SetStatus(Status status)
        {
            Status = status;
        }

        public void SetDirty()
        {
            IsDirty = true;
        }
        public void SetClean()
        {
            IsDirty = false;
        }

        public void UpdateValidations(ValidationData validations)
        {
            ValidationData = validations;
        }

        public void UpdateLastSuccessfullProcessId()
        {
            LastSuccessfullProcessId = ProcessId;
        }

        public void SetLastSuccessfullProcessId(Guid processId)
        {
            LastSuccessfullProcessId = processId;
        }

        public void SetProcessId(Guid processId)
        {
            ProcessId = processId;
        }
    }
}