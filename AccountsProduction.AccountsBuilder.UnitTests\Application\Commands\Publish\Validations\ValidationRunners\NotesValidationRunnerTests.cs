﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NotesRunner;
using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Domain.NoteModels;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations.ValidationRunners
{
    public class NotesValidationRunnerTests
    {
        public class When_validating_average_number_of_employees : NotesValidationRunnerTests
        {
            [Fact]
            public void Should_return_validation_issues_for_average_number_of_employees_frs102_1a()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            AverageNumberOfEmployees = new AverageNumberOfEmployees
                            {
                                CurrentPeriod = null,
                                PreviousPeriod = null
                            }
                        }
                    }
                };

                var issues =
                    new NotesValidationRunner(ReportStandardType.FRS102_1A).Validate(
                        accountsBuilder);
                this.ShouldSatisfyAllConditions(
                    () => issues.Any(el => el.Name == NotesValidations.AverageNumberOfEmployeesCurrentPeriod && el.ErrorCode == ValidationCodes.AverageNumberOfEmployeesCurrentPeriod
                    && el.Breadcrumb.Contains("Sections FRS102 - Section 1A")).ShouldBeTrue(),
                    () => issues.Any(el => el.Name == NotesValidations.AverageNumberOfEmployeesPreviousPeriod &&
                    el.ErrorCode == ValidationCodes.AverageNumberOfEmployeesPreviousPeriod && el.Breadcrumb.Contains("Sections FRS102 - Section 1A"))
                        .ShouldBeTrue());
            }


            [Fact]
            public void Should_return_validation_issues_for_average_number_of_employees_frs105()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            AverageNumberOfEmployees = new AverageNumberOfEmployees
                            {
                                CurrentPeriod = null,
                                PreviousPeriod = null
                            }
                        }
                    }
                };

                var issues =
                    new NotesValidationRunner(ReportStandardType.FRS105).Validate(
                        accountsBuilder);
                this.ShouldSatisfyAllConditions(
                    () => issues.Any(el => el.Name == NotesValidations.AverageNumberOfEmployeesCurrentPeriod && el.ErrorCode == ValidationCodes.AverageNumberOfEmployeesCurrentPeriod
                    && el.Breadcrumb.Contains("Sections FRS105")).ShouldBeTrue(),
                    () => issues.Any(el => el.Name == NotesValidations.AverageNumberOfEmployeesPreviousPeriod &&
                    el.ErrorCode == ValidationCodes.AverageNumberOfEmployeesPreviousPeriod && el.Breadcrumb.Contains("Sections FRS105"))
                        .ShouldBeTrue());
            }

            [Fact]
            public void Should_not_return_validation_issues_for_zero_values_for_average_number_of_employees()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    Notes = new Notes
                    {
                        CurrentPeriod = new NotesData
                        {
                            AverageNumberOfEmployees = new AverageNumberOfEmployees
                            {
                                CurrentPeriod = 0,
                                PreviousPeriod = 0
                            }
                        }
                    }
                };
                var issues =
                    new NotesValidationRunner(ReportStandardType.FRS102_1A).Validate(
                        accountsBuilder);
                this.ShouldSatisfyAllConditions(
                    () => issues.Any(el => el.Name == NotesValidations.AverageNumberOfEmployeesCurrentPeriod)
                        .ShouldBeFalse(),
                    () => issues.Any(el => el.Name == NotesValidations.AverageNumberOfEmployeesPreviousPeriod)
                        .ShouldBeFalse());
            }

            [Fact]
            public void Should_return_validation_issue_only_for_current_period_when_previous_period_is_populated()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    Notes = new Notes
                    {
                        PreviousPeriod = new NotesData
                        {
                            AverageNumberOfEmployees = new AverageNumberOfEmployees
                            {
                                CurrentPeriod = 0,
                                PreviousPeriod = 0
                            }
                        }
                    }
                };
                var issues =
                    new NotesValidationRunner(ReportStandardType.FRS102_1A).Validate(
                        accountsBuilder);
                this.ShouldSatisfyAllConditions(
                    () => issues.Any(el => el.Name == NotesValidations.AverageNumberOfEmployeesCurrentPeriod && el.ErrorCode == ValidationCodes.AverageNumberOfEmployeesCurrentPeriod)
                        .ShouldBeTrue(),
                    () => issues.Any(el => el.Name == NotesValidations.AverageNumberOfEmployeesPreviousPeriod)
                        .ShouldBeFalse());
            }
        }
    }
}