﻿using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.PracticeDetails;
using AccountsProduction.AccountsBuilder.Domain;
using AutoMapper;
using Iris.AccountsProduction.AccountsBuilder.Messages;
using Microsoft.Extensions.Logging;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Aggregate.EventHandlers.ReportDataEvents
{
    public class PracticeDetailsDataEventStrategy : BaseAggregationStrategy
    {
        public override string Name => EventTypes.PracticeDetailsDataEvent;

        private readonly IAccountsBuilderRepository _repository;
        private readonly ILogger<PracticeDetailsDataEventStrategy> _logger;
        private readonly IMapper _mapper;

        public PracticeDetailsDataEventStrategy(IAccountsBuilderRepository repository,
            ILogger<PracticeDetailsDataEventStrategy> logger, IMapper mapper)
        {
            _repository = repository;
            _logger = logger;
            _mapper = mapper;
        }

        public override async Task Process(string requestMessage, Guid tenantId, Guid clientId, Guid periodId, Guid processId, CancellationToken cancellationToken)
        {
            _logger.LogInformation("START event type {eventType} for  tenantId {tenantId} at eventTimestamp {eventTimestamp}.", Name, tenantId, DateTime.UtcNow);

            var practiceDetailsDto = Deserialize<PracticeDetailMessage>(requestMessage);
            var practiceDetailsData = _mapper.Map<Domain.PracticeDetails>(practiceDetailsDto);

            var accountsBuilders = (await _repository.GetByTenantId(tenantId, cancellationToken))
                                        .OrderByDescending(o => o.EntityModificationTime)
                                        .ToList();

            await Task.WhenAll(accountsBuilders.Select(s => Save(s, practiceDetailsData, practiceDetailsDto.PracticeDetailId.ToString(), practiceDetailsDto.IsPrimaryPracticeOffice, cancellationToken)));

            _logger.LogInformation("FINISHED event type {eventType} for  tenantId {tenantId} at eventTimestamp {eventTimestamp}.", Name, tenantId, DateTime.UtcNow);
        }

        private async Task Save(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder, PracticeDetails practiceDetails, string practiceDetailsId, bool isPrimaryPracticeAddress, CancellationToken cancellationToken)
        {
            try
            {
                if (accountsBuilder.EntitySetup?.PracticeAddress == practiceDetailsId ||
                    (string.IsNullOrEmpty(accountsBuilder.EntitySetup?.PracticeAddress) && isPrimaryPracticeAddress))
                {
                    await Retry.RunAndHandleConcurrency(
                            async () =>
                            {
                                accountsBuilder.AddPracticeDetails(practiceDetails);

                                await _repository.Save(accountsBuilder, cancellationToken);
                            },
                            async () =>
                            {
                                accountsBuilder = await _repository.Get(accountsBuilder.ClientId, accountsBuilder.PeriodId, cancellationToken);
                            },
                            _logger);
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Failed to process event with message type {EventType} for clientId {clientId} and periodId {periodId} at eventTimestamp {eventTimestamp}. ", Name, accountsBuilder.ClientId, accountsBuilder.PeriodId, DateTime.UtcNow);
            }
        }
    }

}