﻿namespace AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData
{
    public enum FinancialDataStatusDto
    {
        Success = 1,
        Error = 2
    }

    public enum ExceptionDetailsStatusCode
    {
        None = 0,
        Exception = 500,
        Validation = 400
    }

    public class FinancialDataDto
    {
        public Guid ClientId { get; set; }

        public Guid PeriodId { get; set; }

        public FinancialDataExceptionDetailsDto? Reason { get; set; } = null!;

        public FinancialDataStatusDto Status { get; set; }
        public DateTime EntityModificationTime { get; set; }

        public List<FinancialDto> Data { get; set; } = [];
    }

    public class FinancialDataExceptionDetailsDto
    {
        public string ExecutionStep { get; set; } = string.Empty;
        public ExceptionDetailsStatusCode StatusCode { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
