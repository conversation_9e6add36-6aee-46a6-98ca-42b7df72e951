﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NotesRunner.NoteFrs105Runner
{
    public static class NotesFrs105Validations
    {
        public const string AdvancesCreditGuarantees = "AdvancesCreditGuarantees";

        public static readonly ValidationRuleConfig AdvancesCreditGuaranteesConfig =
            new ValidationRuleConfig
            {
                Breadcrumb = NotesFrs105Breadcrumbs.AdvancesCreditGuaranteesRule.ToString(),
                Description = "A note for Advances, credits and guarantees granted to Directors has not been entered.",
                Name = AdvancesCreditGuarantees,
                Type = ValidationRuleType.Missing,
                Target = Target.IssueLog,
                ErrorCategory = ErrorCategoryType.Mandatory,
                DisplayName = "Advances, credits and guarantees granted to Directors",
                ErrorCode = ValidationCodes.AdvancesCreditAndGuaranteesGrantedForDirectors
            };
    }
}