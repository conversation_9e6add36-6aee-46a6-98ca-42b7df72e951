﻿using AccountsProduction.AccountsBuilder.Application.Common.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Application.Reporting.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper.Mappers;
using AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper.Mappers.NotesMappers;
using AutoMapper;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.AutoMapper
{
    public static class AutoMapperConfiguration
    {
        public static IMapper GetConfiguredAutoMapper()
        {
            var config = new MapperConfiguration(configuration =>
            {
                CommonMappers(configuration);
                ReportingMappers(configuration);
            });

            var mapper = config.CreateMapper();

            return mapper;
        }

        private static void ReportingMappers(IMapperConfigurationExpression configuration)
        {
            configuration.AddProfile(new Mappers.ProfitAndLossMapper());
            configuration.AddProfile(new ReportingPeriodMapper());
            configuration.AddProfile(new MemberMapper());
            configuration.AddProfile(new Mappers.BalanceSheetMapper());
            configuration.AddProfile(new NoteAccountingPoliciesMapper());
            configuration.AddProfile(new NoteBalanceSheetMapper());
            configuration.AddProfile(new NoteOtherMapper());
            configuration.AddProfile(new NoteProfitAndLossMapper());
            configuration.AddProfile(new TenantMapper());
            configuration.AddProfile(new LineItemMapper());
            configuration.AddProfile(new AccountsBuilderReportingMessageMapper());
            configuration.AddProfile(new DataScreenValuesMapper());
            configuration.AddProfile(new DplSummaryCalcsMapper());
        }

        private static void CommonMappers(IMapperConfigurationExpression configuration)
        {
            configuration.AddProfile(new AccountsBuilderMapper());
            configuration.AddProfile(new ReportingStandardMapper());
            configuration.AddProfile(new AccountPeriodMapper());
            configuration.AddProfile(new EntitySetupMapper());
            configuration.AddProfile(new LicenseDataMapper());
            configuration.AddProfile(new FinancialDataMapper());
            configuration.AddProfile(new TrialBalanceMapper());
            configuration.AddProfile(new SignatoryMapper());
            configuration.AddProfile(new DplDataMapper());
            configuration.AddProfile(new AccountsBuilder.Application.Common.AutoMapper.Mappers.ProfitAndLossMapper());
            configuration.AddProfile(new OtherMapper());
            configuration.AddProfile(new NonFinancialDataMapper());
            configuration.AddProfile(new InvolvementMapper());
            configuration.AddProfile(new AddressMapper());
            configuration.AddProfile(new ClientAddressMapper());
            configuration.AddProfile(new AccountsBuilder.Application.Common.AutoMapper.Mappers.BalanceSheetMapper());
            configuration.AddProfile(new ValidationMapper());
            configuration.AddProfile(new NotesMapper());
            configuration.AddProfile(new PracticeDetailsMapper());
            configuration.AddProfile(new FinancialDataCategoryMapper());
            configuration.AddProfile(new FinancialDataDrilldownMapper());
            configuration.AddProfile(new AccountingPoliciesMapper());
            configuration.AddProfile(new ProfitShareMapper());
            configuration.AddProfile(new DataScreensValueMapper());
        }
    }
}
