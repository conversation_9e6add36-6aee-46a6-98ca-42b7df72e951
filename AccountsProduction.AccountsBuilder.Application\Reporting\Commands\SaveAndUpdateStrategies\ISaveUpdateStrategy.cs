﻿using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;

namespace AccountsProduction.AccountsBuilder.Reporting.Application.Commands.SaveAndUpdateStrategies
{
    public interface ISaveUpdateStrategy
    {
        string Name { get; }

        bool IsMatch(string name);

        Domain.ReportingPeriod? ReportingPeriod { get; set; }

        Task SendSaveAndUpdateDataEvents(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken);
    }
}
