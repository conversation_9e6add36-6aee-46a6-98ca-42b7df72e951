﻿using AccountsProduction.AccountsBuilder.Domain.Reporting.Other;
using AccountsProduction.AccountsBuilder.Reporting.Domain.BalanceSheet;
using AccountsProduction.AccountsBuilder.Reporting.Domain.Note;
using AccountsProduction.AccountsBuilder.Reporting.Domain.ProfitAndLoss;

namespace AccountsProduction.AccountsBuilder.Reporting.Domain
{
    public class ReportingPeriod
    {
        public ReportingPeriod()
        {
            NoteAccountingPolicies = new HashSet<NoteAccountingPolicies>();
            NoteBalanceSheet = new HashSet<NoteBalanceSheet>();
            NoteOther = new HashSet<NoteOther>();
            NoteProfitAndLoss = new HashSet<NoteProfitAndLoss>();
            Reports = new HashSet<Reports>();
            Signature = new HashSet<Signature>();
            MultiColumnToken = new HashSet<MultiColumnToken>();
            DplSummaryCalcs = new HashSet<DplSummaryCalcs>();
        }

        public Guid ClientId { get; set; }

        public Guid AccountPeriodId { get; set; }

        public DateTime? ReportingPeriodStartDate { get; set; }

        public DateTime? ReportingPeriodEndDate { get; set; }

        public DateTime? TenantAccountantsReportSignatureDate { get; set; }
        public virtual Client? Client { get; set; }
        public string? EntitySize { get; set; }
        public string? ReportingStandard { get; set; }
        public string? Terminology { get; set; }
        public string? TradingStatus { get; set; }
        public string? DormantStatus { get; set; }
        public string? ChoiceOfStatement { get; set; }
        public string? IndependentReviewType { get; set; }
        public string? ReportVersion { get; set; }
        public string? WatermarkText { get; set; }
        public bool IncludeAccountantsReport { get; set; }
        public string? ReviseType { get; set; }

        public virtual ProfitAndLossCompaniesAct? ProfitAndLossCompaniesAct { get; set; }

        public virtual ProfitAndLossFRS105? ProfitAndLossFRS105 { get; set; }

        public virtual ProfitAndLossNonCorp? ProfitAndLossNonCorp { get; set; }

        public virtual BalanceSheetFRS105? BalanceSheetFRS105 { get; set; }

        public virtual BalanceSheetGeneral? BalanceSheetGeneral { get; set; }

        public virtual BalanceSheetLLP? BalanceSheetLLP { get; set; }

        public virtual ICollection<Signature> Signature { get; set; }

        public virtual ICollection<NoteAccountingPolicies> NoteAccountingPolicies { get; set; }

        public virtual ICollection<NoteBalanceSheet> NoteBalanceSheet { get; set; }

        public virtual ICollection<NoteOther> NoteOther { get; set; }

        public virtual ICollection<NoteProfitAndLoss> NoteProfitAndLoss { get; set; }

        public virtual ICollection<Reports> Reports { get; set; }

        public virtual ICollection<MultiColumnToken> MultiColumnToken { get; set; }

        public virtual ICollection<DplSummaryCalcs> DplSummaryCalcs { get; set; }

        public virtual ICollection<LineItem>? LineItem { get; set; }
    }
}
