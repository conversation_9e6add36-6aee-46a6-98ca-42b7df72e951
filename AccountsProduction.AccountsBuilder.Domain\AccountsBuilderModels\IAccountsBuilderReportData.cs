﻿using AccountsProduction.AccountsBuilder.Domain.FinancialDataModels;

namespace AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels
{
    public interface IAccountsBuilderReportData
    {
        Guid ClientId { get; set; }
        Guid PeriodId { get; set; }
        Guid TenantId { get; set; }
        TrialBalance TrialBalance { get; set; }

        Signatory Signatory { get; set; }

        FinancialData FinancialData { get; set; }

        NonFinancialData NonFinancialData { get; set; }

        InvolvementsData InvolvementsData { get; set; }

        PracticeDetails PracticeDetails { get; set; }


        LicenseData LicenseData { get; set; }

        ReportingStandard ReportingStandard { get; set; }

        EntitySetup EntitySetup { get; set; }
    }
}