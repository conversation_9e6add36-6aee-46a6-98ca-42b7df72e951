﻿using System.Diagnostics.CodeAnalysis;

namespace AccountsProduction.AccountsBuilder.Api.Middleware
{  /// <summary>
   /// Middleware to return required security headers for each response
   /// </summary>
    [ExcludeFromCodeCoverage]
    public class ResponseHeadersMiddleware
    {
        private readonly RequestDelegate _next;

        public ResponseHeadersMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public Task Invoke(HttpContext httpContext)
        {
            httpContext.Response.Headers.TryAdd("Access-Control-Allow-Origin", "*");
            httpContext.Response.Headers.TryAdd("Access-Control-Allow-Credentials", "true");
            httpContext.Response.Headers.TryAdd("Cache-Control", "no-store, no-cache");
            httpContext.Response.Headers.TryAdd("Referer-Policy", "no-referer");
            httpContext.Response.Headers.TryAdd("Strict-Transport-Security", "max-age=********");
            httpContext.Response.Headers.TryAdd("X-Content-Type-Options", "nosniff");
            httpContext.Response.Headers.TryAdd("X-Frame-Options", "deny");
            httpContext.Response.Headers.TryAdd("X-XSS-Protection", "1; mode=block");
            httpContext.Response.Headers.TryAdd("Content-Security-Policy", "default-src \\'none\\';");

            if (httpContext.Request.Headers.TryGetValue("CorrelationId", out var correlationId))
            {
                httpContext.Response.Headers.Add("CorrelationId", correlationId);
            }

            return _next(httpContext);
        }
    }
}
