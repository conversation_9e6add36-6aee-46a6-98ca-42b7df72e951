﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations
{
    public interface IValidationRunnerBuilder
    {
        IList<ValidationRunner> GetValidationRunners(Domain.AccountsBuilderModels.AccountsBuilder accountsBuilder, string reportType);
    }
}