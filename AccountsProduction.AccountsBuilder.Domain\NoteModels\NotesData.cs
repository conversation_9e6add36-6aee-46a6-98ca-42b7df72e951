﻿namespace AccountsProduction.AccountsBuilder.Domain.NoteModels
{
    public class NotesData : INotesData
    {
        public NotesData()
        {
            AverageNumberOfEmployees = new AverageNumberOfEmployees();
            MembersLiabilityText = new MembersLiabilityText();
            AdditionalNote1 = new AdditionalNote();
            AdditionalNote2 = new AdditionalNote();
        }

        public AverageNumberOfEmployees AverageNumberOfEmployees { get; set; }

        public MembersLiabilityText MembersLiabilityText { get; set; }

        public AdditionalNote AdditionalNote1 { get; set; }

        public AdditionalNote AdditionalNote2 { get; set; }

        public string OffBalanceSheetArrangements { get; set; }


        public string GuaranteesAndOtherFinancialCommitments { get; set; }

        public string RelatedPartyTransactions { get; set; }

        public string LoansAndOtherDebtsDueToMembers { get; set; }

        public string ControllingPartyNote { get; set; }

        public string IntangibleAssetsRevaluation { get; set; }

        public OperatingProfitLoss OperatingProfitLoss { get; set; }

        public string AdvancesCreditAndGuaranteesGrantedToDirectors { get; set; }

        public AdvancesCreditAndGuaranteesGrantedToDirectors AdvancesCreditAndGuaranteesGrantedToDirectorsExtended { get; set; }
        public TangibleFixedAssetsNotes TangibleFixedAssetsNotes { get; set; }
    }
}
