﻿using AccountsProduction.AccountsBuilder.Api.Controllers;
using AccountsProduction.AccountsBuilder.Application.Commands.GenerateReport;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.AccountsBuilder;
using AccountsProduction.AccountsBuilder.Application.Queries.AccountsBuilder;
using Iris.AccountsProduction.Common.Toolkit.Filters;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Shouldly;
using Xunit;
using static Iris.AccountsProduction.Common.Toolkit.Utils.Constants;

namespace AccountsProduction.AccountsBuilder.UnitTests.Controllers
{
    public class ReportsControllerTests
    {
        private readonly Mock<IMediator> _mediator;

        public ReportsControllerTests()
        {
            _mediator = new Mock<IMediator>();
        }


        public class Should_check_for_controller_restrictions : ReportsControllerTests
        {
            private readonly ReportsController _reportsController = new ReportsController(new Mock<IMediator>().Object);

            [Fact]
            public void Should_check_for_licenses_types()
            {
                _reportsController.HasAttribute(typeof(APLicensedAttribute)).ShouldBeTrue();
            }

            [Fact]
            public void Should_check_for_feature_toggle()
            {
                _reportsController.HasAttributeWithExpectedArgument(typeof(FeatureAttribute), FeatureToggle.AccountsProduction.Services).ShouldBeTrue();
            }
        }


        [Fact]
        public async Task Should_return_ok_created_when_post_request()
        {
            var reportsController = new ReportsController(_mediator.Object);
            var response = await reportsController.Generate(Guid.NewGuid(), new GenerateReportCommand());

            response.ShouldBeAssignableTo<OkResult>();
        }

        [Fact]
        public async Task Should_return_ok_when_get_request()
        {
            _mediator.Setup(mediator => mediator.Send(It.IsAny<GetReportQuery>(), CancellationToken.None))
                .ReturnsAsync(() => new AccountsBuilderDto());
            var reportsController = new ReportsController(_mediator.Object);
            var response = await reportsController.GetByPeriodId(Guid.NewGuid(), Guid.NewGuid());

            response.ShouldBeAssignableTo<OkObjectResult>();
        }

        [Fact]
        public async Task Should_return_not_found_on_get_request_when_null_response()
        {
            _mediator.Setup(mediator => mediator.Send(It.IsAny<GetReportQuery>(), CancellationToken.None)).ReturnsAsync(() => null);
            var reportsController = new ReportsController(_mediator.Object);
            var response = await reportsController.GetByPeriodId(Guid.NewGuid(), Guid.NewGuid());

            response.ShouldBeAssignableTo<NotFoundResult>();
        }

		[Fact]
		public void Should_check_for_confidential_client_access()
		{
			var reportsController = new ReportsController(_mediator.Object);
			reportsController.HasAttribute(typeof(ConfidentialClientAccessAttribute)).ShouldBeTrue();
		}
	}
}
