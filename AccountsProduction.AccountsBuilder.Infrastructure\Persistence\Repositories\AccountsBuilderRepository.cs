﻿using AccountsProduction.AccountsBuilder.Domain;
using Amazon.DynamoDBv2.DataModel;
using Amazon.Runtime;
using Iris.AccountsProduction.Common.Toolkit.Exceptions;
using Iris.Elements.DynamoDb;
using System.Net;

namespace AccountsProduction.AccountsBuilder.Infrastructure.Persistence.Repositories
{
    public class AccountsBuilderRepository : IAccountsBuilderRepository
    {
        private const string TableName = "accountsproduction-accountsbuilder";
        private readonly IDynamoDbContextWrapper _dbContextWrapper;

        private readonly DynamoDBOperationConfig _dynamoDbOperationConfig = new DynamoDBOperationConfig
        {
            OverrideTableName = TableName
        };

        public AccountsBuilderRepository(IDynamoDbContextWrapper dbContextWrapper)
        {
            _dbContextWrapper = dbContextWrapper;
        }

        public async Task<Domain.AccountsBuilderModels.AccountsBuilder> Get(Guid clientId, Guid periodId, CancellationToken cancellationToken)
        {
            return await ExecuteDBQuery(async () => await _dbContextWrapper.LoadAsync<Domain.AccountsBuilderModels.AccountsBuilder>(clientId, periodId, _dynamoDbOperationConfig, cancellationToken));
        }

        public async Task<List<Domain.AccountsBuilderModels.AccountsBuilder>> GetAll(Guid clientId, CancellationToken cancellationToken)
        {
            return await ExecuteDBQuery(async () => (await _dbContextWrapper.QueryAsync<Domain.AccountsBuilderModels.AccountsBuilder>(clientId, _dynamoDbOperationConfig).GetRemainingAsync(cancellationToken)).ToList());
        }


        public async Task<Domain.AccountsBuilderModels.AccountsBuilder> Get(Guid processId, CancellationToken cancellationToken)
        {
            var queryConfig = new DynamoDBOperationConfig
            {
                OverrideTableName = TableName,
                IndexName = "ProcessId-index"
            };

            return await ExecuteDBQuery(async () => (await _dbContextWrapper.QueryAsync<Domain.AccountsBuilderModels.AccountsBuilder>(processId, queryConfig).GetRemainingAsync(cancellationToken)).FirstOrDefault());
        }
        public async Task<List<Domain.AccountsBuilderModels.AccountsBuilder>> GetByTenantId(Guid tenantId, CancellationToken cancellationToken)
        {
            var accountsBuilders = new List<Domain.AccountsBuilderModels.AccountsBuilder>();
            var queryConfig = new DynamoDBOperationConfig
            {
                OverrideTableName = TableName,
                IndexName = "TenantId-index"
            };

            var result = await ExecuteDBQuery(async () => (await _dbContextWrapper.QueryAsync<Domain.AccountsBuilderModels.AccountsBuilder>(tenantId, queryConfig).GetRemainingAsync(cancellationToken)));

            accountsBuilders.AddRange(result);

            return accountsBuilders;
        }


        public async Task Save(Domain.AccountsBuilderModels.AccountsBuilder entity, CancellationToken cancellationToken)
        {
            await ExecuteDBQuery(async () => await _dbContextWrapper.SaveAsync(entity, _dynamoDbOperationConfig, cancellationToken));
        }

        private async Task ExecuteDBQuery(Func<Task> func)
        {
            await ExecuteDBQuery(async () =>
            {
                await func();
                return Task.CompletedTask;
            });
        }

        private async Task<TResult> ExecuteDBQuery<TResult>(Func<Task<TResult>> func)
        {
            try
            {
                return await func();
            }

            catch (AmazonServiceException serviceException)
            {
                if (serviceException.Message == "Rate of requests exceeds the allowed throughput.")
                {
                    throw new InternalException(serviceException.Message, serviceException);
                }

                if (serviceException.StatusCode == HttpStatusCode.InternalServerError ||
                    serviceException.StatusCode == HttpStatusCode.ServiceUnavailable)
                {
                    throw new InternalException(serviceException.Message);
                }

                throw;
            }
        }
    }
}
