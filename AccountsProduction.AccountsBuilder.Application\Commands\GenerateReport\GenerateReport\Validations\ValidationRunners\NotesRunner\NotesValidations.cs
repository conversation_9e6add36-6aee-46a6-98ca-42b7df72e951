﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.NotesRunner
{
    public static class NotesValidations
    {
        public const string AverageNumberOfEmployeesCurrentPeriod = "AverageNumberOfEmployeesCurrentPeriod";
        public const string AverageNumberOfEmployeesPreviousPeriod = "AverageNumberOfEmployeesPreviousPeriod";
        public const string AdvancesCreditGuarantees = "AdvancesCreditGuarantees";

        public static ValidationRuleConfig AverageNumberOfEmployeesCurrentPeriodConfig(string reportStandardType) =>
            new ValidationRuleConfig
            {
                Breadcrumb = NotesBreadcrumbs.AverageNumberOfEmployeesRule(reportStandardType).ToString(),
                Description = "A note for Average number of employees (current period) has not been entered.",
                Name = AverageNumberOfEmployeesCurrentPeriod,
                Type = ValidationRuleType.Missing,
                Target = Target.IssueLog,
                ErrorCategory = ErrorCategoryType.Mandatory,
                DisplayName = "Average number of employees (current period)",
                ErrorCode = ValidationCodes.AverageNumberOfEmployeesCurrentPeriod
            };

        public static ValidationRuleConfig AverageNumberOfEmployeesPreviousPeriodConfig(string reportStandardType) =>
            new ValidationRuleConfig
            {
                Breadcrumb = NotesBreadcrumbs.AverageNumberOfEmployeesRule(reportStandardType).ToString(),
                Description = "A note for Average number of employees (previous period) has not been entered.",
                Name = AverageNumberOfEmployeesPreviousPeriod,
                Type = ValidationRuleType.Missing,
                Target = Target.IssueLog,
                ErrorCategory = ErrorCategoryType.Mandatory,
                DisplayName = "Average number of employees (previous period)",
                ErrorCode = ValidationCodes.AverageNumberOfEmployeesPreviousPeriod
            };
    }
}