﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations;
using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.LlpDesignatedMemberRunner;
using AccountsProduction.AccountsBuilder.Domain;
using Shouldly;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Application.Commands.Publish.Validations.ValidationRunners
{
    public class LlpDesignatedMemberValidationRunnerTests
    {
        public class When_validating_mandatory_missing_issues : LlpDesignatedMemberValidationRunnerTests
        {
            [Fact]
            public void Should_return_validation_issues_for_empty_active_designated_members_data()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    InvolvementsData = new InvolvementsData
                    {
                        Involvements = new List<Involvement>
                        {
                            new Involvement
                            {
                                InvolvementType = "Designated Member",
                                InvolvedClientType = "business",
                                StartDate = DateTime.Today.AddMonths(-1)
                            },
                            new Involvement
                            {
                                InvolvementType = "Designated Member",
                                InvolvedClientType = "business",
                                StartDate = DateTime.Today.AddMonths(-3),
                                EndDate = DateTime.Today
                            },
                        }
                    },
                    CreatedTimeUtc = DateTime.Today.AddMonths(-3)
                };

                var llpDesignatedMemberValidationRunner = new LlpDesignatedMemberValidationRunner();

                var issues = llpDesignatedMemberValidationRunner.Validate(accountsBuilder);

                issues.Count.ShouldBe(1);
                issues[0].ErrorCode.ShouldBe(ValidationCodes.DesignatedMemberActive);
                issues[0].Name.ShouldBe(LlpDesignatedMemberValidations.ActiveDesignatedMember);
                issues[0].Type.ShouldBe(ValidationRuleType.Invalid);
                issues[0].Target.ShouldBe(Target.IssueLog);
                issues[0].ErrorCategory.ShouldBe(ErrorCategoryType.Mandatory);
                issues[0].DisplayName.ShouldBe("Designated Member");
                issues[0].Breadcrumb.ShouldBe("Client management > Relationships tab");
                issues[0].Description.ShouldBe("A company must have two active designated members at the time of the approval and submission of financial statements.");
            }

            [Fact]
            public void Should_return_validation_issues_for_empty_designated_members_data()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    InvolvementsData = new AccountsProduction.AccountsBuilder.Domain.InvolvementsData
                    {
                        Involvements = new List<Involvement>
                        {
                            new Involvement()
                        }
                    },
                };

                var llpDesignatedMemberValidationRunner = new LlpDesignatedMemberValidationRunner();

                var issues = llpDesignatedMemberValidationRunner.Validate(accountsBuilder);

                issues.Count.ShouldBe(2);
                issues[0].ErrorCode.ShouldBe(ValidationCodes.DesignatedMember);
                issues[0].Name.ShouldBe(LlpDesignatedMemberValidations.DesignatedMember);
                issues[0].Type.ShouldBe(ValidationRuleType.Missing);
                issues[0].Target.ShouldBe(Target.IssueLog);
                issues[0].ErrorCategory.ShouldBe(ErrorCategoryType.Mandatory);
                issues[0].DisplayName.ShouldBe("Designated Member");
                issues[0].Breadcrumb.ShouldBe("Client management > Relationships tab");
                issues[0].Description.ShouldBe("A company must have two designated members to be able to approve and submit financial statements.");
                issues[1].Name.ShouldBe(LlpDesignatedMemberValidations.ActiveDesignatedMember);
                issues[1].ErrorCode.ShouldBe(ValidationCodes.DesignatedMemberActive);
            }

            [Fact]
            public void Should_not_return_validation_issues_llp_designated_members_data()
            {
                var accountsBuilder = new AccountsProduction.AccountsBuilder.Domain.AccountsBuilderModels.AccountsBuilder
                {
                    InvolvementsData = new AccountsProduction.AccountsBuilder.Domain.InvolvementsData
                    {
                        Involvements = new List<Involvement>
                        {
                            new Involvement
                            {
                                InvolvementType = "Designated Member",
                                InvolvedClientType = "business",
                                StartDate = DateTime.Today.AddMonths(-1)
                            },
                            new Involvement
                            {
                                InvolvementType = "Designated Member",
                                InvolvedClientType = "business",
                                StartDate = DateTime.Today.AddMonths(3)
                            },
                            new Involvement
                            {
                                InvolvementType = "Designated Member",
                                InvolvedClientType = "business",
                                StartDate = DateTime.Today.AddMonths(-1)
                            }
                        }
                    },
                    CreatedTimeUtc = DateTime.Today.AddMonths(-3)
                };

                var llpDesignatedMemberValidationRunner = new LlpDesignatedMemberValidationRunner();

                var issues = llpDesignatedMemberValidationRunner.Validate(accountsBuilder);

                issues.ShouldBeEmpty();
            }
        }
    }
}