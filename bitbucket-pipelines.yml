# IMPORTANT
# Please do not remove this file as it's needed for synchronizing BitBucket repositories to AWS CodeCommit
# If you need to modify this file, please consult with the Elements DevOps team first

image: 'atlassian/default-image:latest'
pipelines:
  default:
    - step:
        name: 'Push branch to AWS CodeCommit.'
        script:
          - echo User ${AWS_CODECOMMIT_USER} >> ~/.ssh/config
          - git remote add codecommit ssh://git-codecommit.eu-west-2.amazonaws.com/v1/repos/${BITBUCKET_REPO_SLUG}
          - git push codecommit ${BITBUCKET_BRANCH}
