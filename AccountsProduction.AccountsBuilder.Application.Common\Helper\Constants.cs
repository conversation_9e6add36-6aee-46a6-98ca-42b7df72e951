﻿namespace AccountsProduction.AccountsBuilder.Application.Common.Helper
{
    public static class Constants
    {
        public static class Http
        {
            public static readonly TimeSpan HttpClientTimeOut = new TimeSpan(0, 0, 15);
        }

        public static class Configuration
        {
            public static class Aws
            {
                public const string Region = "AWS_DEFAULT_REGION";
                public const string AccessKey = "AWS_ACCESS_KEY_ID";
                public const string SecretKey = "AWS_SECRET_ACCESS_KEY";
                public const string SessionToken = "AWS_SESSION_TOKEN";
            }
        }

        public static class AccountPeriodsApi
        {
            public const string EntitySetupEndpoint = "/clients/{0}/accountperiods/{1}/entitysetup/awsiam";
            public const string ClientOnboardedEndpoint = "/clients/{0}/onboarded/awsiam";
            public const string ClientEndpoint = "/clients/{0}/awsiam";
            public const string RoundingOptionsEndpoint = "clients/{0}/accountperiods/{1}/roundingoptions/awsiam";
        }

        public static class TrialBalanceApi
        {
            public const string TrialBalanceCalculateEndpoint = "/clients/{0}/accountperiods/{1}/trialbalance/calculate/awsiam";
        }

        public static class AccountsBuilderApi
        {
            public const string ImportDataEndpoint = "reporting/clients/{0}/accountperiods/{1}/awsiam";
        }

        public static class FeatureToggle
        {
            public static class AccountsProduction
            {
                public const string ViewAllReportTemplates = "AccountProduction.Report.ReportTemplates.ViewAll";
            }
        }

    }
}
