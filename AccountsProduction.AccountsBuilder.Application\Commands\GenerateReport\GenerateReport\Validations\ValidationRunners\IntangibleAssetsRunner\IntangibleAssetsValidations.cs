﻿using AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.Breadcrumbs;
using AccountsProduction.AccountsBuilder.Domain;

namespace AccountsProduction.AccountsBuilder.AggregatorFunc.Application.Commands.Publish.Validations.ValidationRunners.IntangibleAssetsRunner
{
    public static class IntangibleAssetsValidations
    {
        public const string IntangibleAssets = "IntangibleAssets";
        public const string IntangibleAssetsGoodwill = "Goodwill";
        public const string IntangibleAssetsPatentsAndLicenses = "Patents & Licenses";
        public const string IntangibleAssetsDevelopmentCosts = "Development Costs";
        public const string IntangibleAssetsComputerSoftware = "Computer Software";

        public static readonly ValidationRuleConfig IntangibleAssetsConfig = new ValidationRuleConfig
        {
            Breadcrumb = IntangibleAssetsBreadcrumbs.IntangibleAssetsRule.ToString(),
            Description = "Intangible assets should be hidden.",
            Name = IntangibleAssets,
            Type = ValidationRuleType.Missing,
            Target = Target.SectionValidation,
            ErrorCategory = ErrorCategoryType.Advisory,
            DisplayName = "IntangibleAssets",
            ErrorCode = ValidationCodes.IntangibleAssets
        };

        public static readonly ValidationRuleConfig GoodwillConfig = new ValidationRuleConfig
        {
            Breadcrumb = IntangibleAssetsBreadcrumbs.IntangibleAssetsGoodwill.ToString(),
            Description = "Goodwill account codes have been populated in the Trial balance. An accounting policy is therefore required to be entered.",
            Name = IntangibleAssetsGoodwill,
            Type = ValidationRuleType.Missing,
            Target = Target.IssueLog,
            ErrorCategory = ErrorCategoryType.Mandatory,
            DisplayName = "Goodwill accounting policy",
            ErrorCode = ValidationCodes.IntangibleAssetsGoodwill
        };

        public static readonly ValidationRuleConfig PatentsAndLicensesConfig = new ValidationRuleConfig
        {
            Breadcrumb = IntangibleAssetsBreadcrumbs.IntangibleAssetsPatentsAndLicenses.ToString(),
            Description = "Patents and licences account codes have been populated in the Trial balance. An accounting policy is therefore required to be entered.",
            Name = IntangibleAssetsPatentsAndLicenses,
            Type = ValidationRuleType.Missing,
            Target = Target.IssueLog,
            ErrorCategory = ErrorCategoryType.Mandatory,
            DisplayName = "Patents and licences accounting policy",
            ErrorCode = ValidationCodes.IntangibleAssetsPatentsAndLicenses
        };

        public static readonly ValidationRuleConfig DevelopmentCostsConfig = new ValidationRuleConfig
        {
            Breadcrumb = IntangibleAssetsBreadcrumbs.IntangibleAssetsDevelopmentCosts.ToString(),
            Description = "Development costs account codes have been populated in the Trial balance. An accounting policy is therefore required to be entered.",
            Name = IntangibleAssetsDevelopmentCosts,
            Type = ValidationRuleType.Missing,
            Target = Target.IssueLog,
            ErrorCategory = ErrorCategoryType.Mandatory,
            DisplayName = "Development costs accounting policy",
            ErrorCode = ValidationCodes.IntangibleAssetsDevelopmentCosts
        };

        public static readonly ValidationRuleConfig ComputerSoftwareConfig = new ValidationRuleConfig
        {
            Breadcrumb = IntangibleAssetsBreadcrumbs.IntangibleAssetsComputerSoftware.ToString(),
            Description = "Computer software account codes have been populated in the Trial balance. An accounting policy is therefore required to be entered.",
            Name = IntangibleAssetsComputerSoftware,
            Type = ValidationRuleType.Missing,
            Target = Target.IssueLog,
            ErrorCategory = ErrorCategoryType.Mandatory,
            DisplayName = "Computer software accounting policy",
            ErrorCode = ValidationCodes.IntangibleAssetsComputerSoftware
        };
    }
}